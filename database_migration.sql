-- 数据库迁移脚本：从旧结构迁移到新结构
-- 执行前请备份数据库！

-- ===================================================================
-- 第一步：备份现有数据
-- ===================================================================
-- 在执行前，请先导出现有数据：
-- mysqldump -u username -p database_name > backup_before_migration.sql

-- ===================================================================
-- 第二步：创建新表结构
-- ===================================================================

-- 创建新的license_devices表
CREATE TABLE IF NOT EXISTS `license_devices` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `license_id` int(11) NOT NULL COMMENT '激活码ID',
  `device_uid` varchar(64) NOT NULL COMMENT '设备唯一标识',
  `status` enum('active','disabled') NOT NULL DEFAULT 'active' COMMENT '设备状态',
  `activated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
  `last_verify_at` datetime DEFAULT NULL COMMENT '最后验证时间',
  `last_ip` varchar(45) DEFAULT NULL COMMENT '最后验证IP',
  `verify_count` int(11) NOT NULL DEFAULT 0 COMMENT '验证次数',
  PRIMARY KEY (`id`),
  UNIQUE KEY `license_device_unique` (`license_id`, `device_uid`),
  KEY `device_uid` (`device_uid`),
  KEY `idx_license_status` (`license_id`, `status`),
  CONSTRAINT `license_devices_ibfk_1` FOREIGN KEY (`license_id`) REFERENCES `licenses` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备绑定表';

-- 创建Skeys记录表
CREATE TABLE IF NOT EXISTS `skeys_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `license_key` varchar(50) NOT NULL COMMENT '激活码',
  `device_uid` varchar(64) NOT NULL COMMENT '设备UID',
  `skeys` text NOT NULL COMMENT '生成的Skeys',
  `generated_at` datetime NOT NULL COMMENT '生成时间',
  `expires_at` datetime NOT NULL COMMENT '过期时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `license_device_unique` (`license_key`, `device_uid`),
  KEY `skeys_search` (`skeys`(100))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Skeys记录表';

-- 创建API频率限制表
CREATE TABLE IF NOT EXISTS `api_rate_limits` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ip_address` varchar(45) NOT NULL COMMENT 'IP地址',
  `endpoint` varchar(100) NOT NULL COMMENT '端点名称',
  `requests` int(11) NOT NULL DEFAULT 1 COMMENT '请求次数',
  `window_start` datetime NOT NULL COMMENT '时间窗口开始',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ip_endpoint_window` (`ip_address`, `endpoint`, `window_start`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API频率限制表';

-- 创建错误日志表
CREATE TABLE IF NOT EXISTS `error_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `level` varchar(20) NOT NULL COMMENT '错误级别',
  `message` text NOT NULL COMMENT '错误消息',
  `file` varchar(255) DEFAULT NULL COMMENT '文件路径',
  `line` int(11) DEFAULT NULL COMMENT '行号',
  `context` text DEFAULT NULL COMMENT '上下文信息',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_level_created` (`level`, `created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统错误日志表';

-- ===================================================================
-- 第三步：修改现有表结构
-- ===================================================================

-- 修改licenses表，添加新字段
ALTER TABLE `licenses`
ADD COLUMN `bound_devices` int(11) NOT NULL DEFAULT 0 COMMENT '当前绑定设备数量' AFTER `notes`,
ADD COLUMN `max_devices` int(11) NOT NULL DEFAULT 1 COMMENT '最大可绑定设备数量' AFTER `bound_devices`;

-- 修改logs表，调整字段类型
ALTER TABLE `logs` 
MODIFY COLUMN `device_uid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
MODIFY COLUMN `log_type` enum('activate','verify') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作类型';

-- 修改plans表，添加max_devices字段
ALTER TABLE `plans`
ADD COLUMN `max_devices` int(11) NOT NULL DEFAULT 1 COMMENT '最大可绑定设备数量' AFTER `is_active`;

-- ===================================================================
-- 第四步：迁移现有数据
-- ===================================================================

-- 迁移已绑定的设备到license_devices表
INSERT INTO `license_devices` (`license_id`, `device_uid`, `status`, `activated_at`, `last_ip`)
SELECT
    `id` as license_id,
    `device_uid`,
    CASE
        WHEN `status` = 'active' THEN 'active'
        ELSE 'disabled'
    END as status,
    COALESCE(`activated_at`, `created_at`) as activated_at,
    `last_ip`
FROM `licenses`
WHERE `device_uid` IS NOT NULL AND `device_uid` != '';

-- 更新licenses表的bound_devices计数
UPDATE `licenses` l
SET `bound_devices` = (
    SELECT COUNT(*)
    FROM `license_devices` ld
    WHERE ld.license_id = l.id AND ld.status = 'active'
);

-- 更新verify_count（从logs表统计）
UPDATE `license_devices` ld
SET `verify_count` = (
    SELECT COUNT(*) 
    FROM `logs` l 
    WHERE l.device_uid = ld.device_uid 
    AND l.log_type = 'verify' 
    AND l.status = 'SUCCESS'
);

-- 更新last_verify_at（从logs表获取最后验证时间）
UPDATE `license_devices` ld
SET `last_verify_at` = (
    SELECT MAX(l.log_time) 
    FROM `logs` l 
    WHERE l.device_uid = ld.device_uid 
    AND l.log_type = 'verify' 
    AND l.status = 'SUCCESS'
);

-- ===================================================================
-- 第五步：添加索引优化
-- ===================================================================

-- 添加性能优化索引
CREATE INDEX IF NOT EXISTS idx_licenses_status_plan ON licenses(status, plan_id);
CREATE INDEX IF NOT EXISTS idx_licenses_expires ON licenses(expires_at);
CREATE INDEX IF NOT EXISTS idx_logs_time_type ON logs(log_time, log_type);
CREATE INDEX IF NOT EXISTS idx_logs_license_device ON logs(license_key, device_uid);

-- ===================================================================
-- 第六步：验证迁移结果
-- ===================================================================

-- 检查迁移结果
SELECT 
    '原licenses表中有设备的记录' as description,
    COUNT(*) as count
FROM licenses 
WHERE device_uid IS NOT NULL AND device_uid != ''

UNION ALL

SELECT 
    '新license_devices表记录数' as description,
    COUNT(*) as count
FROM license_devices

UNION ALL

SELECT 
    '表结构检查' as description,
    COUNT(*) as count
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_name IN ('licenses', 'license_devices', 'logs', 'plans', 'skeys_records');

-- ===================================================================
-- 第七步：清理（可选，建议先测试）
-- ===================================================================

-- 注意：以下操作会删除旧字段，请确保新系统工作正常后再执行

-- 清理licenses表中的旧字段（可选）
-- ALTER TABLE `licenses` DROP COLUMN `device_uid`;
-- ALTER TABLE `licenses` DROP COLUMN `last_ip`;

-- 显示迁移完成信息
SELECT 'Database migration completed successfully!' as Status;
