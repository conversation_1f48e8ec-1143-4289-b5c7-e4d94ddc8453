<?php
/**
 * 数据库自动备份脚本
 * 可以通过cron定时执行
 */

require_once __DIR__ . '/../includes/db.php';
require_once __DIR__ . '/../includes/config.php';

class DatabaseBackup {
    private $pdo;
    private $config;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->config = Config::load();
    }
    
    /**
     * 执行备份
     */
    public function backup() {
        if (!$this->config['database']['backup_enabled']) {
            echo "备份功能已禁用\n";
            return false;
        }
        
        $backup_dir = __DIR__ . '/../backups';
        if (!is_dir($backup_dir)) {
            mkdir($backup_dir, 0755, true);
        }
        
        $timestamp = date('Y-m-d_H-i-s');
        $backup_file = $backup_dir . "/backup_$timestamp.sql";
        
        try {
            // 获取数据库配置
            global $db_host, $db_name, $db_user, $db_pass;
            
            // 构建mysqldump命令
            $command = sprintf(
                'mysqldump -h%s -u%s -p%s %s > %s',
                escapeshellarg($db_host),
                escapeshellarg($db_user),
                escapeshellarg($db_pass),
                escapeshellarg($db_name),
                escapeshellarg($backup_file)
            );
            
            // 执行备份
            $output = [];
            $return_code = 0;
            exec($command, $output, $return_code);
            
            if ($return_code === 0 && file_exists($backup_file)) {
                echo "备份成功: $backup_file\n";
                echo "文件大小: " . $this->formatBytes(filesize($backup_file)) . "\n";
                
                // 清理旧备份
                $this->cleanupOldBackups($backup_dir);
                
                return true;
            } else {
                echo "备份失败，返回码: $return_code\n";
                return false;
            }
            
        } catch (Exception $e) {
            echo "备份异常: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * 清理旧备份文件
     */
    private function cleanupOldBackups($backup_dir) {
        $keep_days = 30; // 保留30天的备份
        $cutoff_time = time() - ($keep_days * 24 * 3600);
        
        $files = glob($backup_dir . '/backup_*.sql');
        $deleted_count = 0;
        
        foreach ($files as $file) {
            if (filemtime($file) < $cutoff_time) {
                if (unlink($file)) {
                    $deleted_count++;
                }
            }
        }
        
        if ($deleted_count > 0) {
            echo "清理了 $deleted_count 个旧备份文件\n";
        }
    }
    
    /**
     * 格式化文件大小
     */
    private function formatBytes($size, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $base = log($size, 1024);
        return round(pow(1024, $base - floor($base)), $precision) . ' ' . $units[floor($base)];
    }
    
    /**
     * 验证备份文件
     */
    public function verifyBackup($backup_file) {
        if (!file_exists($backup_file)) {
            return false;
        }
        
        // 检查文件大小
        if (filesize($backup_file) < 1024) {
            return false;
        }
        
        // 检查文件内容
        $content = file_get_contents($backup_file, false, null, 0, 1024);
        return strpos($content, 'mysqldump') !== false;
    }
    
    /**
     * 获取备份列表
     */
    public function getBackupList() {
        $backup_dir = __DIR__ . '/../backups';
        $files = glob($backup_dir . '/backup_*.sql');
        
        $backups = [];
        foreach ($files as $file) {
            $backups[] = [
                'file' => basename($file),
                'path' => $file,
                'size' => filesize($file),
                'date' => date('Y-m-d H:i:s', filemtime($file)),
                'valid' => $this->verifyBackup($file)
            ];
        }
        
        // 按日期倒序排列
        usort($backups, function($a, $b) {
            return filemtime($b['path']) - filemtime($a['path']);
        });
        
        return $backups;
    }
}

// 如果直接运行此脚本，执行备份
if (php_sapi_name() === 'cli' && basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    echo "开始数据库备份...\n";
    $backup = new DatabaseBackup($pdo);
    $success = $backup->backup();
    
    if ($success) {
        echo "备份完成\n";
        exit(0);
    } else {
        echo "备份失败\n";
        exit(1);
    }
}
?>
