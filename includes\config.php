<?php
/**
 * 配置管理器
 * 提供统一的配置访问接口
 */

class Config {
    private static $config = null;
    
    /**
     * 加载配置文件
     */
    public static function load() {
        if (self::$config === null) {
            $config_file = __DIR__ . '/../config/app.php';
            if (file_exists($config_file)) {
                self::$config = require $config_file;
            } else {
                self::$config = self::getDefaultConfig();
            }
        }
        return self::$config;
    }
    
    /**
     * 获取配置值
     * @param string $key 配置键，支持点号分隔的嵌套键
     * @param mixed $default 默认值
     * @return mixed
     */
    public static function get($key, $default = null) {
        $config = self::load();
        $keys = explode('.', $key);
        $value = $config;
        
        foreach ($keys as $k) {
            if (is_array($value) && isset($value[$k])) {
                $value = $value[$k];
            } else {
                return $default;
            }
        }
        
        return $value;
    }
    
    /**
     * 设置配置值
     * @param string $key 配置键
     * @param mixed $value 配置值
     */
    public static function set($key, $value) {
        $config = self::load();
        $keys = explode('.', $key);
        $current = &$config;
        
        foreach ($keys as $k) {
            if (!isset($current[$k]) || !is_array($current[$k])) {
                $current[$k] = [];
            }
            $current = &$current[$k];
        }
        
        $current = $value;
        self::$config = $config;
    }
    
    /**
     * 检查配置键是否存在
     * @param string $key 配置键
     * @return bool
     */
    public static function has($key) {
        return self::get($key) !== null;
    }
    
    /**
     * 获取默认配置
     */
    private static function getDefaultConfig() {
        return [
            'app_name' => 'PPMT 激活码管理系统',
            'version' => '2.0.0',
            'license' => [
                'max_devices_per_license' => 100,
                'max_licenses_per_batch' => 1000,
            ],
            'api' => [
                'rate_limit' => 100,
                'rate_window' => 60,
            ],
            'ui' => [
                'default_page_size' => 100,
                'date_format' => 'Y-m-d H:i:s',
            ],
        ];
    }
}

// 便捷函数
function config($key, $default = null) {
    return Config::get($key, $default);
}
?>
