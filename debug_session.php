<?php
require_once 'includes/session_helper.php';

echo "<h2>Session 调试信息</h2>";

echo "<h3>Session 状态</h3>";
echo "<p>Session ID: " . session_id() . "</p>";
echo "<p>Session 状态: " . session_status() . "</p>";
echo "<p>Session 保存路径: " . session_save_path() . "</p>";

echo "<h3>Session 数据</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h3>服务器信息</h3>";
echo "<p>PHP 版本: " . PHP_VERSION . "</p>";
echo "<p>当前时间: " . date('Y-m-d H:i:s') . "</p>";
echo "<p>脚本路径: " . __FILE__ . "</p>";

echo "<h3>测试登录状态</h3>";
if (isset($_SESSION['loggedin']) && $_SESSION['loggedin'] === true) {
    echo "<p style='color: green;'>✓ 已登录</p>";
} else {
    echo "<p style='color: red;'>✗ 未登录</p>";
}

echo "<h3>测试操作</h3>";
echo "<a href='?action=login'>模拟登录</a> | ";
echo "<a href='?action=logout'>模拟登出</a> | ";
echo "<a href='?action=refresh'>刷新页面</a>";

if ($_GET['action'] ?? '' === 'login') {
    $_SESSION['loggedin'] = true;
    echo "<p style='color: green;'>已设置登录状态</p>";
}

if ($_GET['action'] ?? '' === 'logout') {
    unset($_SESSION['loggedin']);
    echo "<p style='color: red;'>已清除登录状态</p>";
}

echo "<h3>AJAX 测试</h3>";
echo "<button onclick='testAjax()'>测试AJAX请求</button>";
echo "<div id='ajax-result'></div>";

?>

<script src="assets/plugins/jquery/jquery.min.js"></script>
<script>
function testAjax() {
    $.post('actions/update_license.php', {
        action: 'unbind_device',
        device_id: 999
    }).done(function(response) {
        $('#ajax-result').html('<h4>AJAX 成功响应:</h4><pre>' + JSON.stringify(response, null, 2) + '</pre>');
    }).fail(function(xhr, status, error) {
        $('#ajax-result').html('<h4>AJAX 失败响应:</h4><pre>' + xhr.responseText + '</pre>');
    });
}
</script>
