<?php
require_once '../includes/check_login.php';
require_once '../includes/db.php';

// --- 获取并处理表单数据 ---
$plan_id = (int)($_POST['plan_id'] ?? 0);
$count = (int)($_POST['count'] ?? 1);
$max_devices = (int)($_POST['max_devices'] ?? 1);
$notes = $_POST['notes'] ?? '';

// ★★★ 新增：获取并清理前缀 ★★★
// 使用正则表达式只保留字母、数字、下划线和连字符，防止恶意输入
$prefix = preg_replace('/[^a-zA-Z0-9_-]/', '', $_POST['prefix'] ?? '');

// 验证设备数量
if ($max_devices < 1 || $max_devices > 100) {
    die('错误：设备数量必须在 1 到 100 之间。');
}

// 验证套餐ID是否存在并获取套餐信息
$stmt_check = $pdo->prepare("SELECT id, plan_name FROM plans WHERE id = ? AND is_active = 1");
$stmt_check->execute([$plan_id]);
$plan_info = $stmt_check->fetch();
if (!$plan_info) {
    die('错误：无效的套餐ID或套餐已被禁用。');
}

// 限制一次生成的数量，防止滥用
if ($count > 0 && $count <= 1000) {
    $pdo->beginTransaction(); // 使用事务，确保要么全部成功，要么全部失败
    $generated_keys = []; // 存储生成的激活码信息

    try {
        $stmt = $pdo->prepare("INSERT INTO licenses (license_key, plan_id, max_devices, notes) VALUES (?, ?, ?, ?)");

        for ($i = 0; $i < $count; $i++) {
            // ★★★ 新增：生成带前缀的激活码 ★★★
            // 1. 先生成一个足够长的随机部分
            $random_part = strtoupper(bin2hex(random_bytes(16))); // 使用更安全的随机字节生成器

            // 2. 拼接前缀和随机部分，并确保总长度不超过数据库字段限制 (varchar(50))
            $full_key = $prefix . $random_part;
            $key = substr($full_key, 0, 50);

            // 可以在这里增加一个循环来检查key是否已存在，虽然概率极低
            // while(key_exists($key)) { ... 重新生成 ... }

            $stmt->execute([$key, $plan_id, $max_devices, $notes]);

            // 保存生成的激活码信息
            $generated_keys[] = [
                'key' => $key,
                'plan_name' => $plan_info['plan_name'],
                'max_devices' => $max_devices,
                'notes' => $notes
            ];
        }
        $pdo->commit(); // 提交事务

        // 生成成功，返回JSON数据用于弹窗显示
        if (isset($_POST['ajax']) && $_POST['ajax'] == '1') {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'count' => $count,
                'keys' => $generated_keys,
                'plan_name' => $plan_info['plan_name']
            ]);
            exit();
        }

    } catch (Exception $e) {
        $pdo->rollBack(); // 如果出错，则回滚所有操作
        if (isset($_POST['ajax']) && $_POST['ajax'] == '1') {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
            exit();
        }
        die("生成失败，发生错误: " . $e->getMessage());
    }
} else {
    if (isset($_POST['ajax']) && $_POST['ajax'] == '1') {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'error' => '生成数量必须在 1 到 1000 之间'
        ]);
        exit();
    }
    die("错误：生成数量必须在 1 到 1000 之间。");
}

header('Location: ../index.php');
exit();
?>