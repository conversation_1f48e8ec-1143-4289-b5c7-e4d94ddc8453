.table td, .table th { vertical-align: middle !important; }
.badge { font-size: 0.8em; padding: 0.4em 0.6em; }
.small-box { color: #fff !important; }
.small-box .icon > i { font-size: 70px; top: 20px; }

/* 表头筛选样式 */
.filterable-header {
    position: relative;
    cursor: pointer;
    user-select: none;
}

.filterable-header:hover {
    background-color: rgba(0,0,0,0.05);
}

.filterable-header.filtered {
    background-color: rgba(0,123,255,0.1);
}

.filter-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    min-width: 200px;
    display: none !important; /* 默认隐藏 */
}

.filter-dropdown.show {
    display: block !important; /* 显示时覆盖隐藏 */
}

.filter-dropdown .dropdown-menu {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    background-color: #fff;
    position: static; /* 避免嵌套定位问题 */
    float: none;
    width: 100%;
    margin: 0;
}

.filter-option {
    cursor: pointer;
}

.filter-option:hover {
    background-color: #f8f9fa;
}

/* 设备列表样式 */
.device-list {
    max-height: 120px;
    overflow-y: auto;
}

.device-item {
    padding: 2px 0;
    border-bottom: 1px solid #eee;
}

.device-item:last-child {
    border-bottom: none;
}