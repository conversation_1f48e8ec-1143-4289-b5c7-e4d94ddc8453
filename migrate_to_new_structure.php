<?php
/**
 * 数据库迁移脚本：从老结构迁移到新结构
 * 使用方法：上传到服务器后访问此文件执行迁移
 */

require_once 'includes/db.php';

echo "<h2>数据库结构迁移</h2>";
echo "<p><strong>警告：请确保已备份数据库！</strong></p>";

// 检查是否已经是新结构
try {
    $pdo->query("SELECT 1 FROM license_devices LIMIT 1");
    echo "<p style='color: orange;'>检测到license_devices表已存在，可能已经迁移过了。</p>";
} catch (Exception $e) {
    echo "<p>license_devices表不存在，需要创建。</p>";
}

if (isset($_POST['confirm_migrate']) && $_POST['confirm_migrate'] === 'yes') {
    try {
        $pdo->beginTransaction();
        
        echo "<h3>开始迁移...</h3>";
        
        // 第一步：创建license_devices表
        echo "<p>1. 创建license_devices表...</p>";
        $create_table_sql = "
        CREATE TABLE IF NOT EXISTS `license_devices` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `license_id` int(11) NOT NULL COMMENT '激活码ID',
          `device_uid` varchar(64) NOT NULL COMMENT '设备唯一标识',
          `status` enum('active','disabled') NOT NULL DEFAULT 'active' COMMENT '设备状态',
          `activated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
          `last_verify_at` datetime DEFAULT NULL COMMENT '最后验证时间',
          `last_ip` varchar(45) DEFAULT NULL COMMENT '最后验证IP',
          `verify_count` int(11) NOT NULL DEFAULT 0 COMMENT '验证次数',
          PRIMARY KEY (`id`),
          UNIQUE KEY `license_device_unique` (`license_id`, `device_uid`),
          KEY `device_uid` (`device_uid`),
          KEY `idx_license_status` (`license_id`, `status`),
          CONSTRAINT `license_devices_ibfk_1` FOREIGN KEY (`license_id`) REFERENCES `licenses` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备绑定表'";
        
        $pdo->exec($create_table_sql);
        echo "<p style='color: green;'>✓ license_devices表创建成功</p>";
        
        // 第二步：检查并添加新字段到licenses表
        echo "<p>2. 检查licenses表字段...</p>";
        
        // 检查bound_devices字段
        try {
            $pdo->query("SELECT bound_devices FROM licenses LIMIT 1");
            echo "<p>bound_devices字段已存在</p>";
        } catch (Exception $e) {
            $pdo->exec("ALTER TABLE `licenses` ADD COLUMN `bound_devices` int(11) NOT NULL DEFAULT 0 COMMENT '当前绑定设备数量' AFTER `notes`");
            echo "<p style='color: green;'>✓ 添加bound_devices字段</p>";
        }
        
        // 检查max_devices字段
        try {
            $pdo->query("SELECT max_devices FROM licenses LIMIT 1");
            echo "<p>max_devices字段已存在</p>";
        } catch (Exception $e) {
            $pdo->exec("ALTER TABLE `licenses` ADD COLUMN `max_devices` int(11) NOT NULL DEFAULT 1 COMMENT '最大可绑定设备数量' AFTER `bound_devices`");
            echo "<p style='color: green;'>✓ 添加max_devices字段</p>";
        }
        
        // 第三步：迁移现有数据
        echo "<p>3. 迁移现有绑定数据...</p>";
        
        // 查找所有已绑定设备的激活码
        $bound_licenses = $pdo->query("
            SELECT id, device_uid, activated_at, last_ip 
            FROM licenses 
            WHERE device_uid IS NOT NULL AND device_uid != ''
        ")->fetchAll();
        
        $migrated_count = 0;
        foreach ($bound_licenses as $license) {
            // 插入到license_devices表
            $insert_stmt = $pdo->prepare("
                INSERT INTO license_devices (license_id, device_uid, activated_at, last_ip, verify_count) 
                VALUES (?, ?, ?, ?, 1)
                ON DUPLICATE KEY UPDATE 
                activated_at = VALUES(activated_at), 
                last_ip = VALUES(last_ip)
            ");
            
            $insert_stmt->execute([
                $license['id'],
                $license['device_uid'],
                $license['activated_at'] ?? date('Y-m-d H:i:s'),
                $license['last_ip']
            ]);
            
            // 更新licenses表的bound_devices计数
            $update_stmt = $pdo->prepare("UPDATE licenses SET bound_devices = 1 WHERE id = ?");
            $update_stmt->execute([$license['id']]);
            
            $migrated_count++;
        }
        
        echo "<p style='color: green;'>✓ 迁移了 $migrated_count 个设备绑定记录</p>";
        
        // 第四步：更新未绑定激活码的计数
        echo "<p>4. 更新未绑定激活码的计数...</p>";
        $pdo->exec("UPDATE licenses SET bound_devices = 0 WHERE device_uid IS NULL OR device_uid = ''");
        echo "<p style='color: green;'>✓ 更新完成</p>";
        
        $pdo->commit();
        
        echo "<h3 style='color: green;'>迁移完成！</h3>";
        echo "<p>现在您可以上传新版本的代码文件了。</p>";
        echo "<p><strong>建议：</strong>迁移完成后，可以保留原有的device_uid字段作为备份，但新系统将使用license_devices表。</p>";
        
        // 显示迁移后的统计
        echo "<h3>迁移后统计</h3>";
        $total_licenses = $pdo->query("SELECT COUNT(*) FROM licenses")->fetchColumn();
        $total_devices = $pdo->query("SELECT COUNT(*) FROM license_devices")->fetchColumn();
        $bound_licenses_count = $pdo->query("SELECT COUNT(*) FROM licenses WHERE bound_devices > 0")->fetchColumn();
        
        echo "<p>总激活码数：$total_licenses</p>";
        echo "<p>总设备绑定记录：$total_devices</p>";
        echo "<p>已绑定设备的激活码：$bound_licenses_count</p>";
        
    } catch (Exception $e) {
        $pdo->rollBack();
        echo "<p style='color: red;'>迁移失败：" . $e->getMessage() . "</p>";
        echo "<p>数据库已回滚到迁移前状态。</p>";
    }
} else {
    // 显示确认表单
    echo "<h3>迁移前检查</h3>";
    
    try {
        $license_count = $pdo->query("SELECT COUNT(*) FROM licenses")->fetchColumn();
        $bound_count = $pdo->query("SELECT COUNT(*) FROM licenses WHERE device_uid IS NOT NULL AND device_uid != ''")->fetchColumn();
        
        echo "<p>当前激活码总数：$license_count</p>";
        echo "<p>已绑定设备的激活码：$bound_count</p>";
        
        echo "<h3>迁移说明</h3>";
        echo "<ul>";
        echo "<li>创建license_devices表用于多设备绑定</li>";
        echo "<li>为licenses表添加bound_devices和max_devices字段</li>";
        echo "<li>将现有的设备绑定数据迁移到新表</li>";
        echo "<li>保留原有数据，不会丢失任何信息</li>";
        echo "</ul>";
        
        echo "<form method='post'>";
        echo "<p><input type='checkbox' id='backup_confirm' required> <label for='backup_confirm'>我已经备份了数据库</label></p>";
        echo "<p><input type='checkbox' id='understand_confirm' required> <label for='understand_confirm'>我理解此操作将修改数据库结构</label></p>";
        echo "<input type='hidden' name='confirm_migrate' value='yes'>";
        echo "<button type='submit' style='background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 4px;'>确认执行迁移</button>";
        echo "</form>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>检查数据库时出错：" . $e->getMessage() . "</p>";
    }
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
th { background-color: #f2f2f2; }
</style>
