<?php
require_once 'includes/check_login.php';
require_once 'includes/db.php';
require_once 'includes/config.php';
require_once 'includes/header.php';
require_once 'includes/sidebar.php';

// 获取系统状态信息
function getSystemStatus($pdo) {
    $status = [];
    
    // 数据库状态
    try {
        $stmt = $pdo->query("SELECT VERSION() as version");
        $status['database'] = [
            'status' => 'healthy',
            'version' => $stmt->fetchColumn(),
            'connection_time' => microtime(true)
        ];
    } catch (Exception $e) {
        $status['database'] = [
            'status' => 'error',
            'error' => $e->getMessage()
        ];
    }
    
    // 表状态 - 监控所有表
    $tables = ['licenses', 'logs', 'plans', 'license_devices', 'skeys_records'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $status['tables'][$table] = [
                'status' => 'healthy',
                'count' => $stmt->fetchColumn()
            ];
        } catch (Exception $e) {
            $status['tables'][$table] = [
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }
    
    // 磁盘空间
    $status['disk'] = [
        'free' => disk_free_space('.'),
        'total' => disk_total_space('.'),
        'used_percent' => round((1 - disk_free_space('.') / disk_total_space('.')) * 100, 2)
    ];
    
    // PHP状态
    $status['php'] = [
        'version' => PHP_VERSION,
        'memory_limit' => ini_get('memory_limit'),
        'memory_usage' => memory_get_usage(true),
        'memory_peak' => memory_get_peak_usage(true)
    ];
    
    return $status;
}

// 获取性能统计
function getPerformanceStats($pdo) {
    $stats = [];
    
    // 今日API调用统计
    $today = date('Y-m-d');
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_requests,
            COUNT(CASE WHEN status = 'SUCCESS' THEN 1 END) as successful_requests,
            COUNT(CASE WHEN log_type = 'activate' THEN 1 END) as activate_requests,
            COUNT(CASE WHEN log_type = 'verify' THEN 1 END) as verify_requests
        FROM logs 
        WHERE DATE(log_time) = ?
    ");
    $stmt->execute([$today]);
    $stats['today'] = $stmt->fetch();
    
    // 最近7天统计
    $stmt = $pdo->query("
        SELECT 
            DATE(log_time) as date,
            COUNT(*) as requests,
            COUNT(CASE WHEN status = 'SUCCESS' THEN 1 END) as successful
        FROM logs 
        WHERE log_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        GROUP BY DATE(log_time)
        ORDER BY date DESC
    ");
    $stats['weekly'] = $stmt->fetchAll();
    
    // 热门IP地址
    $stmt = $pdo->query("
        SELECT
            ip_address,
            COUNT(*) as requests,
            COUNT(CASE WHEN status = 'SUCCESS' THEN 1 END) as successful
        FROM logs
        WHERE log_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        GROUP BY ip_address
        ORDER BY requests DESC
        LIMIT 10
    ");
    $stats['top_ips'] = $stmt->fetchAll();

    // 错误统计
    $stmt = $pdo->query("
        SELECT
            status,
            COUNT(*) as count
        FROM logs
        WHERE log_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR) AND status != 'SUCCESS'
        GROUP BY status
        ORDER BY count DESC
        LIMIT 10
    ");
    $stats['errors'] = $stmt->fetchAll();

    // 系统错误日志（如果存在error_logs表）
    try {
        $stmt = $pdo->query("
            SELECT
                level,
                message,
                file,
                line,
                created_at
            FROM error_logs
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            ORDER BY created_at DESC
            LIMIT 20
        ");
        $stats['system_errors'] = $stmt->fetchAll();
    } catch (Exception $e) {
        $stats['system_errors'] = [];
    }

    return $stats;
}

$system_status = getSystemStatus($pdo);
$performance_stats = getPerformanceStats($pdo);
?>

<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <h1 class="m-0">系统监控</h1>
        </div>
    </section>
    
    <section class="content">
        <div class="container-fluid">
            
            <!-- 系统状态概览 -->
            <div class="row">
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-<?= $system_status['database']['status'] === 'healthy' ? 'success' : 'danger' ?>">
                            <i class="fas fa-database"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">数据库状态</span>
                            <span class="info-box-number"><?= $system_status['database']['status'] === 'healthy' ? '正常' : '异常' ?></span>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-info">
                            <i class="fas fa-hdd"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">磁盘使用率</span>
                            <span class="info-box-number"><?= $system_status['disk']['used_percent'] ?>%</span>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-warning">
                            <i class="fas fa-memory"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">内存使用</span>
                            <span class="info-box-number"><?= round($system_status['php']['memory_usage'] / 1024 / 1024, 1) ?>MB</span>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-primary">
                            <i class="fas fa-chart-line"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">今日请求</span>
                            <span class="info-box-number"><?= $performance_stats['today']['total_requests'] ?></span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 详细信息 -->
            <div class="row">
                <!-- 数据库表状态 -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">数据库表状态</h3>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <thead>
                                    <tr><th>表名</th><th>记录数</th><th>状态</th></tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($system_status['tables'] as $table => $info): ?>
                                    <tr>
                                        <td><?= $table ?></td>
                                        <td><?= $info['status'] === 'healthy' ? number_format($info['count']) : '-' ?></td>
                                        <td>
                                            <span class="badge bg-<?= $info['status'] === 'healthy' ? 'success' : 'danger' ?>">
                                                <?= $info['status'] === 'healthy' ? '正常' : '异常' ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- 热门IP地址 -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">24小时热门IP</h3>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <thead>
                                    <tr><th>IP地址</th><th>请求数</th><th>成功率</th></tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($performance_stats['top_ips'] as $ip_stat): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($ip_stat['ip_address']) ?></td>
                                        <td><?= $ip_stat['requests'] ?></td>
                                        <td><?= round($ip_stat['successful'] / $ip_stat['requests'] * 100, 1) ?>%</td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 错误统计和7天趋势 -->
            <div class="row">
                <!-- 24小时错误统计 -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">24小时错误统计</h3>
                        </div>
                        <div class="card-body">
                            <?php if (empty($performance_stats['errors'])): ?>
                                <p class="text-muted">暂无错误记录</p>
                            <?php else: ?>
                                <table class="table table-sm">
                                    <thead>
                                        <tr><th>错误类型</th><th>次数</th></tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($performance_stats['errors'] as $error): ?>
                                        <tr>
                                            <td><?= htmlspecialchars($error['status']) ?></td>
                                            <td><span class="badge bg-danger"><?= $error['count'] ?></span></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- 系统错误日志 -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">系统错误日志</h3>
                        </div>
                        <div class="card-body">
                            <?php if (empty($performance_stats['system_errors'])): ?>
                                <p class="text-muted">暂无系统错误</p>
                            <?php else: ?>
                                <div style="max-height: 300px; overflow-y: auto;">
                                    <?php foreach ($performance_stats['system_errors'] as $error): ?>
                                    <div class="alert alert-<?= $error['level'] === 'ERROR' ? 'danger' : 'warning' ?> alert-sm">
                                        <strong><?= $error['level'] ?>:</strong> <?= htmlspecialchars($error['message']) ?>
                                        <br><small><?= $error['file'] ?>:<?= $error['line'] ?> - <?= $error['created_at'] ?></small>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 7天趋势 -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">最近7天请求趋势</h3>
                        </div>
                        <div class="card-body">
                            <table class="table">
                                <thead>
                                    <tr><th>日期</th><th>总请求</th><th>成功请求</th><th>成功率</th></tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($performance_stats['weekly'] as $day_stat): ?>
                                    <tr>
                                        <td><?= $day_stat['date'] ?></td>
                                        <td><?= number_format($day_stat['requests']) ?></td>
                                        <td><?= number_format($day_stat['successful']) ?></td>
                                        <td><?= $day_stat['requests'] > 0 ? round($day_stat['successful'] / $day_stat['requests'] * 100, 1) : 0 ?>%</td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
        </div>
    </section>
</div>

<?php require_once 'includes/footer.php'; ?>
