<?php
// --- 请确保信息正确 ---
$db_host = 'localhost';
$db_name = 'z<PERSON><PERSON><PERSON><PERSON>';
$db_user = 'root';
$db_pass = '123';
// --------------------
date_default_timezone_set('Asia/Shanghai');
try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    die("数据库连接失败: " . $e->getMessage());
}
?>