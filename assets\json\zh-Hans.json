{"": "", " cores at ": "", " threads at ": "", "1 day": "", "1 hour": "", "1 month": "", "1 week": "", "6 months": "", "Add Associated Target": "", "Add Kerberos SPN Entry": "", "Add SPN": "", "Admin Password": "", "Admin Username": "", "Administrative user": "", "All Cores": "", "All Threads": "", "Allow all sudo commands": "", "Allow all sudo commands with no password": "", "Allow files which return cannotDownloadAbusiveFile to be downloaded.": "", "Allowed Sudo Commands": "", "Allowed Sudo Commands (No Password)": "", "Allowed sudo commands": "", "Allowed sudo commands with no password": "", "Allows sudo commands": "", "Apps (WIP)": "", "Are you sure you want to terminate all other sessions?": "", "Are you sure you want to terminate the session?": "", "Automated Disk Selection": "", "Available Apps": "", "Available version: {version}": "", "By default, all data disks are created with the amount of swap specified. Changing the value does not affect the amount of swap on existing disks, only disks added after the change. Does not affect log or cache devices as they are created without swap.": "", "Configuration Preview": "", "Core #": "", "Create Data VDEVs": "", "Custom App": "", "Dataset promoted successfully.": "", "Destination Path": "", "Disabling host path validation may result in undefined behavior from TrueNAS system\n      services that access host paths mounted within kubernetes.<br>\n      Undefined behavior may include loss of access to data through inadvertent permissions changes,\n      loss of data due to lack of validation of application compatibility, and application stability issues.<br><br>\n      As such, this configuration is unsupported and bug reports in which this configuration plays a role\n      may be closed as a user configuration issue without further investigation.": "", "Do you want to сonfigure the ACL?": "", "Edit Associated Target": "", "Edit Global Configuration": "", "Edit VM": "", "Enable Host Path Safety Checks": "", "Enable to use <a href=\"https://searchstorage.techtarget.com/definition/thin-provisioning\" target=\"_blank\">thin provisioning</a> where disk space for this volume is allocated <b>‘on demand’</b> as new writes are received. Use caution when enabling this feature, as writes can fail when the pool is low on space.": "", "Enabled Protocols": "", "Enter a number of degrees in Celsius. S.M.A.R.T. reports if the temperature of a drive has changed by N degrees Celsius since the last report.": "", "Enter a threshold temperature in Celsius. S.M.A.R.T. will message with a log level of LOG_CRIT and send an email if the temperature is higher than the threshold.": "", "Enter a threshold temperature in Celsius. S.M.A.R.T. will message with a log level of LOG_INFO if the temperature is higher than the threshold.": "", "Error authenticating with token, please try again.": "", "Error generating token, please try again.": "", "Existing presets": "", "Filesystem Type": "", "Filter {item}": "", "From a key file": "", "General Info": "", "Go to Datasets": "", "If downloading a file returns the error \"This file has been identified as malware or spam and cannot be downloaded\" with the error code \"cannotDownloadAbusiveFile\" then enable this flag to indicate you acknowledge the risks of downloading the file and TrueNAS will download it anyway.": "", "Import Data": "", "Importing Disk": "", "Importing Disk...": "", "Installed": "", "Installed Apps": "", "Installed Catalogs": "", "Inventory": "", "It may impact currently running services and should be done during planned downtime.": "", "Log In": "", "Login error. Please try again.": "", "MSDOSFS Locale": "", "Manage Installed Apps": "", "Manual Selection": "", "Minimum value is 0": "", "No S.M.A.R.T. test results": "", "No S.M.A.R.T. tests have been performed on this disk yet.": "", "No e-mail address is set for root user or any other local administrator. Please, configure such an email address first.": "", "No logs yet": "", "No matching results found": "", "No records": "", "No records have been added yet": "", "Number of VDEVs": "", "Number of VDEVs to create": "", "Only integers allowed": "", "Open Display Device": "", "PCI device does not have a reset mechanism defined and you may experience inconsistent/degraded behavior when starting/stopping the VM.": "", "Partition": "", "Pool Disks have {alerts} alerts and {smartTests} failed S.M.A.R.T. tests": "", "Pool contains {status} Data VDEVs": "", "Pool is not healthy": "", "Pool is using more than {maxPct}% of available space": "", "Pool status is {status}": "", "Preset Name": "", "Promote": "", "Provide keys/passphrases manually": "", "Refresh Charts": "", "Review": "", "Root user (not recommended)": "", "S.M.A.R.T. Test Results": "", "S.M.A.R.T. Test Results of {pk}": "", "Save ACL as preset": "", "Save As Preset": "", "Save Selection": "", "Save current ACL entries as a preset for future use.": "", "Scroll to top": "", "Search or enter value": "", "Select VDEV width": "", "Select a VDEV layout": "", "Select a disk size from inventory": "", "Sessions": "", "Set up TrueNAS authentication method:": "", "Sign In": "", "Snapdev": "", "Start session time": "", "Storage Settings": "", "Submit": "", "Swap Size": "", "Synchronize Time": "", "Synchronize time": "", "System dataset updated.": "", "Terminate Other Sessions": "", "Terminate session": "", "There are no records to show.": "", "These disks do not support S.M.A.R.T. tests:": "", "This session is current and cannot be terminated": "", "This share is configured through TrueCommand": "", "Thread #": "", "Updating ACL": "", "Upload SSH Key": "", "Use Preset": "", "User account password for logging in to the remote system.": "", "Username on the remote system to log in <b>via Web UI</b> to setup connection.": "", "Username on the remote system which will be used to login <b>via SSH</b>.": "", "Validate Host Path": "", "Value in GiB": "", "Vdevs spans enclsoure": "", "Width": "", "Would you like to add a Service Principal Name (SPN) now?": "", "You have successfully added credentials.": "", "{app} Application Summary": "", "{app} Catalog Summary": "", " Please click the button below to add {item}.": " 请点击下面的按钮来添加 {item}。", " When the <b>UPS Mode</b> is set to <i>slave</i>. Enter the open network port number of the UPS <i>Master</i> system. The default port is <i>3493</i>.": "当 <b>UPS 模式</b> 设置为 <i>附属</i> 时。输入 UPS <i>主</i> 系统的开放端口号。默认端口为 <i>3493</i>。", " bytes.": " 字节。", " day, ": " 天，", " days, ": " 天，", " seconds.": " 秒。", "% of all cores": "% 所有核心", "'Hosts Allow' or 'Hosts Deny' has been set": "'Hosts Allow' 或 'Hosts Deny' 已设置", "'Hosts Allow' or 'Hosts Deny' has been updated": "“主机允许”或“主机拒绝”已更新", "(24 Hours)": "（24小时）", "(Examples: 500 KiB, 500M, 2 TB)": "（例如：500 KiB，500M，2 TB）", "(No description)": "（没有说明）", "(Remove pool from database)": "（从数据库中删除池）", "(This Controller)": "（此控制器）", "(TrueNAS Controller 1)": "（TrueNAS 控制器 1）", "(TrueNAS Controller 2)": "（TrueNAS 控制器 2）", "+ Add a backup credential": " 添加备份凭据", "... Make sure the TrueNAS system is powered on and connected to the network.": "... 确保 TrueNAS 系统已通电并连接到网络。", "0=Disabled, blank=inherit": "0=禁用，留空=继承", "1 minute": "1 分钟", "15 seconds": "15 秒", "2 minute": "2 分钟", "20 characters is the maximum length.": "最大长度为 20 个字符。", "2FA": "双重验证", "3 minute": "3 分钟", "30 seconds": "30 秒", "4 minute": "4 分钟", "<a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/WebsiteEndpoints.html\"  target=\"_blank\">S3 API endpoint URL</a>. When using AWS, the endpoint  field can be empty to use the default endpoint for the region, and  available buckets are automatically fetched. Refer to the AWS  Documentation for a list of  <a href=\"https://docs.aws.amazon.com/general/latest/gr/rande.html#s3_website_region_endpoints  target=\"_blank\">Simple Storage Service Website Endpoints</a>.": "<a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/WebsiteEndpoints.html\" target=\"_blank\"> S3 API端点URL </a>。使用AWS时，端点字段可以为空以使用该区域的默认端点，并且会自动获取可用的存储桶。有关简单存储服务网站的列表，请参阅AWS文档<a href=\"https://docs.aws.amazon.com/general/latest/gr/rande.html\\#s3_website_region_endpoints target=\"_blank\"> 端点</a>。", "<a href=\"https://docs.aws.amazon.com/general/latest/gr/rande-manage.html\"  target=\"_blank\">AWS resources in a geographic area</a>. Leave empty to  automatically detect the correct public region for the bucket. Entering  a private region name allows interacting with Amazon buckets created in  that region. For example, enter <i>us-gov-east-1</i> to discover buckets  created in the eastern  <a href=\"https://docs.aws.amazon.com/govcloud-us/latest/UserGuide/whatis.html\" target=\"_blank\">AWS GovCloud</a>  region.": "<a href=\"https://docs.aws.amazon.com/general/latest/gr/rande-manage.html\"  target=\"_blank\">地理区域中的AWS资源</a>。留空以自动检测存储桶的正确公共区域。输入私有区域名称允许与在该区域中创建的Amazon存储桶进行交互。例如，输入<i> us-gov-east-1 </i>以发现在东部创建的存储桶<a href=\"https://docs.aws.amazon.com/govcloud-us/latest/UserGuide/whatis.html\" target=\"_blank\"> AWS GovCloud </a>区域。", "<a href=\"https://docs.microsoft.com/en-us/azure/storage/common/storage-create-storage-account\"  target=\"_blank\">Microsoft Azure</a> account name.": "<a href=\"https://docs.microsoft.com/en-us/azure/storage/common/storage-create-storage-account\"  target=\"_blank\">Microsoft Azure</a> 帐户名称。", "<a href=\"https://docs.pcloud.com/methods/intro/authentication.html\"  target=\"_blank\">pCloud Access Token</a>. These tokens can expire and  require extension.": "<a href=\"https://docs.pcloud.com/methods/intro/authentication.html\"  target=\"_blank\"> pCloud访问令牌</a>。这些令牌可能会过期，并需要延期。", "<a href=\"https://en.wikipedia.org/wiki/Authentication_protocol\"  target=\"_blank\">Authentication protocol</a> used to authenticate  messages sent on behalf of the specified <i>Username</i>.": "<a href=\"https://en.wikipedia.org/wiki/Authentication_protocol\"  target=\"_blank\">身份验证协议</a>用于验证指定<i> 用户名 </i>发送的消息。", "<a href=\"https://en.wikipedia.org/wiki/Cryptographic_protocol\"  target=\"_blank\">Encryption protocol</a> used to encrypt messages  sent on behalf of the specified <i>Username</i>.": "<a href=\"https://en.wikipedia.org/wiki/Cryptographic_protocol\"  target=\"_blank\">加密协议</a>用于加密指定<i> 用户名 </i>发送的消息。", "<a href=\"https://en.wikipedia.org/wiki/Extended_file_attributes\"                target=\"_blank\">Extended attributes</a> are                preserved, but must be supported by both systems.": "<a href=\"https://en.wikipedia.org/wiki/Extended_file_attributes\" target=\"_blank\">扩展属性</a>被保留，但是两个系统都必须支持。", "<a href=\"https://mega.nz/\" target=\"_blank\">MEGA</a> account  password.": "<a href=\"https://mega.nz/\" target=\"_blank\">MEGA</a> 帐户的密码。", "<a href=\"https://mega.nz/\" target=\"_blank\">MEGA</a> account  username.": "<a href=\"https://mega.nz/\" target=\"_blank\">MEGA</a> 帐户的用户名。", "<a href=\"https://rclone.org/swift/#standard-options\" target=\"_blank\">(rclone documentation)</a>.": "<a href=\"https://rclone.org/swift/\\#standard-options\" target=\"_blank\">（rclone文档）</a>。", "<a href=\"https://tools.ietf.org/html/rfc8095\" target=\"_blank\">Transport Protocol</a> for the remote system log server connection. Choosing Transport Layer Security (TLS) also requires selecting a preconfigured system Certificate.": "<a href=\"https://tools.ietf.org/html/rfc8095\" target=\"_blank\">传输协议</a> 用于远程系统日志服务器连接。 选择传输层安全 (TLS) 还需要选择预配置的系统证书", "<a href=\"https://www.backblaze.com/b2/cloud-storage.html\" target=\"_blank\">Backblaze B2</a> Application Key. To generate a new application key, log in to the Backblaze account, go to the <i>App Keys</i> page, and add a new application key. Copy the <i>applicationKey</i> string to this field.": "<a href=\"https://www.backblaze.com/b2/cloud-storage.html\" target=\"_blank\"> Backblaze B2 </a>应用程序密钥。要生成新的应用程序密钥，请登录Backblaze帐户，进入<i> App Keys </i>页面，然后添加一个新的应用程序密钥。将<i> applicationKey </i>字符串复制到此字段。", "<a href=\"https://www.fastmail.com/help/technical/ssltlsstarttls.html\" target=\"_blank\">Email encryption</a> type. Choices are <i>Plain (No Encryption)</i>, <i>SSL (Implicit TLS)</i>, or <i>TLS (STARTTLS)</i>.": "<a href=\"https://www.fastmail.com/help/technical/ssltlsstarttls.html\" target=\"_blank\">电子邮件加密</a>类型。选择为<i>普通（无加密）</i>，<i> SSL（隐式TLS）</i>或<i> TLS（STARTTLS）</i>。", "<a href=\"https://www.ixsystems.com/community/\" target=\"_blank\"> TrueNAS Forums</a> - Find answers from other users in the forums.": "<a href=\"https://www.ixsystems.com/community/\" target=\"_blank\"> TrueNAS 论坛</a> - 在论坛中查找其他用户的答案", "<a href=\"https://www.ixsystems.com/support/\" target=\"_blank\"> TrueNAS Licensing</a> - Learn more about enterprise-grade support.": "<a href=\"https://www.ixsystems.com/support/\" target=\"_blank\"> TrueNAS 许可</a> - 了解更多有关企业级支持的信息", "<a href=\"https://www.truenas.com/docs/hub/\" target=\"_blank\"> TrueNAS Documentation Hub</a> - Read and contribute to the open-source documentation.": "<a href=\"https://www.truenas.com/docs/hub/\" target=\"_blank\"> TrueNAS 文档中心</a> - 阅读或贡献开源文档", "<b>COPY</b>: Files from the source are <i><u>copied</u></i> to the destination. If files with the same names are present on the destination, they are <i><u>overwritten</u></i>.": "<b>复制</b>: 将源文件 <i><u>复制</u></i> 到目标。如果目标位置上存在相同名称的文件，它们将<i><u>被覆盖</u></i>。", "<b>Copy & Paste</b> <br/>\n                  Context menu copy and paste operations are disabled in the Shell. Copy and paste shortcuts for Mac are <i>Command+C</i> and <i>Command+V</i>. For most operating systems, use <i>Ctrl+Insert</i> to copy and <i>Shift+Insert</i> to paste.<br/><br/>\n                  <b>Kill Process</b> <br/>\n                  Kill process shortcut is <i>Crtl+C</i>.": "<b>复制和粘贴</b> <br/>\n                  上下文菜单复制和粘贴操作在 Shell 中被禁用。Mac 的复制和粘贴快捷方式是  <i>Command+C</i> 和 <i>Command+V</i>。 对于大多数操作系统，使用 <i>Ctrl+Insert</i> 复制和 <i>Shift+Insert</i> 粘贴。<br/><br/>\n <b>终止进程</b > <br/>\n                  终止进程的快捷方式是 <i>Crtl+C</i>。", "<b>MOVE</b>: After files are <i><u>copied</u></i> from the source to the destination, they are <i><u>deleted</u></i> from the source. Files with the same names on the destination are <i><u>overwritten</u></i>.": "<b>移动</b>：从源<i><u>复制</u></i>文件到目标后，这些文件将从源<i><u>删除</u></i>。而目标上具有相同名称的文件将被<i><u>覆盖</u></i>。", "<b>Not recommended.</b> Allow more than one group to have the same group ID.": "<b>不建议。</b> 允许多个组具有相同的组 ID。", "<b>Root logins are discouraged.</b> Allows root  logins. A password must be set for the <i>root</i> user account.": "<b>不鼓励使用root登录。</b>允许root登录。必须为<i> root </i>用户帐户设置密码。", "<b>SET</b> will changes all destination datasets to <code>readonly=on</code> after finishing the replication. <br><b>REQUIRE</b> stops replication unless all existing destination datasets to have the property <code>readonly=on</code>. <br><b>IGNORE</b> disables checking the <code>readonly</code> property during replication.": "复制完成后，<b>SET</b>会将所有目标数据集更改为<code>readonly=on</code>。<br> <b>要求</b>停止复制，除非所有现有目标数据集都具有属性<code>readonly=on</code>。<br> <b>忽略</b>禁止在复制期间检查<code>readonly</code>属性。", "<b>SYNC</b>: Files on the destination are <i><u>changed</u></i> to match those on the source. If a file does not exist on the source, it is also <i><u>deleted</u></i> from the destination.": "<b>同步</b>：对目标上的文件进行<i><u>更改</u></i>以匹配源上的文件。如果源上不存在文件，则还会从目标<i><u>删除</u></i>。", "<b>WARNING:</b> Rolling the dataset back destroys data on the dataset <i>and</i> can destroy additional snapshots that are related to the dataset. <b>This can result in permanent data loss!</b> Do not roll back until all desired data and snapshots are backed up.": "<b>警告：此操作将导致永久性数据丢失</b> 不要回滚，除非所有需要的数据和快照都已备份。", "<b>WARNING:</b> The configuration file contains sensitive data like system passwords. However, SSH keys that are stored in <samp>/root/.ssh</samp> are <b>NOT</b> backed up by this operation. Additional sensitive information can be included in the configuration file.<br />": "<b>警告：</b> 配置文件包含敏感数据，如系统密码。但是，此操作<b>不会</b>备份存储在 <samp>/root/.ssh</samp> 中的 SSH 密钥。配置文件中会包含其他敏感信息。<br />", "<b>rsyncd</b> listens on this port.": "<b>rsyncd</b> 在此端口侦听。", "<em>Warning:</em> The WireGuard service must be active on the client system to access the TrueCommand UI.": "<em>警告：</em> WireGuard服务必须在客户端系统上处于活动状态才能访问TrueCommand UI。", "<i>0</i> disables quotas. Specify a maximum allowed space for this dataset.": "<i>0</i>禁用配额。定义这个数据集的最大允许空间。", "<i>0</i> is unlimited. A specified value applies to both this dataset and any child datasets.": "<i>0</i>代表无限制。指定的值将同时应用于此数据集和任何子数据集。", "<i>0</i> is unlimited. Reserve additional space for datasets containing logs which could take up all available free space.": "<i>0</i>代表无限制。为包含日志的数据集保留额外的空间，这些日志可能会占用所有可用的空闲空间。", "<i>AHCI</i> emulates an AHCI hard disk for best software compatibility. <i>VirtIO</i> uses paravirtualized drivers and can provide better performance, but requires the operating system installed in the VM to support VirtIO disk devices.": "<i>AHCI</i>模拟一个AHCI硬盘以获得最好的兼容性。<i>VirtIO</i>使用半虚拟化驱动并可以提供更高的性能，但是需要虚拟机中安装的操作系统支持VirtIO磁盘设备。", "<i>AHCI</i> emulates an AHCI hard disk for better software compatibility. <i>VirtIO</i> uses paravirtualized drivers and can provide better performance, but requires the operating system installed in the VM to support VirtIO disk devices.": "<i>AHCI</i>模拟一个AHCI硬盘以获得更好的软件兼容性。<i>VirtIO</i>使用半虚拟化驱动并可以提供更高的性能，但是需要虚拟机中安装的操作系统支持VirtIO磁盘设备。", "<i>Basic Authentication</i> is unencrypted. <i>Digest Authentication</i> is encrypted.": "<i>基础身份验证</i>不加密。<i>摘要身份验证</i>需要加密。", "<i>Certificate Signing Requests</i> control when an external CA will issue (sign) the certificate. Typically used with ACME or other CAs that most popular browsers trust by default  <i>Import Certificate Signing Request</i> lets you import an existing CSR onto the system. Typically used with ACME or internal CAs.": "<i>证书签名请求</i>控制外部 CA 何时颁发（签名）证书。 通常与大多数流行浏览器默认信任的 ACME 或其他 CA 一起使用 <i>导入证书签名请求</i>可让您将现有 CSR 导入系统。 通常与 ACME 或内部 CA 一起使用。", "<i>Device</i> provides virtual storage access to zvols, zvol snapshots, or physical devices.  <i>File</i> provides virtual storage access to a single file.": "<i>设备</i>提供对zvol，zvol快照或物理设备的虚拟存储访问。<i>文件</i>提供对单个文件的虚拟存储访问。", "<i>HTTP</i> will keep the connection unencrypted. <i>HTTPS</i> encrypts the connection. <i>HTTP+HTTPS</i> allows both types of connections.": "<i>HTTP</i>会保持连接不加密。<i>HTTPS</i>会加密连接。<i>HTTP+HTTPS</i> 同时允许两种类型的连接。", "<i>Intel e82545 (e1000)</i> emulates the same Intel Ethernet card. This provides compatibility with most operating systems. <i>VirtIO</i> provides better performance when the operating system installed in the VM supports VirtIO paravirtualized network drivers.": "<i> Intel e82545（e1000）</i>模拟相同的Intel以太网卡，与大多数操作系统兼容。当虚拟机中安装的操作系统支持VirtIO半虚拟化网络驱动程序时，<i> VirtIO </i>可以提供更好的性能。", "<i>Internal Certificates</i> use system-managed CAs for certificate issuance.  <i>Import Certificate</i> lets you import an existing certificate onto the system.": "<i>内部证书</i>使用系统管理的 CA 来颁发证书。 <i>导入证书</i> 允许您将现有证书导入系统。", "<i>PUSH</i> sends data to cloud storage. <i>PULL</i>  receives data from cloud storage. Changing the direction resets the  <i>Transfer Mode</i> to COPY.": "<i> 推送 </i>将数据发送到云存储。 <i> 拉取 </i>从云存储接收数据。更改方向会将<i>传输模式</i>重设为COPY。", "<i>PUSH</i> sends snapshots to a destination  system.<br><br>  <i>PULL</i> connects to a remote system and retrieves snapshots  matching a <b>Naming Schema</b>.": "<i> 推送 </i>将快照发送到目标系统。<br><br> <i> 拉取 </i>连接到远程系统并检索与<b>命名惯例</b>匹配的快照。", "<i>Quick</i> erases only the partitioning information on a disk without clearing other old data. <i>Full with zeros</i> overwrites the entire disk with zeros. <i>Full with random data</i> overwrites the entire disk with random binary data.": "<i>快速</i>只擦除磁盘的分区信息，不删除其他旧的数据。<i>用0填充</i>用0覆盖整个磁盘。<i>使用随机数据填充</i> 用随机二进制数据覆盖整个磁盘。", "<i>Sensitive</i> assumes filenames are case sensitive. <i>Insensitive</i> assumes filenames are not case sensitive. <i>Mixed</b> understands both types of filenames.": "<i>敏感</i>默认文件名是大小写敏感的。<i>不敏感</i>默认文件名是大小写不敏感的。<i>混合</b>同时兼容两种类型的文件名。", "<i>Standard</i> uses the sync settings that have been requested by the client software, <i>Always</i> waits for data writes to complete, and <i>Disabled</i> never waits for writes to complete.": "<i>标准</i> 使用客户端发送的同步设置，<i>总是</i> 等待数据写入完成， 和<i>禁用</i>从不等待写入完成。", "<i>Yes:</i> Disables the <b>Password</b>  fields and removes the password from the account. The account cannot  use password-based logins for services. For example, disabling the  password prevents using account credentials to log in to an SMB share  or open an SSH session on the system. The <b>Lock User</b> and  <b>Permit Sudo</b> options are also removed.<br><br>  <i>No:</i> Requires adding a <b>Password</b> to the account. The  account can use the saved <b>Password</b> to authenticate with  password-based services.": "<i>是：</i>禁用<b>密码</b>字段，并从帐户中删除密码。该帐户不能使用基于密码的登录服务。例如，禁用密码可以防止使用帐户凭据登录到SMB共享或在系统上打开SSH会话。 \n<b>锁定用户</b>和<b>允许Sudo </b>选项也被删除。<br> <br> <i>否：</i>需要添加<b>密码</b> 转到该帐户。 该帐户可以使用保存的<b>密码</b>并对基于密码的服务进行身份验证。", "<i>global</i> is a reserved name that cannot be used as a share name. Please enter a different share name.": "<i>global</i>是保留名称，不能用作共享名。请输入其他共享名。", "<i>{disk}</i> has been detached.": "<i>{disk}</i> 已分离。", "<p>Currently following GPU(s) have been isolated:<ol>{gpus}</ol></p>": "<p>目前以下GPU(s)已经被隔离：<ol>{gpus}</ol></p>", "<p>Including the Password Secret Seed allows using this configuration file with a new boot device. This also decrypts all system passwords for reuse when the configuration file is uploaded.</p> <br /><b>Keep the configuration file safe and protect it from unauthorized access!</b>": "<p>包括密码秘密种子在内，可以将此配置文件与新的引导设备一起使用。上传配置文件时，会解密所有系统密码以供重复使用。</p> <br /> <b>请保管好配置文件并保护好它，其免受未经授权的访问！</b>", "<p>The system will reboot to perform this operation!</p> <p><font color=\"red\">All passwords are reset when the  uploaded configuration database file was saved  without the Password Secret Seed. </font></p>": "<p>系统将重新启动以执行此操作！</p> <p> <font color=\"red\">在没有密码秘密种子的情况下保存上传的配置数据库文件时，所有密码都会重置。</font> </p>", "<strong>Dataset:</strong> ": "<strong>数据集：</strong>", "A User Access Token for <a href=\"https://developer.box.com/\"  target=\"_blank\">Box</a>. An  <a href=\"https://developer.box.com/reference#token\"  target=\"_blank\">access token</a> enables Box to verify a request  belongs to an authorized session. Example token:  <i>********************************</i>.": "<a href=\"https://developer.box.com/\"  target=\"_blank\"> Box </a>的用户访问令牌。<a href=\"https://developer.box.com/reference\\#token\"  target=\"_blank\">访问令牌</a>使Box能够验证请求是否属于授权会话。令牌示例：<i> ******************************** </i>。", "A message with verification instructions has been sent to the new email address. Please verify the email address before continuing.": "带有验证说明的消息已发送到新的电子邮件地址，请在继续之前验证电子邮件地址。", "A pool with this name already exists.": "具有该名称的池已经存在。", "A single bandwidth limit or bandwidth limit schedule in rclone format. Separate entries by pressing <code>Enter</code>. Example:  <samp>08:00,512 12:00,10MB 13:00,512 18:00,30MB 23:00,off</samp>. Units can be specified with a suffix of <samp>b</samp>, <samp>k</samp> (default), <samp>M</samp>, or <samp>G</samp>. See <a href=\"https://rclone.org/docs/#bwlimit-bandwidth-spec\" target=\"_blank\">rclone --bwlimit</a>.": "rclone格式的单个带宽限制或带宽限制计划。通过按<code> Enter </code>分隔条目。例如：<samp>08:00,512 12:00,10MB 13:00,512 18:00,30MB 23:00,off</samp>。可以使用开头字母指定单位：<samp>b</samp>, <samp>k</samp>（默认），<samp>M</samp>或<samp>G</samp>。请参阅<a href=\"https://rclone.org/docs/\\#bwlimit-bandwidth-spec\" target=\"_blank\">rclone --bwlimit </a>。", "A smaller block size can reduce sequential I/O performance and space efficiency.": "较小的块大小会降低顺序 I/O 性能和空间效率。", "A stripe log vdev may result in data loss if it fails combined with a power outage.": "如果一个条带日志 vdev 出现故障并断电，则可能会导致数据丢失。", "A stripe {vdevType} vdev is highly discouraged and will result in data loss if it fails": "非常不鼓励使用条带 {vdevType} vdev，如果失败会导致全部数据丢失", "A system update is in progress. It might have been launched in another window or by an external source like TrueCommand.": "正在进行系统更新。 它可能是在另一个窗口中启动的，也可能是由 TrueCommand 等外部源启动的。", "A unique name to identify this keypair. Automatically generated keypairs are named after the object that generated the keypair with \" Key\" appended to the name.": "标识此密钥对的唯一名称。自动生成的密钥对以生成密钥对的对象命名，并在名称后附加\" Key \"。", "A username on the FTP Host system. This user must already exist  on the FTP Host.": "FTP主机系统上的用户名。该用户必须已经存在于FTP主机上。", "A valid IP address or domain name to which OpenVPN will connect.": "OpenVPN将连接到的有效IP地址或域名。", "ACL Editor": "ACL 编辑器", "ACL Entries": "ACL条目", "ACL Mode": "ACL模式", "ACL Type": "ACL类型", "ACME DNS-Authenticators": "ACME DNS身份验证器", "ACME Server Directory URI": "ACME服务器目录URI", "AD Timeout": "AD超时", "ALERT": "警报", "API Docs": "API 文档", "API Key": "API密钥", "API Key Actions": "API 秘钥操作", "API Key or Password": "API密钥或密码", "API Key:": "API 密钥：", "API Keys": "API密钥", "ARN": "ARN", "ATA Security User": "ATA安全用户", "AVAILABLE": "可用", "AWS Region": "AWS地区", "Abort": "中止", "About": "关于", "Access": "访问", "Access Based Share Enumeration": "基于访问的共享枚举", "Access Control Entry": "访问控制入口", "Access Control Entry (ACE) user or group. Select a specific <i>User</i> or <i>Group</i> for this entry, <i>owner@</i> to apply this entry to the user that owns the dataset, <i>group@</i> to apply this entry to the group that owns the dataset, or <i>everyone@</i> to apply this entry to all users and groups. See <a href=\"https://man7.org/linux/man-pages/man1/nfs4_setfacl.1.html\" target=\"_blank\">nfs4_setfacl(1) NFSv4 ACL ENTRIES</a>.": "访问控制条目 (ACE) 用户或组。 为此条目选择一个特定 <i>用户</i> 或 <i>组</i>, <i>拥有者@</i> 以将此条目应用于所有该项目的拥有者， <i>组@</i> 以将此条目应用于所有该项目的拥有组， or <i>所有热@</i> 以将此条目应用于所有用户和组。 请参阅 <a href=\"https://man7.org/linux/man-pages/man1/nfs4_setfacl.1.html\" target=\"_blank\">nfs4_setfacl(1) NFSv4 ACL ENTRIES</a>.", "Access Control List": "访问控制列表", "Access Key": "访问密钥", "Access Key ID": "访问密钥ID", "Access Key ID for the linked AWS account.": "链接AWS账户的访问密钥ID。", "Access Mode": "访问模式", "Access Token": "访问令牌", "Access Token <a href=\"https://api.hubic.com/sandbox/\"  target=\"_blank\">generated by a Hubic account</a>.": "访问由 <a href=\"https://api.hubic.com/sandbox/\"  target=\"_blank\">Hubic帐户生成的令牌</a>。", "Access Token for a Dropbox account. A  <a href=\"https://blogs.dropbox.com/developers/2014/05/generate-an-access-token-for-your-own-account/\"  target=\"_blank\">token must be generated</a> by the  <a href=\"https://www.dropbox.com/\" target=\"_blank\">Dropbox account</a>  before adding it here.": "Dropbox帐户的访问令牌。令牌<a href=\"https://blogs.dropbox.com/developers/2014/05/generate-an-access-token-for-your-own-account/\"  target=\"_blank\">必须由</a><a href=\"https://www.dropbox.com/\" target=\"_blank\">Dropbox帐户</a>生成，然后再添加到此处。", "Access checks should use bucket-level IAM policies.": "访问检查应使用存储桶级 IAM 策略。", "Account Key": "账户密钥", "Account Name": "账户名", "Account to be used for guest access. Default is  <i>nobody</i>. The chosen account is required to have permissions to the  shared pool or dataset. To adjust permissions, edit the dataset Access  Control List (ACL), add a new entry for the chosen guest account, and  configure the permissions in that entry. If the selected <i>Guest Account</i>  is deleted the field resets to <i>nobody</i>.": "用于访客访问的帐户。 默认为<i>nobody</i>。 所选帐户必须具有共享池或数据集的权限。 要调整权限，请编辑数据集访问控制列表 (ACL)，为所选访客帐户添加新条目，并在该条目中配置权限。 如果所选的<i>访客帐户</i>被删除，该字段将重置为<i>nobody</i>。", "Ace has errors.": "访问控制入口有错误。", "Action Not Possible": "无法采取行动", "Actionable Options": "可用选项", "Actions": "动作", "Activate": "启用", "Activate KMIP configuration and begin syncing keys with the KMIP server.": "激活KMIP配置，并开始将密钥与KMIP服务器同步。", "Activate the Basic Constraints extension to identify whether           the certificate's subject is a CA and the maximum depth of valid           certification paths that include this certificate.": "激活 Basic Constraints 扩展以确定证书的主题是否为 CA，以及包含此证书的有效证书路径的最大深度。", "Activate this Boot Environment?": "是否激活此引导环境？", "Activate this certificate extension.": "激活此证书扩展。", "Activate this certificate extension.  The key usage extension defines the purpose  (e.g., encipherment, signature, certificate signing) of the key contained in  the certificate. The usage restriction might be employed when a key that  could be used for more than one operation is to be restricted. For  example, when an RSA key should be used only to verify signatures on  objects other than public key certificates and CRLs, the <i>Digital Signature</i>  bits would be asserted. Likewise, when an RSA key should be used only for key  management, the <i>Key Encipherment</i> bit would be asserted. <br>  See <a href=\"https://www.ietf.org/rfc/rfc3280.txt\">RFC 3280, section *******</a>  for more information.": "激活此证书扩展。 密钥使用扩展定义了证书中包含的密钥的用途（例如，加密、签名、证书签名）。 当可以用于多个操作的键被限制时，可以使用使用限制。 例如，当 RSA 密钥仅用于验证对象上的签名而不是公钥证书和 CRL 时，<i>数字签名</i>位将被断言。 同样，当 RSA 密钥仅用于密钥管理时，<i>Key Encipherment</i> 位将被断言。 <br>有关详细信息，请参阅 <a href=\"https://www.ietf.org/rfc/rfc3280.txt\">RFC 3280，第 ******* 节</a>。", "Activate this certificate extension.The Extended Key Usage extension identifies and limits valid uses for this certificate, such as client authentication or server authentication.See <a href=\"https://www.ietf.org/rfc/rfc3280.txt\" target=\"_blank\">RFC 3280, section *******3</a> for more details.": "激活此证书扩展。扩展密钥使用扩展识别并限制此证书的有效用途，例如客户端身份验证或服务器身份验证。请参阅 <a href=\"https://www.ietf.org/rfc/rfc3280.txt\" target=\"_blank\">RFC 3280，第 *******3 节</a> 了解更多详情。", "Activate this extension. The authority key identifier extension provides a means of  identifying the public key corresponding to the private key used to  sign a certificate. This extension is used where an issuer has  multiple signing keys (either due to multiple concurrent key pairs or  due to changeover). The identification MAY be based on either the  key identifier (the subject key identifier in the issuer's  certificate) or on the issuer name and serial number.<br>  See <a href=\"https://www.ietf.org/rfc/rfc3280.txt\">RFC 3280, section *******</a>  for more information.": "激活此扩展程序。 授权密钥标识符扩展提供了一种识别与用于签署证书的私钥相对应的公钥的方法。 此扩展用于颁发者具有多个签名密钥的情况（由于多个并发密钥对或由于转换）。 标识可以基于密钥标识符（颁发者证书中的主题密钥标识符）或颁发者名称和序列号。<br>请参阅 <a href=\"https://www.ietf.org/rfc /rfc3280.txt\">RFC 3280，第 ******* 节</a>了解更多信息。", "Activate this module for use with Rsync. Unset this field to deactivate the module without completely removing it.": "激活此模块以与 Rsync 一起使用。 取消设置此字段以停用模块而不完全删除它。", "Activates a tuning script which attempts to optimize  the system depending on the installed hardware. <b>Warning:</b> Autotuning  is only used as a temporary measure and is not a permanent fix for system  hardware issues.": "激活一个调整脚本，该脚本尝试根据已安装的硬件优化系统。<b>警告</b>：自动调整仅用作临时措施，不是解决系统硬件问题的永久解决方法。", "Activates the configuration. Unset to disable the configuration without deleting it.": "激活配置。取消设置以禁用配置而不删除它。", "Activates the replication schedule.": "激活复制计划。", "Active": "活动的", "Active Directory": "活动目录", "Active Directory - Primary Domain": "活动目录 - 主域名", "Active Directory and LDAP are disabled.": "活动目录和LDAP被禁用。", "Active Directory is disabled.": "活动目录被禁用。", "Active Directory must be enabled before adding new domains.": "在添加新域之前，必须先启用活动目录。", "Active IP Addresses": "活跃 IP 地址", "Active {controller}.": "活跃的 {controller}。", "Adapter Type": "适配器类型", "Add": "添加", "Add ": "添加", "Add API Key": "添加API密钥", "Add Alert Service": "添加警报服务", "Add Authorized Access": "添加授权访问", "Add CA": "添加CA", "Add CSR": "添加CSR", "Add Catalog": "添加目录", "Add Certificate": "添加证书", "Add Cloud Sync Task": "添加云同步任务", "Add Cron Job": "添加定时任务", "Add DNS Authenticator": "添加 DNS 身份验证器", "Add Dataset": "添加数据集", "Add Device": "添加设备", "Add Disks": "添加磁盘", "Add Disks To:": "将磁盘添加到", "Add Extent": "添加范围", "Add External Interfaces": "添加外部接口", "Add Group": "添加组", "Add Group Quotas": "添加组配额", "Add ISCSI Target": "添加 ISCSI 目标", "Add Idmap": "添加Idmap", "Add Init/Shutdown Script": "添加开机/关机脚本", "Add Initiator": "添加启动器", "Add Interface": "添加接口", "Add Item": "添加项目", "Add Kerberos Keytab": "添加Kerberos密钥表", "Add Kerberos Realm": "添加Kerberos领域", "Add License": "添加许可证", "Add NFS Share": "添加 NFS 共享", "Add NTP Server": "添加 NTP 服务器", "Add Periodic Snapshot Task": "添加定期快照任务", "Add Portal": "添加门户", "Add Replication Task": "添加复制任务", "Add Rsync Module": "添加 Rsync 模块", "Add Rsync Task": "添加Rsync任务", "Add S.M.A.R.T. Test": "添加S.M.A.R.T.测试", "Add SMB": "添加SMB", "Add SMB Share": "添加SMB共享", "Add Scrub Task": "添加校验任务", "Add Snapshot": "添加快照", "Add Static Route": "添加静态路由", "Add Sysctl": "添加Sysctl", "Add Target": "添加目标", "Add Target/Extent": "添加目标/范围", "Add To Pool": "添加到池", "Add To Trusted Store": "添加到信任库", "Add Unix (NFS) Share": "添加 Unix (NFS) 共享", "Add User": "添加用户", "Add User Quotas": "添加用户配额", "Add VM Snapshot": "添加VM快照", "Add VMware Snapshot": "添加VMware快照", "Add Vdev": "添加Vdev", "Add Vdevs": "添加Vdevs", "Add WebDAV": "添加WebDAV", "Add WebDAV Share": "添加 WebDAV 共享", "Add Windows (SMB) Share": "添加 Windows (SMB) 共享", "Add Zvol": "添加Zvol", "Add a new bucket to your Storj account.": "将新存储桶添加到您的 Storj 帐户。", "Add any more <a href=\"https://man7.org/linux/man-pages/man5/sshd_config.5.html\" target=\"_blank\">sshd_config(5)</a> options not covered in this screen. Enter one option per line. These options are case-sensitive. Misspellings can prevent the SSH service from starting.": "添加更多 <a href=\"https://man7.org/linux/man-pages/man5/sshd_config.5.html\" target=\"_blank\">sshd_config(5)</a> 未在此处显示的选项。每行输入一个选项。选项是大小写敏感的。错误的拼写可能导致SSH服务无法启动。", "Add any notes about this zvol.": "输入这个zvol的备注。", "Add bucket": "添加存储桶", "Add catalog to system even if some trains are unhealthy.": "即使某些列车不健康，也将目录添加到系统中。", "Add entry": "添加条目", "Add groups": "添加组", "Add listen": "添加监听", "Add more options from <a href=\"https://linux.die.net/man/8/tftpd\" target=\"_blank\">tftpd(8)</a>. Add one option on each line.": "从 <a href=\"https://linux.die.net/man/8/tftpd\" target=\"_blank\">tftpd(8)</a>. 添加更多选项。每行输入一个选项", "Add new": "新增", "Add this user to additional groups.": "将此用户添加到其他组。", "Added disks are erased, then the pool is extended onto the new disks with the chosen topology. Existing data on the pool is kept intact.": "删除添加的磁盘，然后将池扩展到具有所选拓扑的新磁盘上。池中的现有数据保持不变。", "Adding Vdevs to an encrypted pool resets the passphrase and recovery key!": "将Vdevs添加到加密池中会重置密码和恢复密钥！", "Adding data vdevs of different types is not supported.": "不支持添加不同类型的数据vdev。", "Adding large catalogs can take minutes. Please check on the progress in Task Manager.": "添加大型目录可能需要几分钟时间。请在任务管理器中查看进度。", "Additional      <a href=\"https://rsync.samba.org/ftp/rsync/rsync.html\" target=\"_blank\">rsync(1)</a>      options to include. Separate entries by pressing <code>Enter</code>.<br>      Note: The \"*\" character must be escaped with a backslash (\\*.txt) or used      inside single quotes ('*.txt').": "要包含的附加<a href=\"https://rsync.samba.org/ftp/rsync/rsync.html\" target=\"_blank\">rsync(1)</a>选项。通过按<code> Enter </code>分隔条目。<br>注意： \"*\"字符必须以反斜杠 (\\\\*.txt) 进行转义，或在单引号 ('*.txt') 内使用。", "Additional  <a href=\"https://www.samba.org/samba/docs/current/man-html/smb.conf.5.html\" target=\"_blank\">smb.conf</a>  parameters.": "更多  <a href=\"https://www.samba.org/samba/docs/current/man-html/smb.conf.5.html\" target=\"_blank\">smb.conf</a>  参数。", "Additional <a href=\"https://www.smartmontools.org/browser/trunk/smartmontools/smartctl.8.in\" target=\"_blank\">smartctl(8)</a> options.": "附加的<a href=\"https://www.smartmontools.org/browser/trunk/smartmontools/smartctl.8.in\" target=\"_blank\">smartctl(8)</a>参数。", "Additional Data VDevs to Create": "要创建的附加数据VDev", "Additional Domains": "附加域", "Additional Domains:": "附加域", "Additional Hardware": "附加硬件", "Additional Kerberos application settings. See the \"appdefaults\" section of <a href=\"https://web.mit.edu/kerberos/krb5-1.12/doc/admin/conf_files/krb5_conf.html\" target=\"_blank\">[krb.conf(5)]</a>. for available settings and usage syntax.": "更多Kerberos应用设置。请参阅 \"appdefaults\" section of <a href=\"https://web.mit.edu/kerberos/krb5-1.12/doc/admin/conf_files/krb5_conf.html\" target=\"_blank\">[krb.conf(5)]</a>. 以获取可用设置和用法语法。", "Additional Kerberos library settings. See the \"libdefaults\" section of <a href=\"https://web.mit.edu/kerberos/krb5-1.12/doc/admin/conf_files/krb5_conf.html\" target=\"_blank\">[krb.conf(5)]</a>. for available settings and usage syntax.": "更多Kerberos库设置。请参阅 \"libdefaults\" section of <a href=\"https://web.mit.edu/kerberos/krb5-1.12/doc/admin/conf_files/krb5_conf.html\" target=\"_blank\">[krb.conf(5)]</a>. 以获取可用设置和用法语法。", "Additional Parameters": "附加参数", "Additional domains to search. Separate entries by  pressing <code>Enter</code>. Adding search domains can cause slow DNS  lookups.": "要搜索的其他域。通过按<code> Enter </code>分隔条目。添加搜索域可能会导致DNS查找速度变慢。", "Additional hosts to be appended to <i>/etc/hosts</i>. Separate entries by pressing <code>Enter</code>. Hosts defined here are  still accessible by name even when DNS is not available. See  <a href=\"https://man7.org/linux/man-pages/man5/hosts.5.html\" target=\"_blank\">hosts(5)</a>  for additional information.": "在 <i>/etc/hosts</i>中添加更多主机。 按下 <code>Enter</code> 以分隔条目. 此处定义的主机在DNS失效的情况下仍可用名称访问。 参阅  <a href=\"https://man7.org/linux/man-pages/man5/hosts.5.html\" target=\"_blank\">hosts(5)</a>  以获取更多信息", "Additional options for <a href=\"https://arthurdejong.org/nss-pam-ldapd/nslcd.conf.5\" target=\"_blank\">nslcd.conf</a>.": "附加选项 <a href=\"https://arthurdejong.org/nss-pam-ldapd/nslcd.conf.5\" target=\"_blank\">nslcd.conf</a>。", "Additional parameters.": "附加参数。", "Address": "地址", "Addresses": "地址", "Adjust Scrub/Resilver Priority": "调整数据校验/重新同步优先级", "Adjust how often alert notifications are sent, use  the Frequency drop-down. Setting the Frequency to NEVER prevents that  alert from being added to alert notifications, but the alert can still  show in the web interface if it is triggered.": "调整发送警报通知的频率，使用频率下拉列表。 将频率设置为 NEVER 可防止将该警报添加到警报通知中，但如果触发该警报，该警报仍可显示在 Web 界面中。", "Adjust the file permissions using the checkboxes.": "使用复选框来调整文件权限。", "Admin Server": "管理服务器", "Admin Servers": "管理服务器", "Administrative account name on the LDAP server. Example: <i>cn=Manager,dc=test,dc=org</i>.": "LDAP服务器上的管理帐户名。例子：<i>cn=Manager,dc=test,dc=org</i>.", "Administrators Group": "管理员组", "Adv. Power Management": "高级电源管理", "Adv. Power Mgmt.": "高级电源管理", "Advanced": "高级", "Advanced Details": "高级详细信息", "Advanced Mode": "高级模式", "Advanced Options": "高级选项", "Advanced Power Management": "高级电源管理", "Advanced Remote Options": "高级远程选项", "Advanced Replication Creation": "高级复制创建", "Advanced Settings": "高级设置", "After entering the <b>Hostname, Username</b>, and <b>Password</b>, click <b>Fetch Datastores</b> and select the datastore to be synchronized.": "在输入<b>主机名，用户名</b>，和<b>密码</b>后，点击<b>获取数据存储区</b>并选择同步的数据存储区。", "Alert": "警报", "Alert Services": "警报服务", "Alert Settings": "警报设置", "Alert service saved": "警报服务已保存", "Alerts": "警报", "Alerts could not be loaded": "无法加载警告", "Algorithm": "算法", "Alias for the identical interface  on the other TrueNAS controller. The alias can be an IPv4 or IPv6 address.": "另一个 TrueNAS 控制器上相同接口的别名。 别名可以是 IPv4 或 IPv6 地址。", "Aliases": "别名", "Aliases must be 15 characters or less.": "别名不得超过15个字符。", "All": "全部", "All Disks": "所有磁盘", "All data on that pool was destroyed.": "所有在此存储池上的数据将被摧毁。", "All data stored in this dataset will be decrypted and the dataset marked  as non-encrypted. Do you want to continue?": "存储在此数据集中的所有数据都将被解密，并将数据集标记为未加密。 您要继续吗？", "All pools are online.": "所有存储池在线", "All selected directories must be at the same level i.e., must have the same parent directory.": "所有选定的目录必须处于同一级别，即必须具有相同的父目录。", "All users": "所有用户", "Allocate RAM for the VM. Minimum value is 256 MiB.": "为虚拟机分配内存，最小值为 256 MiB。", "Allocate at least 256 MiB.": "至少分配256 MiB。", "Allocate space for the new zvol.": "为新的zvol分配空间。", "Allow": "允许", "Allow All": "允许全部", "Allow All Initiators": "允许所有启动器", "Allow Anonymous Binding": "允许匿名绑定", "Allow Anonymous Login": "允许匿名登录", "Allow Blocks Larger than 128KB": "允许大于128KB的块", "Allow Compressed WRITE Records": "允许压缩的写记录", "Allow DNS Updates": "允许DNS更新", "Allow Duplicate GIDs": "允许重复的GID", "Allow Guest Access": "允许访客访问", "Allow Kerberos Authentication": "允许Kerberos身份验证", "Allow Local User Login": "允许本地用户登录", "Allow New Files": "允许创建新文件", "Allow Password Authentication": "允许密码验证", "Allow Root Login": "允许Root登录", "Allow Specific": "允许特定", "Allow TCP Port Forwarding": "允许TCP端口转发", "Allow Taking Empty Snapshots": "允许创建空快照", "Allow Transfer Resumption": "允许断点续传", "Allow Trusted Domains": "允许可信域", "Allow all initiators": "允许所有发起者", "Allow anonymous FTP logins with access to the  directory specified in <b>Path</b>.": "允许匿名FTP登录名访问<b>路径</b>中指定的目录。", "Allow any local user to log in. By default, only  members of the <i>ftp</i> group are allowed to log in.": "允许任何本地用户登录。默认情况下，仅允许<i> ftp </i>组的成员登录。", "Allow configuring a non-standard port to access the GUI over <i>HTTP</i>. Changing this setting might require changing a <a href=\"https://www.redbrick.dcu.ie/~d_fens/articles/Firefox:_This_Address_is_Restricted\" target=\"_blank\">Firefox configuration setting</a>.": "允许配置一个非标准端口来通过<i>HTTP</i>访问GUI。修改这个设置可能需要修改一个<a href=\"https://www.redbrick.dcu.ie/~d_fens/articles/Firefox:_This_Address_is_Restricted\" target=\"_blank\">Firefox配置设置</a>。", "Allow configuring a non-standard port to access the GUI over <i>HTTPS</i>.": "允许配置非标准端口来通过<i>HTTPS</i>访问GUI。", "Allow different groups to be configured with different authentication profiles. Example: all users with a group ID of <i>1</i> will inherit the authentication profile associated with Group <i>1</i>.": "允许不同的群组配置不同的验证配置文件。例子：所有有着群组ID<i>1</i>的用户将会继承群组<i>1</i>的验证配置文件。", "Allow encrypted connections. Requires a certificate  created or imported with the <b>System > Certificates</b> menu.": "允许加密的连接。需要使用<b>系统>证书</b>菜单创建或导入的证书。", "Allow group members to use <a href=\"https://man7.org/linux/man-pages/man8/sudo.8.html\" target=\"_blank\">sudo</a>. Group members are prompted for their password when using <b>sudo</b>.": "允许组使用sudo <a href=\"https://man7.org/linux/man-pages/man8/sudo.8.html\" target=\"_blank\">sudo</a>. 组成员使用 <b>sudo</b> 以提升权限.", "Allow more ciphers for  <a href=\"https://man7.org/linux/man-pages/man8/sshd.8.html\" target=\"_blank\">sshd(8)</a>  in addition to the defaults in  <a href=\"https://man7.org/linux/man-pages/man5/sshd_config.5.html\" target=\"_blank\">sshd_config(5)</a>.  <code>None</code> allows unencrypted SSH connections and  <code>AES128-CBC</code> allows the 128-bit  <a href=\"https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.197.pdf\" target=\"_blank\">Advanced Encryption Standard</a>.<br><br>  WARNING: these ciphers are considered security vulnerabilities and  should only be allowed in a secure network environment.": "允许更多密码  <a href=\"https://man7.org/linux/man-pages/man8/sshd.8.html\" target=\"_blank\">sshd(8)</a>  作为默认的补充  <a href=\"https://man7.org/linux/man-pages/man5/sshd_config.5.html\" target=\"_blank\">sshd_config(5)</a>.  <code>无</code> 允许未经加密的SSH连接 和  <code>AES128-CBC</code> 允许128位  <a href=\"https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.197.pdf\" target=\"_blank\">高级加密标准</a>.<br><br>  警告: 这些密码被认为有安全隐患且仅应当在安全的网络环境中使用。", "Allow non-root mount": "允许非root挂载", "Allow this replication to send large data blocks. The  destination system must also support large blocks. This setting cannot be changed  after it has been enabled and the replication task is created. For more details, see  <a href=\"https://linux.die.net/man/8/zfs\"  target=\"_blank\">zfs(8)</a>.": "允许此副本发送大数据块。 目标系统必须支持大数据块。 此设置在创建副本并启用后无法更改。 参阅  <a href=\"https://linux.die.net/man/8/zfs\"  target=\"_blank\">zfs(8)</a>.以获取更多信息", "Allow using open file handles that can withstand short disconnections.  Support for POSIX byte-range locks in Samba is also disabled. This option is not recommended when  configuring multi-protocol or local access to files.": "允许使用可以承受短时间断开连接的打开文件句柄。Samba中对POSIX字节范围锁的支持也被禁用。配置文件的多协议或本地访问时，建议不要使用此选项。", "Allow {activities}": "允许 {activities}", "Allowed Initiators (IQN)": "允许的启动器（IQN）", "Allowed Services": "允许的服务", "Allowed characters: letters, numbers, underscore (_), and dash (-).": "允许的字符：字母，数字，下划线(_)和破折号(-)。", "Allows multiple  <a href=\"http://www.ntfs.com/ntfs-multiple.htm\" target\"_blank\">NTFS data streams</a>.  Disabling this option causes MacOS to write streams to files on the filesystem.": "允许多个<a href=\"http://www.ntfs.com/ntfs-multiple.htm\" target\"_blank\">NTFS数据流</a>。禁用此选项会使MacOS将流写入文件系统上的文件。", "Allows the share to host user home  directories. Each user is given a personal home directory when  connecting to the share which is not accessible by other users. This  allows for a personal, dynamic share. Only one share can be used  as the home share.": "允许共享托管用户主目录。当连接到共享时，每个用户都会获得一个个人主目录，其他用户无法访问该目录。这允许个人动态共享。只能将一个共享用作家庭共享。", "Alphanumeric <a href=\"https://www.backblaze.com/b2/cloud-storage.html\" target=\"_blank\">Backblaze B2</a> Application Key ID. To generate a new application key, log in to the Backblaze account, go to the <i>App Keys</i> page, and add a new application key. Copy the application <i>keyID</i> string to this field.": "字母数字的<a href=\"https://www.backblaze.com/b2/cloud-storage.html\" target=\"_blank\"> Backblaze B2 </a>应用程序密钥ID。要生成新的应用程序密钥，请登录Backblaze帐户，进入<i> App Keys </i>页面，然后添加一个新的应用程序密钥。将应用程序<i> keyID </i>字符串复制到此字段。", "Also Include Naming Schema": "包括命名惯例", "Also include snapshots with the name": "还包括带有名称的快照", "Also unlock any encrypted dataset stored within this dataset.": "同时解锁该数据集中存储的所有加密数据集。", "Alternative names that SMB clients can use when connecting to this NAS. Can be no greater than 15 characters.": "SMB客户端连接到此NAS时可以使用的备用名称。不得超过15个字符。", "Always": "一直", "Always Chroot": "永远Chroot", "Always On": "永远在线", "Amazon Web Services Key ID. This is found on  <a href=\"https://aws.amazon.com/\" target=\"_blank\">Amazon AWS</a> by  going through <i>My account -> Security Credentials -> Access Keys  (Access Key ID and Secret Access Key)</i>. Must be alphanumeric and  between 5 and 20 characters.": "Amazon Web Services密钥ID。在<a href=\"https://aws.amazon.com/\" target=\"_blank\">Amazon AWS</a>中通过<i>我的帐户->安全凭证->访问密钥（访问密钥ID和秘密访问密钥）</i>上找到。必须为字母数字，且介于5到20个字符之间。", "Amazon Web Services password. If the Secret Access Key cannot be  found or remembered, go to <i>My Account -> Security Credentials ->  Access Keys</i> and create a new key pair. Must be alphanumeric and  between 8 and 40 characters.": "Amazon Web Services密码。如果找不到或忘记了访问密钥，请转到<i>我的帐户->安全凭据->访问密钥</i>并创建一个新的密钥对。必须为字母数字，且介于8到40个字符之间。", "Amount of disk space that can be used by the selected groups.  Entering <code>0</code> (zero) allows all disk space.": "所选组可以使用的磁盘空间量。输入<code> 0 </code>（零）将允许所有磁盘空间。", "Amount of disk space that can be used by the selected users.  Entering <code>0</code> (zero) allows all disk space to be used.": "所选用户可以使用的磁盘空间量。输入<code> 0 </code>（零）将允许使用所有磁盘空间。", "An ACL is detected on the selected path but <i>Enable ACL</i> is not selected for this share.  ACLs must be stripped from the dataset prior to creating an SMB share.": "在所选路径上检测到ACL，但未为此共享选择<i>启用ACL</i>。 在创建SMB共享之前，必须从数据集中删除ACL。", "An ACL is present in this path": "此路径中存在ACL", "An update is already applied. Please reboot the system.": "已应用更新。请重新启动系统。", "Anonymous User Download Bandwidth": "匿名用户下载速度", "Anonymous User Upload Bandwidth": "匿名用户上传速度", "Any notes about initiators.": "关于启动器的任何注释。", "Any system service can communicate externally.": "任何系统服务都可以与外部通信。", "App": "应用", "Appdefaults Auxiliary Parameters": "应用默认值辅助参数", "Append <i>@realm</i> to <i>cn</i> in LDAP queries for both groups and users when User CN is set).": "在设置用户 CN 时，在 LDAP 查询中为组和用户追加<i>@realm</i>到<i>cn</i>。", "Append Data": "追加数据", "Appends a suffix to the share connection path.  This is used to provide unique shares on a per-user, per-computer, or per-IP address basis.  Suffixes can contain a macro. See the  <a href=\"https://www.samba.org/samba/docs/current/man-html/smb.conf.5.html\" target=\"_blank\">smb.conf</a> manual page for  a list of supported macros. The connectpath **must** be preset before a client connects.": "向共享连接路径添加一个前缀。  此操作用于提供基于每个用户，每台计算机，或者每个IP地址的个性化共享。前缀可以包含宏。 参阅  <a href=\"https://www.samba.org/samba/docs/current/man-html/smb.conf.5.html\" target=\"_blank\">smb.conf</a> 以获取可用宏的清单。 连接路径 **必须** 在被客户端连接前设置。", "Application Events": "应用事件", "Application Key": "应用程序密钥", "Application Name": "应用名称", "Applications": "应用", "Applications are not running": "应用未运行", "Applications not configured": "未配置应用", "Applications you install will automatically appear here. Click below and browse the TrueNAS catalog to get started.": "您安装的应用程序将自动出现在此处。 单击下方并浏览TrueNAS目录以开始使用。", "Applied Dataset Quota": "应用数据集配额", "Apply Group": "应用于组", "Apply Owner": "申请所有者", "Apply Pending Updates": "应用更新", "Apply Pending update": "应用更新", "Apply Quotas to Selected Groups": "将配额应用于所选组", "Apply Quotas to Selected Users": "将配额应用于所选用户", "Apply To Groups": "应用于组", "Apply To Users": "应用于用户", "Apply Update": "应用更新", "Apply User": "应用于用户", "Apply permissions recursively": "递归应用权限", "Apply permissions recursively to all child datasets of the current dataset.": "以递归方式将权限应用于当前数据集的所有子数据集。", "Apply permissions recursively to all directories and files in the current dataset.": "递归地将权限应用于当前数据集中的所有目录和文件。", "Apply permissions recursively to all directories and files within the current dataset.": "递归地将权限应用于当前数据集中的所有目录和文件。", "Apply permissions to child datasets": "将权限应用于子数据集", "Apply the same quota critical  alert settings as the parent dataset.": "应用与父数据集相同的配额严重警报设置。", "Apply the same quota warning  alert settings as the parent dataset.": "应用与父数据集相同的配额警告设置。", "Apply updates and reboot system after downloading.": "下载后应用更新并重新启动系统。", "Apps": "应用", "Apr": "四月", "Archive": "存档", "Are you sure you want to abort the <b>{task}</b> task?": "您确定要中止 <b>{task}</b> 任务吗？", "Are you sure you want to delete \"{cert}\"?": "您确定要删除 \"{cert}\"?", "Are you sure you want to delete group <b>\"{name}\"</b>?": "您确定要删除组 <b>\"{name}\"</b> 吗？", "Are you sure you want to delete the <b>{address}</b> NTP Server?": "您确定要删除 <b>{address}</b> NTP 服务器吗？", "Are you sure you want to delete the <b>{name}</b> API Key?": "您确定要删除 <b>{name}</b> API 密钥吗？", "Are you sure you want to delete the group quota <b>{name}</b>?": "确定要删除组配额 <b>{name}</b>？", "Are you sure you want to delete the user quota <b>{name}</b>?": "您确定要删除用户配额 <b>{name}</b> 吗？", "Are you sure you want to delete this idmap?": "您确定要删除此idmap吗？", "Are you sure you want to delete user <b>\"{user}\"</b>?": "您确定要删除用户 <b>\"{user}\"</b> 吗？", "Are you sure you want to deregister TrueCommand Cloud Service?": "您确定要注销TrueCommand云服务吗？", "Are you sure you want to stop connecting to the TrueCommand Cloud Service?": "您确定要停止连接到TrueCommand云服务吗？", "Are you sure you want to sync from peer?": "是否确定要从对等节点同步？", "Are you sure you want to sync to peer?": "是否确定要同步到对等节点？", "Are you sure?": "你确定吗？", "Arguments": "参数", "Arrange the disks according to capacity, redundancy, and  performance considerations. More types become available as more disks are added to the vdev.<br>  A <i>Stripe</i> uses the entire capacity of the disks for storage and <b>has no redundancy</b>.  Failed or degraded disks in a stripe can result in data loss!<br> A <i>Mirror</i> requires at  least two disks and mirrors the data from one disk onto each other disk in the vdev, which can  limit the total capacity.<br><i>Raid-Z</i> configurations offer different balances of data  redundancy and total capacity for the selected disks.": "根据容量，冗余和性能方面的考虑排列磁盘。随着将更多磁盘添加到vdev，更多类型可用。<br> <i>条带</i>使用磁盘的全部容量进行存储，但是<b>没有冗余</b>。条带中的某一个磁盘出现故障或降级都可能导致数据丢失！<br> <i>镜像</i>至少需要两个磁盘，并将数据从一个磁盘镜像到vdev中的另一个磁盘，这可能会限制总容量。<br> <i> Raid-Z </i>配置所选磁盘并提供了不同的数据冗余平衡和总容量平衡。", "Associated Targets": "相关目标", "At least 1 GPU is required by the host for it’s functions.": "宿主机需要至少一个GPU以正常运作。", "At least one module must be defined in <a                href=\"https://www.samba.org/ftp/rsync/rsyncd.conf.html\"                target=\"_blank\">rsyncd.conf(5)</a> of the rsync                server or in the <b>Rsync Modules</b> of another                system.": "必须在<a                href=\"https://www.samba.org/ftp/rsync/rsyncd.conf.html\"                target=\"_blank\">rsyncd.conf(5)</a>中定义至少一个模块或另一个系统的<b>Rsync模块</b>中定义一个模块。", "At least one pod must be available": "必须至少有一个Pod可用", "At least one pool must be available to use apps": "必须至少有一个池才能使用应用", "Attach": "附加", "Attach Debug": "附加调试", "Attach NIC": "附加网卡", "Attach screenshots": "添加截屏", "Attention": "注意", "Aug": "八月", "Auth Token": "验证令牌", "Auth Token from alternate authentication - optional <a href=\"https://rclone.org/swift/#standard-options\" target=\"_blank\">(rclone documentation)</a>.": "来自备用身份验证的身份验证令牌-可选<a href=\"https://rclone.org/swift/\\#standard-options\" target=\"_blank\">（rclone文档）</a>。", "AuthVersion": "验证版本", "AuthVersion - optional - set to (1,2,3) if your auth URL has no version <a href=\"https://rclone.org/swift/#standard-options\" target=\"_blank\">(rclone documentation)</a>.": "验证版本-可选-如果您的身份验证URL没有版本，则设置为（1,2,3）。请参阅<a href=\"https://rclone.org/swift/\\#standard-options\" target=\"_blank\">（rclone文档）</a>。", "Authentication": "认证方式", "Authentication Algorithm": "认证算法", "Authentication Group Number": "身份验证组号", "Authentication Method": "身份验证方法", "Authentication Method and Group": "身份验证方法和组", "Authentication Protocol": "认证协议", "Authentication Type": "认证类型", "Authentication URL": "认证URL", "Authentication URL for the server. This is the OS_AUTH_URL from an <a href=\"https://rclone.org/swift/#configuration-from-an-openstack-credentials-file\" target=\"_blank\">OpenStack credentials file</a>.": "服务器的认证URL。这是来自OS_AUTH_URL的<a href=\"https://rclone.org/swift/\\#configuration-from-an-openstack-credentials-file\" target=\"_blank\"> OpenStack凭证文件</a>。", "Authenticator": "身份验证器", "Authenticator to validate the Domain. Choose a previously configured ACME DNS authenticator.": "身份验证域。 选择一个先前配置的ACME DNS身份验证器。", "Authority Cert Issuer": "权威证书签发", "Authority Key Config": "授权密钥配置", "Authority Key Identifier": "授权密钥标识符", "Authorized Access": "授权访问", "Authorized Hosts and IP addresses": "授权的主机和IP地址", "Authorized Keys": "授权密钥", "Authorized Networks": "授权网络", "Auto": "自动", "Auto Refresh": "自动刷新", "Auto TRIM": "自动TRIM", "Autoconfigure IPv6": "自动配置IPv6", "Automatic update check failed. Please check system network settings.": "自动更新检查失败。请检查系统网络设置。", "Automatically populated with the original hostname of the system. This name is limited to 15 characters and cannot be the <b>Workgroup</b> name.": "使用系统的原始主机名自动填充。此名称限制为15个字符，不能以<b>Workgroup</b>为名称。", "Automatically reboot the system after the update is applied.": "应用更新后，自动重启系统。", "Automatically stop the script or command after the specified seconds.": "在指定的秒数后自动停止脚本或命令。", "Autostart": "自动启动", "Auxiliary Arguments": "附加参数", "Auxiliary Groups": "附加组", "Auxiliary Parameters": "附加参数", "Auxiliary Parameters (ups.conf)": "附加参数 （ups.conf）", "Auxiliary Parameters (upsd.conf)": "附加参数 （upsd.conf）", "Auxiliary parameters": "附加参数", "Available": "可用的", "Available Applications": "可用的应用", "Available Disks": "可用磁盘", "Available Memory:": "可用内存：", "Available Space": "可用空间", "Available Space Threshold (%)": "可用空间阈值（％）", "Available version:\n": "可用版本：\n", "Average Disk Temperature": "平均磁盘温度", "Avg Usage": "平均使用量", "Back": "返回", "Back to Support": "返回支持", "Back up the encryption key now! If the key is lost, the data on the disks will also be lost with no hope of recovery. Click <b>Download Encryption Key</b> to begin the download. This type of encryption is for users storing sensitive data. iXsystems, Inc. cannot be held responsible for any lost or unrecoverable data as a consequence of using this feature.": "请立刻备份加密密钥！如果密钥丢失，磁盘上的数据将一并丢失且不可恢复。点击 <b>下载密钥</b> 以开始下载。 此类加密用于用户存储敏感数据。iXsystems, Inc. 不对使用此功能造成的任何数据丢失负责。", "Backend": "后端", "Backend used to map Windows security  identifiers (SIDs) to UNIX UIDs and GIDs. To configure the selected  backend, click <i>EDIT IDMAP</i>.": "后端用于将Windows安全标识符（SID）映射到UNIX UID和GID。要配置选定的后端，请单击<i> 编辑IDMAP </i>。", "Background (lowest)": "背景（最低）", "Backup Credentials": "备份凭据", "Bandwidth": "带宽", "Bandwidth Limit": "带宽限制", "Base DN": "基本 DN", "Base Name": "基本名称", "Base64 encoded key for the Azure account.": "Azure帐户的Base64编码密钥。", "Basic": "基本", "Basic Constraints": "基本约束", "Basic Constraints Config": "基本约束配置", "Basic Info": "基本信息", "Basic Mode": "基本模式", "Basic Options": "基本选项", "Basic Settings": "基本设置", "Batch Operations": "批处理", "Before updating, please read the release notes.": "在更新之前，请阅读发行说明。", "Begin": "开始", "Best effort (default)": "尽可能（默认）", "Bind": "绑定", "Bind DN": "绑定 DN", "Bind IP Addresses": "绑定IP地址", "Bind Interfaces": "绑定接口", "Bind Password": "绑定密码", "Block (iSCSI) Shares Targets": "块 (iSCSI) 共享目标", "Block Size": "块大小", "Block size": "块大小", "Body": "部分", "Boot": "启动", "Boot Environments": "引导环境", "Boot Loader Type": "引导加载程序类型", "Boot Method": "启动方法", "Boot Pool Condition": "引导池条件", "Boot Pool Status": "启动池状态", "Boot environment name. Alphanumeric characters, dashes (-), underscores (_),     and periods (.) are allowed.": "引导环境名称。允许使用字母数字字符，破折号(-)，下划线(_)和句点(.)。", "Boot environment to be cloned.": "要克隆的引导环境。", "Bot API Token": "机器人 API 令牌", "Brainpool curves can be more secure, while secp curves can be faster. See <a href=\"https://tls.mbed.org/kb/cryptography/elliptic-curve-performance-nist-vs-brainpool\" target=\"blank\"> Elliptic Curve performance: NIST vs Brainpool </a> for more information.": "Brainpool曲线可以更安全，而secp曲线可以更快。参见<a href=\"https://tls.mbed.org/kb/cryptography/elliptic-curve-performance-nist-vs-brainpool\" target=\"blank\">椭圆曲线性能：NIST与Brainpool</a>了解更多信息。", "Branch": "分支", "Bridge": "桥接", "Bridge Members": "桥接成员", "Bridge Settings": "桥接设置", "Bridge interface": "桥接接口", "Browsable to Network Clients": "允许网络客户端浏览", "Browse to a CD-ROM file present on the system storage.": "浏览系统存储中存在的CD-ROM文件。", "Browse to a storage location and add the name of the new raw file on the end of the path.": "浏览存储位置，并在路径末尾添加新原始文件的名称。", "Browse to an <b>existing</b> directory to use for storage. Some devices can require a specific directory name. Consult the documentation for that device to see if there are any restrictions.": "浏览一个<b>现有</b>文件夹用于存储。某些设备可能需要特定的目录名称。请参阅该设备的文档，了解是否有任何限制。", "Browse to an existing file. Create a new file by browsing to a dataset and appending /<i>'{'filename.ext'}'</i> to the path. Extents cannot be created inside a jail root directory.": "浏览到现有文件。通过浏览到数据集并将/<i>'{'filename.ext'}'</i>附加到路径来创建新文件。无法在监狱根目录中创建范围。", "Browse to an existing pool or dataset to store the new zvol.": "浏览现有池或数据集以存储新的zvol。", "Browse to the ZFS dataset that will hold the copied data.": "浏览将保存复制数据的ZFS数据集。", "Browse to the desired zvol on the disk.": "浏览磁盘上所需的zvol。", "Browse to the directory for the S3 filesystem.": "浏览S3文件系统的目录。", "Browse to the existing path on the remote host to                sync with. Maximum path length is 255 characters": "浏览远程主机上的现有路径以进行同步。最大路径长度为255个字符", "Browse to the exported key file that can be used to unlock this      dataset.": "浏览可用于解锁此数据集的导出密钥文件。", "Browse to the installer image file and click <b>Upload</b>.": "浏览安装程序映像文件，然后单击<b>上传</b>。", "Browse to the keytab file to upload.": "浏览要上传的密钥表文件。", "Browse to the operating system installer image file.": "浏览操作系统安装镜像文件。", "Browse to the path to be copied. Linux file path limits apply. Other operating systems can have different limits which might affect how they can be used as sources or destinations.": "浏览需要复制的路径。Linux文件路径限制应用。其他操作系统也可能包含限制其作为源或终点的限制。", "Browse to the pool or dataset to share.": "浏览要共享的池或数据集。", "Browse to the pool or dataset to store received  data.": "浏览池或数据集以存储接收到的数据。", "Bucket": "存储桶", "Bucket Policy Only": "仅存储桶策略", "Bucket name": "存储桶名称", "Bug": "漏洞", "Builtin": "内置", "Bulk Actions": "批量操作", "Bulk Edit Disks": "批量编辑磁盘", "Bulk Operations": "批量操作", "Bulk actions": "批量操作", "Burst": "突发", "By default, Samba uses a hashing algorithm for NTFS illegal  characters. Enabling this option translates NTFS illegal characters to the Unicode private range.": "默认情况下，Samba对NTFS非法字符使用哈希算法。启用此选项会将NTFS非法字符转换为Unicode私有范围。", "By default, the VM receives an auto-generated random MAC address. Enter a custom address into the field to override the default. Click <b>Generate MAC Address</b> to add a new randomized address into this field.": "在默认情况下，虚拟机将会获得一个自动生成的随机MAC地址。输入一个自定义的地址来覆盖默认地址。点击<b>生成MAC地址</b>添加一个随机MAC地址到这个区域。", "CA": "认证机构", "CAUTION: Once Two-Factor Authentication is enabled, a One-Time Password (OTP)  will be required to sign into this system. Be sure to immediately set up another two-factor device.": "注意：启用“双重身份验证”后，将需要使用一次性密码（OTP）来登录此系统。确保立即设置另一个双重设备。", "CC": "CC", "CD-ROM": "光盘", "CD-ROM Path": "CD-ROM路径", "CLOSE": "关闭", "CN": "国家", "CN Realm": "CN 领域", "CONNECT": "连接", "CONTACT SUPPORT": "联系支持", "CONVEYANCE": "运输", "COPY": "复制", "CPU": "处理器", "CPU Details": "CPU详情", "CPU Mode": "CPU模式", "CPU Model": "CPU型号", "CPU and Memory": "处理器和内存", "CPUs and Memory": "处理器和内存", "CRITICAL": "危急", "CRL Sign": "CRL签名", "CSR exists on this system": "此系统上存在CSR", "CSRs": "CSRs", "Cache": "缓存", "Cache VDEVs": "缓存 VDEV", "Cache VDev": "缓存VDev", "Caches": "缓存", "Can be set to <i>0</i>, left empty for TrueNAS to assign a port when the VM is started, or set to a fixed, preferred port number.": "可以设置为 <i>0</i>，留空让TrueNAS在虚拟机启动时分配端口，或设置为固定的首选端口号。", "Can not retrieve response": "无法检索到响应", "Cancel": "取消", "Cancel any pending Key synchronization.": "取消任何挂起的密钥同步。", "Canceled Resilver on {date}": "{date}取消了重新同步", "Canceled Scrub on {date}": "{date}取消了擦除", "Cannot Delete while HA is Enabled": "启用 HA 时无法删除", "Cannot Edit while HA is Enabled": "启用 HA 时无法编辑", "Cannot allocate {size} to storage for this virtual machine.": "无法为此虚拟机分配 {size} 存储空间。", "Cannot edit while HA is enabled.": "启用 HA 时无法编辑", "Capacity": "容量", "Capacity Settings": "容量设置", "Case Sensitivity": "区分大小写", "Catalog": "目录", "Catalog Name": "目录名", "Catalogs": "目录", "Categories": "类别", "Category": "类别", "Caution: Allocating too much memory can slow the  system or prevent VMs from running.": "注意：分配过多的内存可能会降低系统速度或阻止虚拟机运行。", "Certificate": "证书", "Certificate Authorities": "证书颁发机构", "Certificate Authority": "证书颁发机构", "Certificate Authority (CA) to use for connecting to the key server. A valid CA public certificate is required to authenticate the connection. WARNING: for security reasons, please protect the Certificate Authority used for key server authentication.": "证书颁发机构（CA）用于连接到密钥服务器。需要有效的CA公共证书来验证连接。警告：出于安全原因，请保护用于密钥服务器身份验证的证书颁发机构。", "Certificate Options": "证书选项", "Certificate Signing Request": "证书签名请求", "Certificate Signing Requests": "证书签名请求", "Certificate Subject": "证书主题", "Certificate to use for key server authentication. A valid certificate is required to verify the key server connection. WARNING: for security reasons, please protect the Certificate used for key server authentication.": "用于密钥服务器身份验证的证书。需要有效证书来验证密钥服务器连接。警告：出于安全原因，请保护用于密钥服务器身份验证的证书。", "Certificate to use when performing LDAP  certificate-based authentication. To configure LDAP certificate-based  authentication, create a Certificate Signing Request for the LDAP  provider to sign. A certificate is not required when using  username/password or Kerberos authentication.": "执行基于LDAP证书的身份验证时要使用的证书。要配置基于LDAP证书的身份验证，请创建一个证书签名请求以供LDAP提供程序进行签名。使用用户名/密码或Kerberos身份验证时，不需要证书。", "Certificates": "证书", "Change Enclosure Label": "更改机柜标签", "Change Password": "更改密码", "Change Server": "更改服务器", "Change Settings and Clear Report History?": "更改设置并清除报告历史记录？", "Change User & Group Ownership": "更改用户和组的所有权", "Change existing ownership of ALL files in the share  to user <samp>webdav</samp> and group <samp>webdav</samp>. If unset,  ownership of files to be accessed through WebDAV must be manually set  to the <samp>webdav</samp> or <samp>www</samp> user/group.": "将共享中所有文件的现有所有权更改为用户<samp>webdav</samp>和组<samp>webdav</samp>。如果未设置，则必须手动将要通过 WebDAV 访问的文件的所有权设置为<samp>webdav</samp>或<samp>www</samp>用户/组。", "Change from <i>public</i> to increase system security. Can only contain alphanumeric characters, underscores, dashes, periods, and spaces. This can be left empty for <i>SNMPv3</i> networks.": "从<i>public</i>更改以提高系统安全性。只能包含字母数字字符、下划线、破折号、句点和空格。对于<i>SNMPv3</i>网络，可以留空。", "Change log": "更改日志", "Change order for <b>{vmDevice}</b>": "更改 <b>{vmDevice}</b> 的顺序", "Change the default password to improve system security. The new password cannot contain a space or <b>#</b>.": "修改默认密码以提高系统安全性。新的密码不能包含空格或<b>\\#</b>。", "Changelog": "变更日志", "Changes Saved": "更改已保存", "Changes to <em>Hosts Allow</em> or <em>Hosts Deny</em> take effect when the  SMB service restarts.": "对 <em>允许主机</em>或<em>拒绝主机</em> 的更改在 SMB 服务重新启动时生效。", "Changes to ACL type affect how on-disk ZFS ACL is  written and read.\nWhen the ACL type is changed from POSIX to NFSv4,  no migration is performed for default and access ACLs encoded in the  posix1e acl extended attributes to native ZFS ACLs.\nWhen ACL type is  changed from NFSv4 to POSIX, native ZFS ACLs are not converted to  posix1e extended attributes, but the native ACL will be used internally  by ZFS for access checks.\n\nThis means that the user must manually set  new ACLs recursively on the dataset after ACL type changes in order to  avoid unexpected permissions behavior.\n\nThis action will be destructive,  and so it is advised to take a ZFS snapshot of the dataset prior to ACL  type changes and permissions modifications.": "对ACL类型的更改会影响写入和读取磁盘上ZFS ACL的方式。\n当ACL类型从POSIX更改为NFSv4时，不会将默认ACL和在posix1e acl扩展属性中编码的访问ACL迁移到本机ZFS ACL。\n当ACL类型从NFSv4更改为POSIX时，本机ZFS ACL不会转换为posix1e扩展属性，但本机ACL将由ZFS在内部用于访问检查。\n\n这意味着用户必须在ACL类型更改后的数据集，以避免意外的权限行为。\n\n此操作将具有破坏性，因此建议在ACL类型更改和权限修改之前对数据集进行 ZFS 快照。", "Changing Advanced settings can be dangerous when done incorrectly. Please use caution before saving.": "如果操作不当，更改高级设置可能会很危险。保存前请谨慎使用。", "Changing dataset permission mode can severely affect existing permissions.": "更改数据集权限模式可能会严重影响现有权限。", "Changing settings below will result in Kubernetes cluster re-initialization deleting installed apps and their data.": "更改以下设置将导致Kubernetes集群重新初始化并删除已安装的应用程序及其数据。", "Changing to a nightly train is one-way. Changing back to a stable train is not supported! ": "更改为nightly分支是单向的。不支持更改回稳定的分支！", "Channel": "向导", "Channel {n}": "频道{n}", "Check Alerts for more details.": "查看警报以获取更多详细信息。", "Check Interval": "检查Interval", "Check Now": "立即检查", "Check Release Notes": "检查发行说明", "Check for Updates": "检查更新", "Check for Updates Daily and Download if Available": "每天检查更新并在可用时下载", "Check the box for full upgrade. Leave unchecked to download only.": "选中复选框以进行完整升级。取消选中仅下载。", "Check the update server daily for                                   any updates on the chosen train.                                   Automatically download an update if                                   one is available. Click                                   <i>APPLY PENDING UPDATE</i> to install                                   the downloaded update.": "每天检查更新服务器，获取选择分支的更新。在一个更新可用时将会自动下载。点击<i>应用更新</i>安装已下载的更新。", "Check this box if importing a certificate for which a CSR exists on this system": "如果导入此系统上存在的 CSR 证书，请选中此框", "CheckIP Path": "CheckIP路径", "CheckIP Server": "CheckIP服务器", "CheckIP Server SSL": "CheckIP服务器SSL", "Checking HA status": "正在检查HA状态", "Checksum": "校验和", "Checksum Errors": "校验错误", "Child Shares": "子集", "Children": "子集", "Choices are <i>None, Auto, CHAP,</i> or <i>Mutual CHAP</i>.": "可以从<i>无，自动，CHAP，</i> 或 <i>Mutual CHAP</i>中选择。", "Choose": "选择", "Choose <i>AES-256</i> or <i>None</i>.": "选择<i>AES-256</i> 或 <i>无</i>。", "Choose <i>Master</i> if the UPS is plugged directly into the system serial port. The UPS will remain the last item to shut down. Choose <i>Slave</i> to have this system shut down before <i>Master</i>. See the <a href=\"https://networkupstools.org/docs/user-manual.chunked/ar01s02.html#_monitoring_client\" target=\"_blank\">Network UPS Tools Overview</a>.": "如果UPS直接插入系统串行端口，则选择<i>主</i>。UPS 将是最后一个要关闭的项目。选择<i>从属</i>使系统在<i>主</i>之前关闭。请参阅<a href=\"https://networkupstools.org/docs/user-manual.chunked/ar01s02.html\\#_monitoring_client\" target=\"_blank\">网络UPS工具概述</a>。", "Choose <i>ON</i> to update the access time for files when they are read. Choose <i>Off</i> to prevent producing log traffic when reading files. This can result in significant performance gains.": "选择<i>ON</i>来更新读取文件时的访问时间。选择<i>Off</i>可以防止在读取文件时产生日志流量。这可以带来明显性能提升。", "Choose File": "选择文件", "Choose Pool": "选择池", "Choose a DNS provider and configure any required authenticator attributes.": "选择DNS提供程序并配置任何必需的身份验证器属性。", "Choose a Disk": "选择一个磁盘", "Choose a Tag": "选择标签", "Choose a cipher algorithm to encrypt data channel packets.": "选择一种加密算法来加密数据通道数据包。", "Choose a compression algorithm.": "选择一种压缩算法。", "Choose a connection that has been saved in  <b>System > SSH Connections</b>.": "选择已保存在<b>系统>SSH连接</b>中的连接。", "Choose a date format.": "选择日期格式。", "Choose a location to store the installer image file.": "选择一个位置来保存安装镜像文件。", "Choose a new disk for the pool. To protect any existing data, adding the selected disk is stopped when the disk is already in use or has partitions present.": "为池选择一个新磁盘。为了保护任何现有数据，当磁盘已在使用中或存在分区时，将停止添加所选磁盘。", "Choose a path to the user's home directory. If the directory exists and matches the username, it is set as the user's home directory. When the path does not end with a subdirectory matching the username, a new subdirectory is created. The full path to the user's home directory is shown here when editing a user.": "选择用户主目录的路径。如果目录存在并且与用户名匹配，则将其设置为用户的主目录。当路径的结尾不是与用户名匹配的子目录时，将创建一个新的子目录。编辑用户时，此处显示用户主目录的完整路径。", "Choose a pool for Apps": "选择应用程序池", "Choose a pool to scrub.": "选择要校验的池。", "Choose a privacy protocol.": "选择隐私协议。", "Choose a recent IP address to limit the usage when accessing the administrative GUI. The built-in HTTP server binds to the wildcard address of <i>0.0.0.0</i> (any address) and issues an alert if the specified address becomes unavailable.": "选择一个最新的IP地址以限制访问管理GUI时的使用。内置的HTTP服务器绑定到<i> 0.0.0.0 </i>的通配符地址（任何地址），并在指定的地址不可用时发出警报。", "Choose a recent IPv6 address to limit the usage when accessing the administrative GUI. The built-in HTTP server binds to the wildcard address of <i>0.0.0.0</i> (any address) and issues an alert if the specified address becomes unavailable.": "选择一个最新的IPv6地址以限制访问管理GUI时的使用。内置的HTTP服务器绑定到<i> 0.0.0.0 </i>的通配符地址（任何地址），并在指定的地址不可用时发出警报。", "Choose a safety level for the  rollback action. The rollback is canceled when the safety check finds  additional snapshots that are directly related to the dataset being rolled back.": "选择回滚操作的安全级别。当安全检查找到与正在回滚的数据集直接相关的其他快照时，将取消回滚。", "Choose a saved SSH Keypair or select  <i>Generate New</i> to create a new keypair and use it for this  connection.": "选择一个已保存的SSH密钥对或选择<i>新建</i>来创建一个新的密钥对并将其用于此连接。", "Choose a temporary location for the encryption key that will decrypt replicated data.": "为将解密复制数据的加密密钥选择一个临时位置。", "Choose a time format.": "选择时间格式。", "Choose a valid client certificate which exists on this system and hasn't been revoked. Find more about generating certificates and CAs for OpenVPN <a href=\"https://community.openvpn.net/openvpn/wiki/HOWTO#SettingupyourownCertificateAuthorityCAandgeneratingcertificatesandkeysforanOpenVPNserverandmultipleclients\" target=\"_blank\">here.</a>": "选择一个有效的客户端证书，该证书存在于该系统上，并且未被吊销。在<a href=\"https://community.openvpn.net/openvpn/wiki/HOWTO\\#SettingupyourownCertificateAuthorityCAandgeneratingcertificatesandkeysforanOpenVPNserverandmultipleclients\" target=\"_blank\">此处</a>查找有关为OpenVPN生成证书和CAs的更多信息。", "Choose a virtual network interface. More information can be found  <a href=\"https://community.openvpn.net/openvpn/wiki/BridgingAndRouting\" target=\"_blank\">here</a>.": "选择一个虚拟网络接口。可以在<a href=\"https://community.openvpn.net/openvpn/wiki/BridgingAndRouting\" target=\"_blank\">此处</a>中找到更多信息。", "Choose an alert service to display options for that service.": "选择警报服务以显示该服务的选项。", "Choose an algorithm to authenticate packets.": "选择一种算法对数据包进行身份验证。", "Choose an authentication method.": "选择身份验证方法。", "Choose an encryption mode to use with LDAP.": "选择要用于LDAP的加密模式。", "Choose between <i>Internal CA</i>, <i>Intermediate CA</i>, and  <i>Import CA</i>. An <i>Internal CA</i> functions like a publicly trusted CA  to sign certificates for an internal network. They are not trusted outside  the private network. An <i>Intermediate CA</i> lives between the root and end  entity certificates and its main purpose is to define and authorize the  types of certificates that can be requested from the root CA. <i>Import CA</i>  allows an existing CA to be imported onto the system.<br>  For more information see  <a href=\"https://www.globalsign.com/en/blog/what-is-an-intermediate-or-subordinate-certificate-authority\">  What are Subordinate CAs and Why Would You Want Your Own?</a>": "在<i>内部 CA</i>、<i>中间 CA</i> 和 <i>导入 CA</i> 之间进行选择。 <i>内部 CA</i> 的功能类似于公共信任的 CA，可为内部网络签署证书。 它们在专用网络之外不受信任。 <i>中间 CA</i> 位于根和最终实体证书之间，其主要目的是定义和授权可以从根 CA 请求的证书类型。 <i>导入 CA</i> 允许将现有 CA 导入系统。<br>有关详细信息，请参阅<a href=\"https://www.globalsign.com/en/blog/what-is -an-intermediate-or-subordinate-certificate-authority\">什么是从属 CA，为什么要拥有自己的 CA？</a>", "Choose between a <i>Hex</i> (base 16 numeral) or <i>Passphrase</i> (alphanumeric) style encryption key.": "在 <i>Hex</i>（基数为 16 的数字）或 <i>密码</i>（字母数字）样式的加密密钥之间进行选择。", "Choose container": "选择容器", "Choose how many log entries to create. Choices range from the least log entries (<i>Emergency</i>) to the most (<i>Debug</i>).": "选择要创建的日志条目数。选择范围从数量最少的日志条目（<i>Emergency</i>）到数量最多的（<i>Debug</i>）。", "Choose if the .zfs snapshot directory is <i>Visible</i> or <i>Invisible</i> on this dataset.": "选择此数据集上的 .zfs 快照目录是<i>可见</i>还是<i>不可见</i>。", "Choose log": "选择日志", "Choose one of the presets  or choose <i>Custom</i> to use the advanced scheduler.": "选择一种预设，或选择<i>自定义</i>使用高级计划程序。", "Choose one of the presets or select <i>Custom</i>  to open the advanced scheduler.": "选择一种预设，或选择<i>自定义</i>以打开高级计划程序。", "Choose permissions for this rsync module.": "选择此rsync模块的权限。", "Choose pod": "选择pod", "Choose the VM operating system type.": "选择虚拟机操作系统类型。", "Choose the backend to map Windows security identifiers (SIDs) to UNIX UIDs and GIDs. Click Edit to configure that backend.": "选择后端以将 Windows 安全标识符 （SID） 映射到 UNIX UID 和 GID。单击“编辑”以配置该后端。", "Choose the hour and minute when new resilver tasks                are not allowed to start. This does not affect active                resilver tasks.": "选择不允许启动新的重新同步任务的小时和分钟。这不会影响活动的重新同步任务。", "Choose the hour and minute when resilver tasks can be                started.": "选择可以启动重新同步任务时的小时和分钟。", "Choose the platform that will use this share. The associated options are applied to this share.": "选择将使用此共享的平台。关联的选项将应用于此共享。", "Choose the protocol to use when connecting with the remote system.": "选择与远程系统连接时要使用的协议。", "Choose the root Certificate Authority that was used to sign the Client and Server certificates.         Find more about generating certificates and CAs for OpenVPN  <a href=\"https://community.openvpn.net/openvpn/wiki/HOWTO#SettingupyourownCertificateAuthorityCAandgeneratingcertificatesandkeysforanOpenVPNserverandmultipleclients\" target=\"_blank\">here.</a>": "选择用于签署客户端和服务器证书的根证书颁发机构。在<a href=\"https://community.openvpn.net/openvpn/wiki/HOWTO\\#SettingupyourownCertificateAuthorityCAandgeneratingcertificatesandkeysforanOpenVPNserverandmultipleclients\" target=\"_blank\">此处</a>查找有关为OpenVPN生成证书和CAs的更多信息。", "Choose the schema to use when querying AD for user/group info. <i>rfc2307</i> uses the schema support included in Windows 2003 R2, <i>sfu</i> is for Service For Unix 3.0 or 3.5, and <i>sfu20</i> is for Service For Unix 2.0.": "选择查询用户/组信息的 AD 时要使用的架构。<i>rfc2307</i>使用 Windows 2003 R2 中包含的架构支持，<i>sfu</i> 用于 Unix 3.0 或 3.5 的服务，<i>sfu20</i> 用于 Unix 2.0 服务。", "Choose the schema to use with LDAP authentication for SMB shares. The LDAP server must be configured with Samba attributes to use a Samba Schema.  Options include <i>RFC2307</i> (included in Windows 2003 R2) and <i>Service for Unix (SFU)</i>.  For SFU 3.0 or 3.5, choose \"SFU\". For SFU 2.0, choose \"SFU20\".": "选择用于SMB共享的LDAP身份验证的架构。LDAP服务器必须配置有Samba属性才能使用Samba模式。选项包括<i> RFC2307 </i>（包含在Windows 2003 R2中）和<i> Unix服务（SFU）</i>。对于SFU 3.0或3.5，选择\"SFU\"。对于SFU 2.0，选择\"SFU20\"。", "Choose the speed in bps used by the serial port.": "选择串行端口使用的速度（以bps为单位）。", "Choose the test type. See <a                href=\"https://www.smartmontools.org/browser/trunk/smartmontools/smartctl.8.in\"                target=\"_blank\">smartctl(8)</a> for descriptions of                each type. Some types will degrade performance or                take disks offline. Avoid scheduling S.M.A.R.T. tests                simultaneously with scrub or resilver operations.": "选择测试类型。请参阅<a                href=\"https://www.smartmontools.org/browser/trunk/smartmontools/smartctl.8.in\"                target=\"_blank\">smartctl(8)</a>中有关每种类型的说明。一些类型会降低性能或使磁盘下线。避免S.M.A.R.T. 测试和数据校验或传送操作任务同时运行", "Choose the type of filesystem on the disk.": "选择磁盘上的文件系统类型。", "Choose the type of interface. <i>Bridge</i> creates a logical link between multiple networks. <i>Link Aggregation</i> combines multiple network connections into a single interface. A <i>Virtual LAN (VLAN)</i> partitions and isolates a segment of the connection. Read-only when editing an interface.": "选择接口类型。<i>网桥</i>在多个网络之间创建逻辑链接。<i>链路聚合</i>将多个网络连接合并到一个接口中。<i>虚拟 LAN （VLAN）</i>分开并隔离连接段。编辑界面时为只读。", "Choose the type of permissions. <i>Basic</i> shows general permissions. <i>Advanced</i> shows each specific type of permission for finer control.": "选择权限类型。<i>基础</i>显示常规权限。<i>高级</i>显示每种特定类型的权限，以进行更精细的控制。", "Choose the type that matches the type of client accessing the pool/dataset.": "选择与访问池/数据集的客户端类型相匹配的类型。", "Choose to connect using either SSH private key stored   in user's home directory or SSH connection from the keychain": "选择使用存储在用户主目录中的 SSH 私钥或钥匙串中的 SSH 连接进行连接", "Choose to either use a custom-defined remote module  of the rsync server or to use an SSH configuration for the rsync task.": "选择使用rsync服务器的自定义远程模块，还是将SSH配置用于rsync任务。", "Choose when the UPS initiates shutdown.": "选择UPS发起关机的时间。", "Choosing an entry loads a preset ACL that is  configured to match general permissions situations.": "选择条目将加载配置为与常规权限情况匹配的预设ACL。", "Cipher": "密码", "City": "城市", "Classification for each S3 object. Choose a class based on the specific use case or performance requirements. See <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/storage-class-intro.html\" target=\"_blank\">Amazon S3 Storage Classes</a> for more information.": "每个S3对象的分类。根据特定的用例或性能要求选择一个类。请参见<a href=\"https://docs.aws.amazon.com/AmazonS3/latest/dev/storage-class-intro.html\" target=\"_blank\">Amazon S3存储类</a>了解更多信息。", "Clear": "清除", "Clear SED Password": "清除SED密码", "Clear Sync Keys": "清除同步秘钥", "Clear the Idmap Cache": "清除Idmap缓存", "Clear the SED password for this disk.": "清除该磁盘的SED密码。", "Clearing Cache...": "正在清除缓存...", "Click an item to view NFSv4 permissions": "单击项目以查看 NFSv4 权限", "Click for information on    <a href=\"https://www.truenas.com/docs/truenasupgrades/\" target=\"_blank\">TrueNAS SCALE Migration, Nightly trains    and other upgrade options.</a>": "点击 <a href=\"https://www.truenas.com/docs/truenasupgrades/\" target=\"_blank\">以获取关于TrueNAS SCALE迁移，每夜更新通道以及更多升级选项.</a>", "Clicking Continue allows TrueNAS to finish the update in the background while  Abort stops the process and reverts the dataset ACL to the previously active ACL.": "单击继续允许TrueNAS在后台完成更新，而中止停止该过程并将数据集ACL恢复到以前活动的ACL。", "Client Certificate": "客户证书", "Client Name": "客户端名称", "Clients": "客户端", "Clone": "克隆", "Clone Boot Environment": "克隆引导环境", "Clone To New Dataset": "克隆到新数据集", "Clone to New Dataset": "克隆到新数据集", "Close": "关闭", "Close expanded row": "关闭扩展行", "Close panel": "关闭面板", "Cloud Credentials": "云凭据", "Cloud Sync Task": "云同步任务", "Cloud Sync Tasks": "云同步任务", "Cloud credential added.": "添加了云凭证。", "Cloud credential updated.": "云凭证已更新。", "Cloud sync <i>{taskName}</i> stopped.": "云端同步 <i>{taskName}</i> 已停止。", "Cloud sync «{name}» has started.": "云同步 «{name}» 已开始。", "Cloud sync «{name}» stopped.": "云同步 «{name}» 已停止。", "Cluster CIDR": "集群CIDR", "Cluster DNS IP": "集群DNS IP", "Collapse": "折叠", "Collapse Row": "解散行", "Columns": "列", "Combine multiple, critical-for-failover  interfaces into a group. Groups apply to single systems. A failover  occurs when every interface in the group fails. Groups with a single  interface trigger a failover when that interface fails. Configuring the  system to failover when any interface fails requires marking each  interface as critical and placing them in separate groups.": "将多个故障转移关键接口组合在一起。组适用于单个系统。组中的每个接口均发生故障时，将发生故障转移。具有单个接口的组会在该接口发生故障时触发故障转移。将系统配置为在任何接口出现故障时进行故障转移都需要将每个接口标记为关键接口，并将它们放在单独的组中。", "Command": "命令", "Commands": "命令", "Comment": "注释", "Comments": "注释", "Comments about this script.": "关于此脚本的注释。", "Common": "常见的", "Common Name": "通用名", "Community": "社区", "Compatibility": "兼容性", "Complete the Upgrade": "完成升级", "Completed S.M.A.R.T. Tests": "完成 S.M.A.R.T. 测试", "Completely replicate the selected dataset. The target dataset will have all of the source dataset's properties, child datasets, clones and snapshots that match the specified naming schema. Set Snapshot Name Regular Expression to .* to replicate all snapshots.": "完全复制所选数据集。 目标数据集将具有与指定命名模式匹配的所有源数据集的属性、子数据集、克隆和快照。 将快照名称正则表达式设置为 .* 以复制所有快照。", "Compress": "压缩", "Compress Connections": "压缩连接", "Compression": "压缩", "Compression Level": "压缩级别", "Compression level": "压缩级别", "Computer Account OU": "电脑帐户OU", "Config Service": "配置服务", "Configuration": "配置", "Configuration Options": "配置选项", "Configure": "配置", "Configure ACL": "配置ACL", "Configure Active Directory": "配置活动目录", "Configure Dashboard": "配置仪表板", "Configure LDAP": "配置LDAP", "Configure now": "立即配置", "Configure permissions for this share's dataset now?": "现在配置此共享的数据集的权限？", "Configure virtual addressing topology when running in TUN mode.  (TAP mode always uses a SUBNET topology.)": "在TUN模式下运行时，配置虚拟寻址拓扑。（TAP模式始终使用子网拓扑）", "Configure {name}": "配置 {name}", "Configuring...": "配置中...", "Confirm": "确认", "Confirm Export/Disconnect": "确认导出/断开", "Confirm Failover": "确认故障转移", "Confirm New Password": "确认新密码", "Confirm Options": "确认选项", "Confirm Passphrase": "确认密码短语", "Confirm Password": "确认密码", "Confirm SED Password": "确认SED密码", "Confirm changes to <i>Group</i>. To prevent errors, changes to the <i>Group</i> are submitted only when this box is set.": "确认对<i>组</i>的更改。 为防止错误，只有在设置此框时才提交对 <i>组</i> 的更改。", "Confirm changes to <i>User</i>. To prevent errors, changes to the <i>User</i> are submitted only when this box is set.": "确认对<i>用户</i>的更改。 为防止错误，只有在设置此框时才提交对 <i>用户</i> 的更改。", "Confirm these settings.": "确认这些设置。", "Confirm to unset pool?": "确认取消设置池？", "Congratulations! Your ticket has been submitted successfully. It may take some time before images appear.": "恭喜！您的工单已成功提交。 可能需要一些时间才能看到镜像。", "Connect": "连接", "Connect Timeout": "连接超时", "Connect to TrueCommand Cloud": "连接到TrueCommand Cloud", "Connect using:": "使用连接", "Connected Initiators": "连接启动器", "Connecting to TrueCommand": "连接到 TrueCommand", "Connecting to TrueNAS": "连接到 TrueNAS", "Connection": "连接", "Connection Error": "连接错误", "Connection port number on the central key server.": "中央密钥服务器上的连接端口号。", "Connection security level:<br>  <ul><li><i>Standard</i> is most secure, but has the greatest impact on  connection speed.</li><br>  <li><i>Fast</i> is less secure than <i>Standard</i> but can give  reasonable transfer rates for devices with limited cryptographic speed.</li><br>  <li><i>Disabled</i> removes all security in favor of maximizing  connection speed. Disabling the security should only be used within a  secure, trusted network.</li></ul>": "连接安全级别：<br> <ul> <li> <i>标准</i>最安全，但是对连接速度的影响最大。</li> <br> <li> <i>快速</i>的安全性低于<i> 标准 </i>，但可以为加密速度有限的设备提供合理的传输速率。</li> <br> <li> <i>禁用</i>删除了所有安全性，以最大程度地提高连接速度。禁用安全性只能在安全，可信任的网络内使用。</li> </ul>", "Connections": "连接", "Console": "控制台", "Console Keyboard Map": "控制台键盘映射", "Console Port": "控制台端口", "Contact": "联系", "Container": "容器", "Container Images": "容器镜像", "Container Name": "容器名", "Containers": "容器", "Content Commitment": "内容承诺", "Contents of the uploaded Service Account JSON file.": "已上传的服务帐户JSON文件的内容。", "Context menu copy and paste operations     are disabled in the Shell. Copy and paste     shortcuts for Mac are <i>Command+c</i> and     <i>Command+v</i>. For most operating     systems, use <i>Ctrl+Insert</i> to copy and     <i>Shift+Insert</i> to paste.": "命令行管理程序中禁用了上下文菜单复制和粘贴操作。 Mac的复制和粘贴快捷键是<i>Command+c</i>和<i>Command+v</i>。 对于大多数操作系统，请使用<i>Ctrl+Insert</i>复制和<i>Shift+Insert</i>粘贴。", "Continue": "继续", "Continue with download?": "继续下载吗？", "Contract Type": "合约类型", "Control": "控制", "Controller Type": "控制器类型", "Controls whether the volume snapshot devices under /dev/zvol/⟨pool⟩  are hidden or visible. The default value is hidden.": "控制 /dev/zvol/⟨pool⟩ 下的卷快照设备是隐藏还是可见。 默认值是隐藏的。", "Coolest:": "最冷：", "Copied to clipboard": "已复制到剪贴板", "Copies": "副本", "Copy URL": "复制 URL", "Copy and Paste": "复制和粘贴", "Copy to Clipboard": "复制到剪贴板", "Cores": "核心数", "Country": "国家", "Country Code": "国家代码", "Country Code must contain a valid  <a href=\"https://www.iso.org/obp/ui/#search/code/\" target=\"_blank\">ISO 3166-1 alpha-2 code</a>  of two capital letters.": "国家代码必须包含有效的两个大写字母<a href=\"https://www.iso.org/obp/ui/\\#search/code/\" target=\"_blank\">ISO 3166-1 alpha-2 代码</a>。", "Crash reporting": "崩溃报告", "Create": "创建", "Create ACME Certificate": "创建ACME证书", "Create Boot Environment": "创建启动环境", "Create New": "新建", "Create New Primary Group": "创建新的主要组", "Create Pool": "创建池", "Create Snapshot": "创建快照", "Create Virtual Machine": "创建虚拟机", "Create a custom ACL": "创建自定义ACL", "Create a new Target or choose an existing target for this share.": "为此共享创建一个新目标或选择一个现有目标。", "Create a recommended formation of vdevs in a pool.": "在池中创建推荐的vdev形式。", "Create an ACL": "创建ACL", "Create custom schedule": "创建自定义时间表", "Create empty source dirs on destination after sync": "同步后在目标上创建空源目录", "Create more data vdevs like the first.": "与第一个类似，创建更多数据vdev。", "Create new disk image": "创建新磁盘镜像", "Create or Choose Block Device": "创建或选择块设备", "Create pool": "创建池", "Create {vdevs} new {vdevType} data vdevs using {used} ({size}) {type}s and leaving {remaining} of those drives unused.": "使用 {used} ({size}) {type} 创建 {vdevs} 新的 {vdevType} 数据 vdevs，并保留 {remaining} 这些驱动器未使用。", "Created Date": "创建日期", "Creates dataset snapshots even when there  have been no changes to the dataset from the last snapshot. Recommended  for creating long-term restore points, multiple snapshot tasks pointed  at the same datasets, or to be compatible with snapshot schedules or  replications created in TrueNAS 11.2 and earlier.<br><br> For example,  allowing empty snapshots for a monthly snapshot schedule allows that  monthly snapshot to be taken, even when a daily snapshot task has  already taken a snapshot of any changes to the dataset.": "即使最后一个快照的数据集没有更改，也可以创建数据集快照。建议用于创建长期还原点，将多个快照任务指向相同的数据集，或者与在TrueNAS 11.2和更早版本中创建的快照计划或复制兼容。<br> <br>例如，允许每月快照计划使用空快照，即使每月快照任务已经为数据集的任何更改创建了快照，也可以进行每月创建快照。", "Creating Certificate": "正在创建证书", "Creating or editing a <i>sysctl</i> immediately updates the Variable to the configured Value. A restart is required to apply <i>loader</i> or <i>rc.conf</i> tunables. Configured tunables remain in effect until deleted or Enabled is unset.": "创建或编辑<i> sysctl </i>会立即将变量更新为已配置的值。必须重新启动才能应用<i> loader </i>或<i> rc.conf </i>可调参数。配置的可调参数将保持有效，直到删除或未设置“启用”为止。", "Credential": "凭据", "Credentials": "证书", "Critical": "危急", "Critical Extension": "关键扩展", "Critical applications": "关键应用", "Criticality": "关键性", "Cron Job": "计划任务", "Cron Jobs": "定时任务", "Cryptographic protocols for securing client/server connections. Select which <a href=\"https://en.wikipedia.org/wiki/Transport_Layer_Security\" target=\"_blank\">Transport Layer Security (TLS)</a> versions TrueNAS can use for connection security.": "用于保护客户端/服务器连接的加密协议。选择TrueNAS可用于连接安全性的<a href=\"https://en.wikipedia.org/wiki/Transport_Layer_Security\" target=\"_blank\">传输层安全（TLS）</a>版本。", "Current Password": "当前密码", "Current State": "当前状态", "Current Train:": "当前分支：", "Current Version": "当前版本", "Custom": "自定义", "Custom Name": "自定义名称", "Custom Path": "自定义路径", "Custom Provider": "自定义提供者", "Custom Server": "自定义服务器", "Custom Value": "自定义值", "Customer Name": "客户名称", "Customizes the importance of the alert. Each level of  importance has a different icon and color to express the level of importance.": "自定义警报的重要性。每个重要性级别都有不同的图标和颜色来表示。", "DDNS server name. For example,  <i>members.dyndns.org</i> denotes a server similar to dyndns.org.": "DDNS服务器名称。例如，<i> members.dyndns.org </i>表示与dyndns.org类似的服务器。", "DDNS server path. Path syntax varies by  provider and must be obtained from that provider. For example,  <i>/update?hostname=</i> is a simple path for the  <i>update.twodns.de</i> <b>Custom Server</b>. The hostname is  automatically appended by default. More examples are in the  <a href=\"https://github.com/troglobit/inadyn#custom-ddns-providers\" target=\"_blank\">In-A-Dyn documentation</a>.": "DDNS服务器路径。路径语法因提供程序而异，必须从该提供程序获取。例如，<i>/update?hostname=</i>是<i>update.twodns.de</i> <b>自定义服务器</b>的简单路径。默认情况下，主机名会自动附加。更多示例在<a href=\"https://github.com/troglobit/inadyn\\#custom-ddns-providers\" target=\"_blank\"> In-A-Dyn文档</a>中。", "DEBUG": "调试", "DEFAULT": "默认", "DHCP": "DHCP", "DNS Domain Name": "DNS域名", "DNS Servers": "DNS服务器", "DNS Timeout": "DNS超时", "DNS name of the domain": "域的DNS名称", "DNS timeout in seconds. Increase this value if DNS queries timeout.": "DNS超时（以秒为单位）。如果DNS查询超时，请增加此值。", "DQ % Used": "已使用数据配额 %", "DQ Used": "已使用的数据配额", "Daily time range for the specific  periodic snapshots to replicate, in 15 minute increments. Periodic snapshots  created before the <i>Begin</i> time will not be included in the replication.": "要复制的特定定期快照的每日时间范围，以15分钟为增量。在<i>开始</i>时间之前创建的定期快照将不包括在复制中。", "Daily time range for the specific  periodic snapshots to replicate, in 15 minute increments. Snapshots created  after the <i>End</i> time will not be included in the replication.": "要复制的特定定期快照的每日时间范围，以15分钟为增量。在<i>结束</i>时间之后创建的快照将不包括在复制中。", "Dashboard": "仪表板", "Dashboard Configure": "仪表板配置", "Dashboard is Empty!": "仪表盘是空的！", "Data": "数据", "Data Encipherment": "数据加密", "Data Protection": "数据保护", "Data Quota": "数据配额", "Data VDEVs": "数据 VDEV", "Data VDevs": "数据VDev", "Data Written": "数据写入", "Data not available": "数据不可用", "Data not provided": "未提供数据", "Data transfer security. The connection is  authenticated with SSH. Data can be encrypted during transfer for  security or left unencrypted to maximize transfer speed. Encryption is  recommended, but can be disabled for increased speed on secure networks.": "数据传输安全性。连接已通过SSH验证。为了安全起见，可以在传输过程中对数据进行加密，也可以不加密以最大化传输速度。建议使用加密，不过在安全网络的情况下可以禁用加密以提高传输速度。", "Database": "数据库", "Dataset": "数据集", "Dataset Details": "数据集详细信息", "Dataset Key": "数据集密钥", "Dataset Name": "数据集名", "Dataset Passphrase": "数据集密码", "Dataset Rollback From Snapshot": "从快照回滚数据集", "Dataset Space Management": "数据集空间管理", "Dataset is currently locked": "数据集目前处于锁定状态", "Dataset is shared via NFS": "数据集通过 NFS 共享", "Dataset is shared via SMB": "数据集通过 SMB 共享", "Dataset is shared via SMB as {shares}": "数据集通过 SMB 共享为 {shares}", "Dataset is shared via iSCSI": "数据集通过 iSCSI 共享", "Dataset name (read-only).": "数据集名(只读)", "Dataset name is set by appending the parent path with the name entered by you. The max allowed length for the dataset name is 200. The parent path for this dataset already exceeds that limit. It is not possible to create anymore nested datasets under this path.": "数据集名称是通过在父路径后面加上您输入的名称来设置的。 数据集名称的最大允许长度为 200。此数据集的父路径已超过该限制。 无法再在此路径下创建嵌套数据集。", "Dataset rolled back to snapshot {name}.": "从快照 {name} 回滚数据集。", "Dataset settings updated.": "数据集设置已更新。", "Dataset {name} was created.": "数据集 {name} 已创建。", "Datasets": "数据集", "Datastore": "数据存储", "Date": "日期", "Date Created": "创建日期", "Date Format": "日期格式", "Date created": "创建日期", "Day of Month": "每月的几号", "Day of Week": "星期几", "Day(s)": "天", "Days": "天数", "Days before a completed scrub is allowed  to run again. This controls the task schedule. For example, scheduling  a scrub to run daily and setting <b>Threshold days</b> to <i>7</i>  means the scrub attempts to run daily. When the scrub is successful, it  continues to check daily but does not run again until seven days have  elapsed. Using a multiple of seven ensures the scrub always occurs on  the same weekday.": "完成校验的前几天允许再次运行。这可以控制任务计划。例如，安排每天运行一次校验并将<b>阈值天</b>设置为<i> 7 </i>表示每天尝试运行校验。校验成功后，它将继续每天检查一次，直到7天后才再次运行。使用7的倍数可确保校验总是在同一工作日进行。", "Days of Month": "月的天数", "Days of Week": "星期几", "Days of the Week": "星期几", "Days when this task will run.": "该任务将运行的天数。", "De-duplication tables are stored on this special vdev type. These vdevs must be sized to <i>X</i> GiB for each <i>X</i> TiB of general storage.": "重复数据删除表存储在这种特殊的vdev类型上。对于常规存储的每个<i> X </i> TiB，这些vdev的大小必须设置为<i> X </i> GiB。", "Debug": "调试", "Debug could not be downloaded.": "调试无法下载。", "Dec": "十二月", "Decipher Only": "仅解密", "Dedup": "去重", "Dedup VDEVs": "去重 VDEV", "Dedup VDev": "去重VDev", "Default": "默认", "Default ACL Options": "默认ACL选项", "Default Checksum Warning": "默认校验和警告", "Default Gateway": "默认网关", "Default Route": "默认路由", "Default TrueNAS controller": "默认TrueNas控制器", "Default is UTF-8 which supports all characters in all languages.": "默认值为UTF-8，它支持所有语言的字符。", "Define a length of time to retain the snapshot  on this system using a numeric value and a single lowercase letter for  units. Examples: <i>3h</i> is three hours, <i>1m</i> is one month, and  <i>1y</i> is one year. Does not accept Minute values. After the time  expires, the snapshot is removed. Snapshots which have been replicated  to other systems are not affected.": "使用数值和单位的单个小写字母来定义将快照保留在此系统上的时间。例如：<i> 3h </i>是三个小时，<i> 1m </i>是一个月，而<i> 1y </i>是一年。不接受分钟值。时间到期后，快照将被删除。已复制到其他系统的快照不受影响。", "Define a maximum size for both the dataset and any child datasets. Enter <i>0</i> to remove the quota.": "为数据集和子数据集定义最大大小。输入<i> 0 </i>删除配额。", "Define a number of minutes for <a href=\"https://linux.die.net/man/8/smartd\" target=\"_blank\">smartd</a> to wake up and check if any tests are configured to run.": "设定 <a href=\"https://linux.die.net/man/8/smartd\" target=\"_blank\">smartd</a> 分钟数以唤醒并检查是否有预定的需要运行的测试。", "Define an alias for the interface  on this TrueNAS controller. The alias can be an IPv4 or IPv6  address.": "为该TrueNAS控制器上的接口定义别名。别名可以是IPv4或IPv6地址。", "Define an alias that can  connect to the interface on either TrueNAS controller. This address  remains active if a Controller failover occurs.": "定义可以连接到任一TrueNAS控制器上的接口的别名。如果发生控制器故障转移，此地址将保持活动状态。", "Define the maximum number of chunks for a multipart upload. This can  be useful if a service does not support the 10,000 chunk AWS S3 specification.": "定义分段上传的最大块数。如果服务不支持10,000块AWS S3规范，这会很有用。", "Define the number of UIDS/GIDS available per domain  range. The minimum is <i>2000</i> and the recommended default is <i>100000</i>.": "定义每个域范围可用的UIDS/GIDS数量。最小值为<i> 2000 </i>，建议的默认值为<i> 100000 </i>。", "Define the path to a system location  that has snapshots to replicate. Click the  <i class=\"material-icons\">folder</i> to see all locations on the  source system or click in the field to manually type a location  (Example: <code>pool1/dataset1</code>). Multiple source locations can  be selected or manually defined with a comma (<code>,</code>) separator.": "定义要复制快照的系统位置的路径。单击<i class=\"material-icons\">文件夹</i>以查看源系统上的所有位置，或单击字段以手动键入位置（例如：<code>pool1/dataset1</code> ）。可以选择多个源位置，或使用逗号（<code>,</code>）分隔符手动定义多个源位置。", "Define the path to a system location  that has snapshots to replicate. Click the  <i class=\"material-icons\">folder</i> to see all locations on the  source system or click in the field to manually type a location  (Example: <code>pool1/dataset1</code>). Multiple source locations can  be selected or manually defined with a comma (<code>,</code>) separator. <br><br> Selecting a location displays the number of existing snapshots  that can be replicated. Selecting a location that has no snapshots  configures the replication task to take a manual snapshot of that  location and replicate it to the destination.": "定义要复制快照的系统位置的路径。单击<i class=\"material-icons\">文件夹</i>以查看源系统上的所有位置，或单击字段以手动键入位置（例如：<code>pool1/dataset1</code> ）。可以选择多个源位置，或使用逗号（<code>,</code>）分隔符手动定义多个源位置。<br> <br>选择一个位置将显示可以复制的现有快照的数量。 选择没有快照的位置会将复制任务配置为对该位置进行手动快照，然后将其复制到目标位置。", "Define the path to a system location that  will store replicated snapshots. Click the  <i class=\"material-icons\">folder</i> to see all locations on the  destination system or click in the field to manually type a location  path (Example: <code>pool1/dataset1</code>). Selecting a location  defines the full path to that location as the destination. Appending a  name to the path will create new zvol at that location.<br><br>  For example, selecting <i>pool1/dataset1</i> will store  snapshots in <i>dataset1</i>, but clicking the path and typing  <code>/zvol1</code> after <i>dataset1</i> will create <i>zvol1</i> for  snapshot storage.": "定义要复制快照的系统位置的路径。 单击<i class=\"material-icons\">文件夹</i>以查看源系统上的所有位置，或单击字段以手动键入位置（例如：<code>pool1/dataset1</code> ）。可以选择多个源位置，或使用逗号（<code>,</code>）分隔符手动定义多个源位置。<br> <br>选择一个位置将显示可以复制的现有快照的数量。选择没有快照的位置会将复制任务配置为对该位置进行手动快照，然后将其复制到目标位置。", "Define the path to an existing zvol for VM use.": "定义现有zvol的路径以供虚拟机使用。", "Define the server where all changes to the database are performed.": "定义执行对数据库的所有更改的服务器。", "Define the server where all password changes are performed.": "定义执行所有密码更改的服务器。", "Define the size of the raw file in GiB.": "定义raw文件大小，以GiB为单位。", "Define the system services that are allowed to communicate externally. All other external traffic is restricted.": "定义允许与外部通信的系统服务。所有其他外部流量都受到限制。", "Define the target as *iSCSI*, *Fibre Channel*, or *Both*.": "将目标定义为*iSCSI*，*光纤通道*或*两者*。", "Define whether the control channel,  data channel, both channels, or neither channel of an FTP  session must occur over SSL/TLS. The policies are described  <a href=\"http://www.proftpd.org/docs/directives/linked/config_ref_TLSRequired.html\" target=\"_blank\">here</a>": "定义控制通道，数据通道，两个通道或 FTP 会话通道是否必须通过 SSL/TLS 来进行。<a href=\"http://www.proftpd.org/docs/directives/linked/config_ref_TLSRequired.html\" target=\"_blank\">此处</a>介绍了策略。", "Delay Updates": "延迟更新", "Delay VM Boot Until VNC Connects": "延迟虚拟机启动，直到VNC连接", "Delays the start of network services until  pings are returned from the IP addresses added to the  <i>Netwait IP List</i>.": "延迟网络服务的启动，直到从添加到<i> Netwait IP列表</i>的IP地址返回ping为止。", "Delete": "删除", "Delete ": "删除 ", "Delete <b>{deviceType} {device}</b>": "删除 <b>{deviceType} {device}</b>", "Delete API Key": "删除 API 密钥", "Delete Certificate": "删除证书", "Delete Children": "删除子项", "Delete Dataset": "删除数据集", "Delete Device": "删除设备", "Delete Group": "删除组", "Delete Group Quota": "删除组配额", "Delete NTP Server": "删除NTP服务器", "Delete User": "删除用户", "Delete User Quota": "删除用户配额", "Delete Virtual Machine": "删除虚拟机", "Delete Virtual Machine Data?": "删除虚拟机数据？", "Delete configuration of shares that used this pool?": "删除使用这个池的共享配置吗？", "Delete dataset {name}": "删除数据集 {name}", "Delete docker images used by the app": "删除应用使用的 Docker 镜像", "Delete files in the destination directory                that do not exist in the source directory.": "删除目标目录中源目录不存在的文件。", "Delete iSCSI extent {name}?": "删除iSCSI extent {name}?", "Delete raw file": "删除原始文件", "Delete selections": "删除选中项目", "Delete snapshot {name}?": "删除快照 {name}？", "Delete the selected item?": "删除所选项目？", "Delete user primary group `{name}`": "删除用户主要组 `{name}`", "Delete zvol device": "删除 zvol 设备", "Delete {n, plural, one {# user} other {# users}} with this primary group?": "删除具有此主要组的{n, plural, other{#个用户}}？", "Delete {name}?": "删除 {name}？", "Deleted {n, plural, one {# snapshot} other {# snapshots}}": "删除了{n, plural, other{#个快照}}", "Deleting interfaces while HA is enabled is not allowed.": "不允许在启用HA的情况下删除接口。", "Deleting...": "删除中...", "Deny": "拒绝", "Deny All": "全部拒绝", "Deployments": "部署", "Deregister": "注销", "Deregister TrueCommand Cloud Service": "注销TrueCommand云服务", "Describe the UPS device. It can contain alphanumeric, period, comma, hyphen, and underscore characters.": "描述UPS设备。它可以包含字母数字，句点，逗号，连字符和下划线。", "Describe the scrub task.": "描述这个数据校验任务。", "Describe this module.": "描述这个模块。", "Describe this service.": "描述这个服务。", "Description": "描述", "Description (optional).": "描述（可选）。", "Description of the share or notes on how it is used.": "共享说明或使用说明。", "Descriptive identifier for this API key.": "此API密钥的描述性标识符。", "Descriptive identifier for this certificate authority.": "此证书颁发机构的描述性标识符。", "Descriptive identifier for this certificate.": "此证书的描述性标识符。", "Descriptive name for the replication.": "复制的描述性名称。", "Destination": "目标", "Destination Dataset Read-only Policy": "目标数据集只读策略", "Destination Location": "目标位置", "Destination Snapshot Lifetime": "目标快照生命周期", "Destination Snapshots Are Not Related to Replicated Snapshots": "目标快照与复制快照无关", "Destination dataset does not contain any snapshots that can be used as a basis for the incremental changes in the snapshots being sent. The snapshots in the destination dataset will be deleted and the replication will begin with a complete initial copy.": "目标数据集不包含任何快照，这些快照可用作发送的快照中增量更改的基础。目标数据集中的快照将被删除，复制将以完整的初始副本开始。", "Destroy data on this pool?": "销毁这个池里的数据？", "Detach": "分离", "Detach Disk": "分离磁盘", "Detach disk {name}?": "分离磁盘 {name}？", "Details": "详细信息", "Details for": "详细信息", "Determine how  <a href=\"https://man7.org/linux/man-pages/man1/chmod.1.html\" target=\"_blank\">chmod</a>  behaves when adjusting file ACLs. See the  <a href=\"https://linux.die.net/man/8/zfs\" target=\"_blank\">zfs(8)</a> aclmode property.  <br><br><i>Passthrough</i> only updates ACL entries that are related to the file or directory mode.  <br><br><i>Restricted</i> does not allow chmod to make changes to files or directories with a  non-trivial ACL. An ACL is trivial if it can be fully expressed as a file mode without losing  any access rules. Setting the ACL Mode to Restricted is typically used to optimize a dataset for  SMB sharing, but can require further optimizations. For example, configuring an rsync task with this  dataset could require adding <i>--no-perms</i> in the task <i>Auxiliary Parameters</i> field.": "查明  <a href=\"https://man7.org/linux/man-pages/man1/chmod.1.html\" target=\"_blank\">chmod</a>  在更改文件ACL时如何工作。请参阅 <a href=\"https://linux.die.net/man/8/zfs\" target=\"_blank\">zfs(8)</a> ACL模式属性.  <br><br><i>直通</i> 只更新于文件或目录模式相关联的ACL条目。<br><br><i>受限的</i> 不允许chmod对有特殊ACL设置的文件或目录做更改。 特殊ACL的定义是其可以完全表述为一个文件模式且不丢失任何访问权限。 ACL模式“受限的”通常被用于优化一个通过SMB共享的数据集，但是可能需要更多优化。例如：在此数据集上设置一个Rsync任务需要添加在 <i>Auxiliary Parameters</i> 条目中添加 <i>--no-perms</i> 参数.", "Determine whether this share name is included when browsing shares. Home shares are only visible to the owner regardless of this setting.": "确定浏览共享时是否包含此共享名称。无论此设置如何，家庭共享仅对所有者可见。", "Determines the outgoing and incoming traffic ports.<br>  <i>LACP</i> is the recommended protocol if the network switch is capable of  active LACP.<br><i>Failover</i> is the default protocol choice and  should only be used if the network switch does not support active LACP.": "确定传出和传入流量端口。<br><i>LACP</i>是推荐的协议，如果网络交换机支持活动LACP。<br><i>故障转移</i>是默认协议选择，并且仅当网络交换机不支持活动LACP时才应使用。", "Device": "设备", "Device Attached": "设备已连接", "Device Busy": "设备忙", "Device ID": "设备ID", "Device Name": "设备名称", "Device Order": "设备顺序", "Device Type": "设备类型", "Device detached": "设备已分离 ", "Device names of each disk being edited.": "正在编辑的每个磁盘的设备名称。", "Devices": "设备", "Difference": "差异", "Digest Algorithm": "摘要算法", "Digital Signature": "数字签名", "Direct the flow of data to the remote host.": "将数据流定向到远程主机。", "Direction": "方向", "Directories and Permissions": "目录和权限", "Directory": "目录", "Directory Inherit": "目录继承", "Directory Mask": "目录掩码", "Directory Permissions": "目录权限", "Directory Services": "目录服务", "Directory Services Monitor": "目录服务监视器", "Directory/Files": "目录/文件", "Disable AD User / Group Cache": "禁用 AD 用户 / 组缓存", "Disable Endpoint Region": "禁用端点区域", "Disable Failover": "禁用故障转移", "Disable LDAP User/Group Cache": "禁用LDAP用户/组缓存", "Disable Password": "禁用密码", "Disable Physical Block Size Reporting": "禁用物理块大小报告", "Disable Two-Factor Authentication": "禁用双重身份验证", "Disable automatic failover.": "禁用自动故障转移。", "Disable caching LDAP users and groups in large LDAP environments.  When caching is disabled, LDAP users and groups do not appear in dropdown  menus, but are still accepted when manually entered.": "禁用大型 LDAP 环境中的缓存 LDAP 用户和组。 禁用缓存后，LDAP 用户和组不会显示在下拉菜单中，但在手动输入时仍被接受。", "Disabled": "禁用", "Disabled in Disk Settings": "在磁盘设置中禁用", "Discard": "丢弃", "Discover Remote Host Key": "发现远程主机密钥", "Discovery Auth Group": "发现身份验证组", "Discovery Auth Method": "发现身份验证方法", "Discovery Authentication Group": "发现身份验证组", "Discovery Authentication Method": "发现身份验证方法", "Disk": "磁盘", "Disk Details": "磁盘详细信息", "Disk Health": "磁盘运行状况", "Disk Info": "磁盘信息", "Disk Reports": "磁盘报告", "Disk Sector Size": "磁盘扇区大小", "Disk Size": "磁盘大小", "Disk Type": "磁盘类型", "Disk Wiped successfully": "磁盘擦除成功", "Disk device name.": "磁盘设备名称。", "Disk is unavailable": "磁盘不可用", "Disk not attached to any pools.": "磁盘未连接到任何池。", "Disk settings successfully saved.": "磁盘设置已成功保存。", "Disks": "磁盘", "Disks Overview": "磁盘概览", "Disks temperature related alerts": "磁盘温度相关警报", "Disks to be edited:": "要编辑的磁盘：", "Disks with Errors": "有错误的磁盘", "Dismiss": "关闭", "Dismiss All Alerts": "关闭所有警报", "Dismissed": "已忽略", "Display": "展示", "Display Device": "显示设备", "Display Login": "显示登录", "Display Port": "显示端口", "Display Type": "显示类型", "Display console messages in real time at the bottom of the browser.": "在浏览器底部实时显示控制台消息。", "Display network interface IP address. The primary interface IP address is the default. A different interface IP address can be chosen.": "显示网络接口IP地址。主接口IP地址是默认值。可以选择不同的接口IP地址。", "Distinguished Name": "专有名称", "Do <b>NOT</b> change this setting when using Windows as the initiator. Only needs to be changed in large environments where the number of systems using a specific RPM is needed for accurate reporting statistics.": "使用Windows作为启动器时，请<b>不要</b>更改此设置。仅在大型环境中需要更改，在大型环境中需要使用特定RPM的系统数量才能获得准确的报告统计信息。", "Do not save": "不要保存", "Do not set this if the Serial Port is disabled.": "如果禁用了串行端口，请不要设置此项。", "Docker Host": "Docker主机", "Docker Registry Authentication": "Docker注册表认证", "Does your business need <a href=\"https://www.ixsystems.com/truenas/?utm_source=truenas+core+ui&utm_medium=os&utm_campaign=welcome\" target=\"_blank\">Enterprise level</a> support and services? Contact <a href=\"https://www.ixsystems.com/quote-form/?utm_source=truenas+core+ui&utm_medium=os&utm_campaign=welcome\"target=\"_blank\">iXsystems</a> for more information.": "您的企业是否需要 <a href=\"https://www.ixsystems.com/truenas/?utm_source=truenas+core+ui&utm_medium=os&utm_campaign=welcome\" target=\"_blank\">Enterprise level</a> 支持服务？ 联系 <a href=\"https://www.ixsystems.com/quote-form/?utm_source=truenas+core+ui&utm_medium=os&utm_campaign=welcome\"target=\"_blank\">iXsystems</a> 以获取信息.", "Domain": "域", "Domain Account Name": "域账户名", "Domain Account Password": "域账户密码", "Domain Name": "域名", "Domain for the user <i>Name</i>. Required when a <i>SID</i> is not entered. Local users have the SMB server NetBIOS name: <code>freenas\\smbusers</code>.": "用户<i>名称</i>的域。如果未输入<i> SID </i>，则为必填项。本地用户具有SMB服务器的NetBIOS名称：<code>freenas\\\\smbusers</code>。", "Domain:": "域名：", "Domains": "域", "Done": "完成", "Download": "下载", "Download Authorized Keys": "下载授权密钥", "Download Client Config": "下载客户端配置", "Download Encryption Key": "下载加密密钥", "Download File": "下载文件", "Download Key": "下载密钥", "Download Keys": "下载密钥", "Download Logs": "下载日志", "Download Private Key": "下载私钥", "Download Public Key": "下载公钥", "Download Update": "下载更新", "Download Updates": "下载更新", "Download actions": "下载动作", "Drive Account Type": "驱动器帐户类型", "Drive Details": "驱动器详细信息", "Drive ID": "驱动器ID", "Drive Temperatures": "驱动器温度", "Drive reserved for inserting into DATA pool vdevs when an active drive has failed.": "活动驱动器发生故障时保留的驱动器，用于插入到数据池vdev中。", "Driver": "驱动", "Drives List": "驱动器列表", "Drives and IDs registered to the Microsoft account.  Selecting a drive also fills the <i>Drive ID</i> field.": "已注册到Microsoft帐户的驱动器和ID。选择一个驱动器还会填充<i> 驱动器ID </i>字段。", "Dry Run": "空运行", "Dry Run Cloud Sync Task": "空运行云同步任务", "E-mail address that will receive SNMP service messages.": "接收SNMP服务消息的电子邮件地址。", "EC Curve": "EC曲线", "EC2 Instance ID": "EC2 实例 ID", "EDIT LABEL": "编辑标签", "EMERGENCY": "紧急情况", "ERROR": "错误", "ERROR: Not Enough Memory": "错误：内存不足", "EULA": "最终用户许可协议", "EXPIRED": "已过期", "EXPIRES TODAY": "今天到期", "Edit": "编辑", "Edit API Key": "编辑API密钥", "Edit Alert Service": "编辑警告服务", "Edit Application Settings": "编辑应用设置", "Edit Authorized Access": "编辑授权访问", "Edit Auto TRIM": "编辑自动 TRIM", "Edit CSR": "编辑CSR", "Edit Catalog": "编辑目录", "Edit Certificate Authority": "编辑证书颁发机构", "Edit Cloud Sync Task": "编辑云同步任务", "Edit Cron Job": "编辑定时任务", "Edit DNS Authenticator": "编辑DNS身份验证器", "Edit Dataset": "编辑数据集", "Edit Device": "编辑设备", "Edit Disk": "编辑磁盘", "Edit Disk(s)": "编辑磁盘", "Edit Encryption Options for {dataset}": "编辑 {dataset} 的加密选项", "Edit Extent": "编辑范围", "Edit Filesystem ACL": "编辑文件系统ACL", "Edit Group": "编辑组", "Edit Group Quota": "编辑组配额", "Edit ISCSI Target": "编辑ISCSI目标", "Edit Idmap": "编辑Idmap", "Edit Init/Shutdown Script": "编辑开机/关机脚本", "Edit Interface": "编辑接口", "Edit Kerberos Keytab": "编辑Kerberos密钥表", "Edit Kerberos Realm": "编辑Kerberos领域", "Edit NFS Share": "编辑 NFS 共享", "Edit NTP Server": "编辑 NTP 服务器", "Edit Periodic Snapshot Task": "编辑定期快照任务", "Edit Portal": "编辑门户", "Edit Replication Task": "编辑复制任务", "Edit Rsync Module": "编辑 Rsync 模块", "Edit Rsync Task": "编辑Rsync任务", "Edit S.M.A.R.T. Test": "编辑S.M.A.R.T.测试", "Edit SMB": "编辑SMB", "Edit SSH Connection": "编辑 SSH 连接", "Edit Scrub Task": "校验任务", "Edit Share ACL": "编辑共享ACL", "Edit Static Route": "编辑静态路由", "Edit Sysctl": "编辑 Sysctl", "Edit User": "编辑用户", "Edit User Quota": "编辑用户配额", "Edit VM Snapshot": "编辑虚拟机快照", "Edit WebDAV": "编辑WebDAV", "Edit Zvol": "编辑Zvol", "Editing interfaces while HA is enabled is not allowed.": "启用HA时，不允许编辑接口。", "Editing top-level datasets can prevent users from accessing data in child datasets.": "编辑顶级数据集可能会阻止用户访问子数据集中的数据。", "Either SID or Domain Name + Name are required.": "需要 SID 或域名 + 名称。", "Electing {controller}.": "选择 {controller}。", "Elements": "元素", "Email": "电子邮件", "Email Options": "邮箱选项", "Email Subject": "电子邮件主题", "Email addresses must be entered in the format   <i>local-name</i>@<i>domain.com<i>, with entries separated by pressing   <code>Enter</code>.": "电子邮件地址必须以<i>本地名称</i>@<i>domain.com<i>的格式输入，并按<code> Enter </code>分隔条目。", "Email addresses to receive copies of iXsystems Support   messages about this issue. Use the format <i>name</i>@<i>domain.com</i>.   Separate entries by pressing <code>Enter</code>.": "用于接收有关此问题的iXsystems支持消息副本的电子邮件地址。使用格式<i>名称</i>@<i>domain.com</i>。通过按<code> Enter </code>分隔条目。", "Email settings updated.": "邮箱设置已更新。", "Emergency": "紧急情况", "Emulating an <i>Intel e82545 (e1000)</i> Ethernet card provides compatibility with most operating systems. Change to <i>VirtIO</i> to provide better performance on systems with VirtIO paravirtualized network driver support.": "模拟<i> Intel e82545（e1000）</i>以太网卡可与大多数操作系统兼容。更改为<i> VirtIO </i>，以在具有VirtIO半虚拟化网络驱动程序支持的系统上提供更好的性能。", "Enable": "启用", "Enable   <a href=\"https://en.wikipedia.org/wiki/SMTP_Authentication\"   target=\"_blank\">SMTP AUTH</a> using PLAIN SASL.   Requires a valid Username and Password.": "使用PLAIN SASL启用<a href=\"https://en.wikipedia.org/wiki/SMTP_Authentication\" target=\"_blank\">SMTP认证</a>。需要有效的用户名和密码。", "Enable  <a href=\"https://zfsonlinux.org/manpages/0.8.3/man8/zfs.8.html\" target=\"_blank\">ZFS encryption</a>  for this pool and add an encryption algorithm selector.": "为此池启用<a href=\"https://zfsonlinux.org/manpages/0.8.3/man8/zfs.8.html\" target=\"_blank\">ZFS加密</a>，并添加加密算法选择器。", "Enable (requires password or Kerberos principal)": "启用（需要密码或Kerberos主体）", "Enable ACL": "启用ACL", "Enable ACL support for the SMB share.": "为SMB共享启用ACL支持。", "Enable Active Directory": "启用活动目录", "Enable Alternate Data Streams": "启用备用数据流", "Enable Apple SMB2/3 Protocol Extensions": "启用Apple SMB2/3扩展协议", "Enable Atime": "启用Atime", "Enable Autotune": "启用自动调节", "Enable Browser": "启用浏览器", "Enable Container Image Updates": "启用容器镜像更新", "Enable Debug Kernel": "启用内核调试", "Enable Display": "启用显示", "Enable FSRVP": "启用FSRVP", "Enable FXP": "启用FXP", "Enable GMail OAuth authentication.": "启用GMail OAuth身份验证。", "Enable GPU support": "启用 GPU 支持", "Enable HTTPS Redirect": "启用 HTTPS 重定向", "Enable Hyper-V Enlightenments": "启用 Hyper-V 启蒙", "Enable Integrated Loadbalancer": "启用集成负载均衡器", "Enable Netwait Feature": "启用Netwait功能", "Enable S.M.A.R.T.": "启用S.M.A.R.T.", "Enable SMB1 support": "启用SMB1支持", "Enable SMB2/3 Durable Handles": "启用SMB2/3 Durable Handles", "Enable SMTP configuration": "启用SMTP配置", "Enable Serial Console": "启用串行控制台", "Enable Service": "启用服务", "Enable Shadow Copies": "启用卷影副本", "Enable TLS": "启用TLS", "Enable TPC": "启用TPC", "Enable Time Machine backups on this share.": "在这个共享上启用时光机备份。", "Enable Two-Factor Auth for SSH": "为SSH启用双重身份验证", "Enable Two-Factor Authentication": "启用双重身份验证", "Enable a Display (Virtual Network Computing) remote connection. Requires <i>UEFI</i> booting.": "启用显示（虚拟网络计算）远程连接。 需要<i>UEFI</i>启动。", "Enable for TrueNAS to periodically review data blocks and identify empty blocks of obsolete blocks that can be deleted. Unset to incorporate day block overwrites when a device write is started (default).": "启用TrueNAS可以定期查看数据块并识别可以删除的废弃块的空块。取消设置以在设备写入开始时合并日期块覆盖（默认）。", "Enable iXsystems Proactive Support": "启用iXsystems主动支持", "Enable sending anonymous usage statistics to iXsystems.": "启用向iXsystems发送匿名使用情况统计信息。", "Enable service": "启用服务", "Enable support for the File Server Remote VSS Protocol  (<a href=\"https://docs.microsoft.com/en-us/openspecs/windows_protocols/ms-fsrvp\" target=\"_blank\">FSVRP</a>).  This protocol allows RPC clients to manage snapshots for a specific SMB share.  The share path must be a dataset mountpoint. Snapshots have the prefix  <code>fss-</code> followed by a snapshot creation timestamp. A snapshot must have  this prefix for an RPC user to delete it.": "启用对文件服务器远程VSS协议（<a href=\"https://docs.microsoft.com/en-us/openspecs/windows_protocols/ms-fsrvp\" target=\"_blank\">FSVRP</a>）的支持。该协议允许RPC客户端管理特定SMB共享的快照。共享路径必须是数据集挂载点。快照的前缀为<code> fss-</code>，后跟快照创建时间戳。快照必须具有此前缀，RPC用户才能删除快照。", "Enable the Active Directory service. The first time this option is set, the Domain Account Password must be entered.": "启用活动目录服务。首次设置此选项时，必须输入域帐户密码。", "Enable the SNMPv3 security model.": "启用SNMPv3安全模型。", "Enable this Cloud Sync Task. Unset to disable this Cloud Sync Task without deleting it.": "启用这个云同步任务。取消设置以禁用此云同步任务而不删除它。", "Enable this NFS share. Unset to disable this NFS share  without deleting it.": "启用此NFS共享。取消设置以禁用此NFS共享而不删除它。", "Enable this SMB share. Unset to disable this SMB share  without deleting it.": "启用此SMB共享。取消设置以禁用此SMB共享而不删除它。", "Enable this WebDAV share. Unset to disable this WebDAV  share without deleting it.": "启用此WebDAV共享。取消设置以禁用此WebDAV共享而不删除它。", "Enable this cron job. When unset, disable the                cron job without deleting it.": "启用这个计划任务。取消设置后，禁用此计划任务而不将其删除。", "Enable this rsync task. Unset to disable this                rsync task without deleting it.": "启用这个rsync任务。取消设置以禁用此rsync任务而不删除它。", "Enable this service to start automatically.": "启用此服务以自动启动。", "Enable this task. Unset to disable the task     without deleting it.": "启用这个任务。取消设置以禁用任务而不删除它。", "Enable this tunable. Unset to disable this tunable without deleting it.": "启用这个可调参数。取消设置以禁用此可调参数而不删除它。", "Enable to prevent binding to local address and port.  Must be enabled if OpenVPN client and server are to run concurrently.": "启用以防止绑定到本地地址和端口。如果OpenVPN客户端和服务器要同时运行，则必须启用。", "Enable two-factor authentication for SSH access to the system. It is recommended  to leave this DISABLED until after two-factor authentication is successfully tested with the UI.": "为通过SSH访问系统启用双重身份验证。建议将此禁用设置为禁用，直到使用UI成功测试了双重身份验证。", "Enable/Disable STP on the bridge interfaces configurable.": "在可配置的桥接接口上开启/关闭 STP。", "Enable/disable TLS Web Client Authentication.": "启用/禁用TLS Web客户端身份验证。", "Enabled": "已启用", "Enabled 'Time Machine'": "启用“时间机器”", "Enabled 'Use as Home Share'": "启用“用作家庭共享”", "Enabled HTTPS Redirect": "启用 HTTPS 重定向", "Enables <i>receive</i> mode. Any received peer  information is saved in interface descriptions.": "启用<i>接收</i>模式。任何收到的对等信息都保存在接口描述中。", "Enabling <em>Time Machine</em> on an SMB share requires restarting the SMB service.": "在SMB共享上启用 <em>Time Machine</em>需要重新启动SMB服务。", "Enabling allows the system to conduct periodic <a href=\"https://www.truenas.com/docs/scale/scaleuireference/dataprotection/smarttestsscreensscale/\" target=\"_blank\">S.M.A.R.T. tests</a>.": "启用后，系统会定期进行<a href=\"--docurl--/tasks.html\\#s-m-a-r-t-tests\" target=\"_blank\"> S.M.A.R.T. 测试</a>。", "Enabling allows using a password to authenticate  the SSH login. <i>Warning:</i> when directory services are enabled, allowing password  authentication can grant access to all users imported by the directory service.<br>  Disabling changes authentication to require keys for all users. This requires  <a href=\"http://the.earth.li/&percnt;7Esgtatham/putty/0.55/htmldoc/Chapter8.html\" target=\"_blank\">additional setup</a>  on both the SSH client and server.": "启用允许使用密码来验证SSH登录。<i>警告</i>启用目录服务时，允许密码身份验证可以授予目录服务导入的所有用户访问权限。<br>禁用更改身份验证以要求所有用户使用密钥。这需要<a href=\"http://the.earth.li/&percnt;7Esgtatham/putty/0.55/htmldoc/Chapter8.html\" target=\"_blank\">额外设置</a>SSH客户端和服务器。", "Enabling redirect will require all URLs served from current host to be served via HTTPS regardless of port used. This may make some App portals inaccessible if they don't use HTTPS. Do you wish to continue?": "启用重定向将要求从当前主机提供的所有 URL 都通过 HTTPS 提供，而不考虑使用的端口。 如果某些应用程序门户不使用 HTTPS，这可能会使它们无法访问。 你想继续吗？", "Enabling sends the  <i>plugin instance</i> and <i>type instance</i> to Graphite as separate  path components: <i>host.cpu.0.cpu.idle</i>.<br><br> Disabling sends  the <i>plugin</i> and <i>plugin instance</i> as one path component and  <i>type</i> and <i>type instance</i> as another component:  <i>host.cpu-0.cpu-idle</i>.": "启用会将<i>插件实例</i>和<i>类型实例</i>作为单独的路径组件发送到Graphite：<i> host.cpu.0.cpu.idle </i>。<br><br> 禁用会将<i>插件</i>和<i>插件实例</i>作为一个路径组件发送，并将<i>类型</i>和<i>类型实例</i>作为另一个组件发送：<i> host.cpu-0.cpu-idle </i>。", "Enabling this option can expose administrator credentials and is not recommended.": "启用此选项可以公开管理员凭据，因此不建议这样做。", "Enabling this option is <b>not</b> recommended as it bypasses a security mechanism.": "建议<b>不要</b>启用此选项，因为它会绕过安全机制。", "Encipher Only": "仅加密", "Enclosure": "机柜", "Enclosure Label": "外壳标签", "Encode information in less space than the  original data occupies. It is recommended to choose a compression algorithm  that balances disk performance with the amount of saved space.<br> <i>LZ4</i> is  generally recommended as it maximizes performance and dynamically identifies  the best files to compress.<br> <i>GZIP</i> options range from 1 for least  compression, best performance, through 9 for maximum compression with  greatest performance impact.<br> <i>ZLE</i> is a fast algorithm that only  elminates runs of zeroes.": "在比原始数据占用更少的空间中编码信息。建议选择一种压缩算法，以平衡磁盘性能和节省的空间量通常建议使用<br> <i> LZ4 </i>，因为它可以最大化性能并动态识别要最佳压缩的文件。<br> <i> GZIP </i>选项的范围从1（最小压缩），最佳性能到9（最大压缩），不影响性能。<br> <i> ZLE </ i>是一种快速算法，仅消除零的游程。", "Encrypt (<i>PUSH</i>) or decrypt  (<i>PULL</i>) file names with the rclone  <a href=\"https://rclone.org/crypt/#file-name-encryption-modes\" target=\"_blank\">\"Standard\" file name encryption mode</a>.  The original directory structure is preserved. A filename with the same  name always has the same encrypted filename.<br><br>  <i>PULL</i> tasks that have <b>Filename Encryption</b> enabled and an  incorrect <b>Encryption Password</b> or <b>Encryption Salt</b> will not  transfer any files but still report that the task was successful. To  verify that files were transferred successfully, click the finished  task status to see a list of transferred files.": "使用rclone <a href=\"https://rclone.org/crypt/\\#file-name-encryption-modes\" target=\"_blank\">\"Standard\"文件名加密模式</a>加密（<i> 推送 </i>）或解密（<i> 拉取 </i>）文件名。原始目录结构得以保留。具有相同名称的文件名始终具有相同的加密文件名。<br> <br> 启用了<b>文件名加密</b>且<b>加密密码</ b>或<b>加密盐</ b>不正确的<i> 拉取 </ i>任务将不会传输任何文件，但仍然会报告该任务已成功。要验证文件是否已成功传输，请单击完成的任务状态以查看已传输文件的列表。", "Encrypted Datasets": "加密的数据集", "Encryption": "加密", "Encryption (more secure, but slower)": "加密（更安全，但速度较慢）", "Encryption Algorithm": "加密算法", "Encryption Key": "加密密钥", "Encryption Key Format": "加密密钥格式", "Encryption Key Location in Target System": "目标系统中的加密密钥位置", "Encryption Mode": "加密模式", "Encryption Options": "加密选项", "Encryption Options Saved": "加密选项已保存", "Encryption Password": "加密密码", "Encryption Protocol": "加密协议", "Encryption Root": "加密根", "Encryption Salt": "加密盐", "Encryption Standard": "加密标准", "Encryption Type": "加密方式", "Encryption is for users storing sensitive data.  Pool-level encryption does not apply to the storage pool or disks in the pool. It applies  to the root dataset that shares the pool name and any child datasets created unless you change  the encryption at the time you create the child dataset. For more information on encryption please  refer to the <a href=\"http://TrueNAS.com/docs/\" target=\"_blank\">TrueNAS Documentation hub</a>.": "加密用于存储敏感数据的用户。 池级加密不适用于存储池或池中的磁盘。 它适用于共享池名称的根数据集和创建的任何子数据集，除非您在创建子数据集时更改加密。 有关加密的更多信息，请参阅 <a href=\"http://TrueNAS.com/docs/\" target=\"_blank\">TrueNAS 文档中心</a>。", "Encryption key that can unlock the dataset.": "可以解锁数据集的加密密钥。", "End": "结束", "End User License Agreement - TrueNAS": "最终用户许可协议 - TrueNAS", "End time for the replication task. A  replication that is already in progress can continue to run past this  time.": "复制任务的结束时间。已经进行的复制可以继续运行到此时间为止。", "Endpoint": "端点", "Endpoint Type": "端点类型", "Endpoint URL": "端点URL", "Endpoint type to choose from the service catalogue. <i>Public</i> is recommended, see the <a href=\"https://rclone.org/swift/#standard-options\" target=\"_blank\">rclone documentation</a>.": "要从服务目录中选择的端点类型。建议使用<i>公开</i>，请参见<a href=\"https://rclone.org/swift/\\#standard-options\" target=\"_blank\"> rclone文档</a>。", "Ensure Display Device": "确保显示设备", "Ensure valid entries exist in  <b>Directory Services > Kerberos Realms</b> and  <b>Directory Services > Kerberos Keytabs</b> and the system  can communicate with the Kerberos Domain Controller before  enabling this option.": "确保在<b>目录服务>Kerberos领域</b>和<b>目录服务>Kerberos密钥表</b>中存在有效的条目，并且在启用此选项之前，系统可以与Kerberos域控制器进行通信。", "Enter": "输入", "Enter <strong>{pool}</strong> below to confirm": "在下方输入 <strong>{pool}</strong> 进行确认", "Enter <strong>{zvolName}</strong> below to confirm.": "输入存储池名 <strong>{zvolName}</strong> 以确认。", "Enter a Name (optional)": "输入名称（可选）", "Enter a VNC password to automatically pass to the VNC session. Passwords cannot be longer than 8 characters.": "输入VNC密码以自动传递到VNC会话。密码不能长于8个字符。", "Enter a description of the Cloud Sync Task.": "输入云同步任务的描述。", "Enter a description of the cron job.": "输入计划任务的描述。", "Enter a description of the interface.": "输入接口的描述。", "Enter a description of the rsync task.": "输入rsync任务的描述。", "Enter a description of the static route.": "输入静态路由的描述。", "Enter a description of the tunable.": "输入可调参数的描述。", "Enter a descriptive title for the new issue.": "输入描述这个问题的标题。", "Enter a list of IP addresses to  <a href=\"https://man7.org/linux/man-pages/man8/ping.8.html\" target=\"_blank\">ping</a>.  Separate entries by pressing <code>Enter</code>. Each address is tried  until one is successful or the list is exhausted. Leave empty to use  the default gateway.": "输入IP地址列表以 <a href=\"https://man7.org/linux/man-pages/man8/ping.8.html\" target=\"_blank\">ping</a>. 使用 <code>Enter</code> 以分隔条目。在遇到可用地址或整个条目都被遍历之前，每个地址都将被尝试。留空以使用默认网关。", "Enter a list of allowed hostnames or IP addresses. Separate entries by pressing <code>Enter</code>. A more detailed description  with examples can be found  <a href=\"https://www.samba.org/samba/docs/current/man-html/smb.conf.5.html#HOSTSALLOW\" target=\"_blank\">here</a>. <br><br>  If neither *Hosts Allow* or *Hosts Deny* contains  an entry, then SMB share access is allowed for any host. <br><br>  If there is a *Hosts Allow* list but no *Hosts Deny* list, then only allow  hosts on the *Hosts Allow* list. <br><br>  If there is a *Hosts Deny* list but no *Hosts Allow* list, then allow all  hosts that are not on the *Hosts Deny* list. <br><br>  If there is both a *Hosts Allow* and *Hosts Deny* list, then allow all hosts  that are on the *Hosts Allow* list. <br><br>  If there is a host not on the *Hosts Allow* and not on the *Hosts Deny* list,  then allow it.": "Enter a list of allowed hostnames or IP addresses. Separate entries by pressing <code>Enter</code>. A more detailed description  with examples can be found  <a href=\"https://www.samba.org/samba/docs/current/man-html/smb.conf.5.html\\#HOSTSALLOW\" target=\"_blank\">here</a>. <br><br>  If neither *Hosts Allow* or *Hosts Deny* contains  an entry, then SMB share access is allowed for any host. <br><br>  If there is a *Hosts Allow* list but no *Hosts Deny* list, then only allow  hosts on the *Hosts Allow* list. <br><br>  If there is a *Hosts Deny* list but no *Hosts Allow* list, then allow all  hosts that are not on the *Hosts Deny* list. <br><br>  If there is both a *Hosts Allow* and *Hosts Deny* list, then allow all hosts  that are on the *Hosts Allow* list. <br><br>  If there is a host not on the *Hosts Allow* and not on the *Hosts Deny* list,  then allow it.", "Enter a list of chat IDs separated by space, comma or semicolon.  To find your chat ID send a message to the bot, group or channel and visit  <a href=\"https://api.telegram.org/bot<BOT_TOKEN>/getUpdates\"  target=\"_blank\">https://api.telegram.org/bot<BOT_TOKEN>/getUpdates</a>.": "输入以空格、逗号或分号分隔的聊天ID列表。要查找您的聊天ID，请向机器人、群组或频道发送消息并访问 <a href=\"https://api.telegram.org/bot<BOT_TOKEN>/getUpdates\" target=\"_blank\">https ://api.telegram.org/bot<BOT_TOKEN>/getUpdates</a>。", "Enter a list of denied hostnames or IP addresses. Separate entries by pressing <code>Enter</code>.  If neither *Hosts Allow* or *Hosts Deny* contains  an entry, then SMB share access is allowed for any host. <br><br>  If there is a *Hosts Allow* list but no *Hosts Deny* list, then only allow  hosts on the *Hosts Allow* list. <br><br>  If there is a *Hosts Deny* list but no *Hosts Allow* list, then allow all  hosts that are not on the *Hosts Deny* list. <br><br>  If there is both a *Hosts Allow* and *Hosts Deny* list, then allow all hosts  that are on the *Hosts Allow* list. <br><br>  If there is a host not on the *Hosts Allow* and not on the *Hosts Deny* list,  then allow it.": "输入被拒绝的主机名或IP地址的列表。按<code>Enter</code>分隔条目。如果*允许主机*或*拒绝主机*都不包含条目，则允许任何主机访问SMB共享。<br><br>如果有*允许主机*列表但没有*拒绝主机*列表，则只允许*允许主机*列表中的主机。<br><br>如果有*拒绝主机*列表但没有*允许主机*列表，则允许所有不在*拒绝主机*列表中的主机。<br><br> 如果同时存在*允许主机*和*拒绝主机* 列表，则允许*允许主机*列表中的所有主机。<br><br>如果主机不在*允许主机*和*拒绝主机*列表中，则允许它", "Enter a long string of random characters for  use as <a href=\"https://searchsecurity.techtarget.com/definition/salt\"  target=\"_blank\">salt</a> for the encryption password. <b>Warning:</b>  Always securely back up the encryption salt value! Losing the salt  value will result in data loss.": "输入一长串随机字符，用作<a href=\"https://searchsecurity.techtarget.com/definition/salt\"  target=\"_blank\">盐</a>作为加密密码。<b>警告：</b>一定要安全备份加密盐值！丢失盐值将导致数据丢失。", "Enter a name for the interface. Use the format <samp>bond<i>X</i></samp>, <samp>vlan<i>X</i></samp>, or <samp>br<i>X</i></samp> where <i>X</i> is a number representing a non-parent interface. Read-only when editing an interface.": "输入接口的名称。使用格式<samp>bond<i>X</i></samp>、<samp>vlan<i>X</i></samp>或<samp>br<i>X</i> </samp>其中<i>X</i> 是代表非父接口的数字。编辑界面时只读。", "Enter a name for the new credential.": "输入新凭证的名称。", "Enter a name for the share.": "输入共享的名称。", "Enter a name for this Keytab.": "输入此Keytab的名称。", "Enter a number of seconds to wait before alerting that the service cannot reach any UPS. Warnings continue until the situation is fixed.": "输入秒数以等待警告服务无法到达任何UPS之前等待。警告持续直到问题被解决。", "Enter a one to three paragraph summary of the issue. Describe the problem and provide any steps to replicate the issue.": "输入一到三段的问题描述。描述问题并提供复现问题的任何步骤。", "Enter a password for the <i>rancher</i> user. This is used to log in to the VM from the serial shell.": "输入用于<i>rancher</i>用户的密码。这将会用于从串行shell登录虚拟机。", "Enter a password of at least eight characters.": "输入至少8位字符的密码。", "Enter a port number to use for the connection.": "输入用于连接的端口号。", "Enter a port to bind <a href=\"https://linux.die.net/man/8/rpc.lockd\" target=\"_blank\">rpc.lockd(8)</a>.": "输入一个端口以绑定 <a href=\"https://linux.die.net/man/8/rpc.lockd\" target=\"_blank\">rpc.lockd(8)</a>.", "Enter a port to bind <a href=\"https://man7.org/linux/man-pages/man8/mountd.8.html\" target=\"_blank\">mountd(8)</a>.": "输入一个端口以绑定 <a href=\"https://man7.org/linux/man-pages/man8/mountd.8.html\" target=\"_blank\">mountd(8)</a>.", "Enter a port to bind <a href=\"https://man7.org/linux/man-pages/man8/statd.8.html\" target=\"_blank\">rpc.statd(8)</a>.": "输入一个端口以绑定 <a href=\"https://man7.org/linux/man-pages/man8/statd.8.html\" target=\"_blank\">rpc.statd(8)</a>.", "Enter a separate privacy passphrase. <b>Password</b> is used when this is left empty.": "输入单独的隐私密码。在留空时则使用<b>密码</b>。", "Enter a unique name for the dataset. The dataset name length is calculated by adding the length of this field's value and the length of the parent path field value. The length of 'Parent Path' and 'Name' added together cannot exceed 200 characters. Which is why, length validation on this field accounts for the parent path as well. Furthermore, the maximum nested directory levels allowed is 50. You can't create a dataset that's at the 51st level in the directory heirarchy after you account for the nested levels in the parent path.": "输入数据集的唯一名称。 数据集名称长度是通过将此字段值的长度与父路径字段值的长度相加来计算的。 “父路径”和“名称”加在一起的长度不能超过 200 个字符。 这就是为什么，这个字段的长度验证也考虑了父路径。 此外，允许的最大嵌套目录级别为 50。在考虑父路径中的嵌套级别后，您无法创建位于目录层次结构中第 51 级的数据集。", "Enter a user to associate with this service. Keeping the default is recommended.": "输入要与此服务关联的用户。建议保留默认值。", "Enter a username to register with this service.": "输入用户名来注册这个服务。", "Enter a valid IPv4 address.": "输入有效的IPv4地址。", "Enter a valid email address to receive alerts from this system.": "输入有效的电子邮件地址，以接收来自此系统的警报。", "Enter a value in seconds for the the UPS to wait before initiating shutdown. Shutdown will not occur if power is restored while the timer is counting down. This value only applies when <b>Shutdown mode</b> is set to <i>UPS goes on battery</i>.": "输入以秒为单位的值，以使UPS在启动关机之前等待。如果在计时器递减计数时恢复供电，则不会发生关机。仅当<b>关机模式</b>设置为<i> 使用UPS电池</i>时，此值才适用。", "Enter a value to use for the <a href=\"https://man7.org/linux/man-pages/man8/sysctl.8.html\" target=\"_blank\">sysctl</a> variable.": "输入一个 <a href=\"https://man7.org/linux/man-pages/man8/sysctl.8.html\" target=\"_blank\">sysctl</a> 使用的变量", "Enter accounts that have administrative access. See <a href=\"https://linux.die.net/man/5/upsd.users\" target=\"_blank\">upsd.users(5)</a> for examples.": "输入有管理员权限的账号。参阅 <a href=\"https://linux.die.net/man/5/upsd.users\" target=\"_blank\">upsd.users(5)</a> 以获取实例。", "Enter additional <b>smb.conf</b> options. See the  <a href=\"http://www.oreilly.com/openbook/samba/book/appb_02.html\" target=\"_blank\">Samba Guide</a>  for more information on these settings.<br>  To log more details when a client attempts to authenticate to the share, add  <code>log level = 1, auth_audit:5</code>.": "输入其他<b>smb.conf</b>选项。有关这些设置的更多信息，请参阅<a href=\"http://www.oreilly.com/openbook/samba/book/appb_02.html\" target=\"_blank\">Samba指南</a>。<br>要在客户端尝试对共享进行身份验证时记录更多详细信息，请添加 <code>log level = 1, auth_audit:5</code>。", "Enter an IPv4 address. This overrides the default gateway provided by DHCP.": "输入IPv4地址。这将会覆盖DHCP分配的默认网关。", "Enter an IPv6 address. This overrides the default gateway provided by DHCP.": "输入IPv6地址。这将会覆盖DHCP分配的默认网关。", "Enter an alphanumeric encryption key. Only available when <i>Passphrase</i> is the chosen key format.": "输入字母数字加密密钥。仅当<i>密码</i>是所选的密钥格式时才可用。", "Enter an alphanumeric name for the certificate. Underscore (_), and dash (-) characters are allowed.": "输入证书的字母数字名称。允许使用下划线(_)和破折号(-)。", "Enter an alphanumeric name for the virtual machine.": "输入虚拟机的字母数字名称。", "Enter an optimal number of threads used by the kernel NFS server.": "输入内核NFS服务器使用的线程数", "Enter any additional <a href=\"http://net-snmp.sourceforge.net/docs/man/snmpd.conf.html\" target=\"_blank\">snmpd.conf(5)</a> options. Add one option for each line.": "输入任何<a href=\"http://net-snmp.sourceforge.net/docs/man/snmpd.conf.html\" target=\"_blank\">snmpd.conf(5)</a> 附加选项。为每行添加一个选项。", "Enter any additional parameters from <a href=\"https://www.samba.org/ftp/rsync/rsyncd.conf.html\" target=\"_blank\">rsyncd.conf(5)</a>.": "输入<a href=\"https://www.samba.org/ftp/rsync/rsyncd.conf.html\" target=\"_blank\">rsyncd.conf(5)</a>中的附加参数 。", "Enter any aliases, separated by spaces. Each alias can be up to 15 characters long.": "输入别名，以空格分隔。每个别名最多可以包含15个字符。", "Enter any email addresses to receive status updates. Separate entries by pressing <code>Enter</code>.": "输入任何电子邮件地址以接收状态更新。按<code>Enter</code>分隔条目。", "Enter any extra options from <a href=\"http://networkupstools.org/docs/man/ups.conf.html\" target=\"_blank\">UPS.CONF(5)</a>.": "输入<a href=\"http://networkupstools.org/docs/man/ups.conf.html\" target=\"_blank\">UPS.CONF(5)</a>的附加选项。", "Enter any extra options from <a href=\"http://networkupstools.org/docs/man/upsd.conf.html\" target=\"_blank\">UPSD.CONF(5)</a>.": "输入<a href=\"http://networkupstools.org/docs/man/upsd.conf.html\" target=\"_blank\">UPSD.CONF(5)</a>的附加选项。", "Enter any information about this S.M.A.R.T. test.": "输入有关此S.M.A.R.T.测试的任何信息。", "Enter any notes about this dataset.": "输入这个数据集的备注。", "Enter dataset name to continue.": "输入数据集名称以继续。", "Enter or paste a string to use as the encryption key for this dataset.": "输入或粘贴字符串以用作此数据集的加密密钥。", "Enter or paste the \"integration/service\" key for this system to access the <a href=\"https://v2.developer.pagerduty.com/v2/docs/events-api\" target=\"_blank\">PagerDuty API</a>.": "输入或粘贴\"集成/服务\"密钥以供该系统访问<a href=\"https://v2.developer.pagerduty.com/v2/docs/events-api\" target=\"_blank\">PagerDuty API</a> 。", "Enter or paste the <a href=\"https://docs.mattermost.com/developer/webhooks-incoming.html\" target=\"_blank\">incoming webhook</a> URL associated with this service.": "输入或粘贴与此服务关联的<a href=\"https://docs.mattermost.com/developer/webhooks-incoming.html\" target=\"_blank\">传入webhook</a> URL。", "Enter or paste the <a href=\"https://docs.opsgenie.com/v1.0/docs/api-integration\" target=\"_blank\">API key</a>. Find the API key by signing into the OpsGenie web interface and going to Integrations/Configured Integrations. Click the desired integration, Settings, and read the API Key field.": "输入或粘贴<a href=\"https://docs.opsgenie.com/v1.0/docs/api-integration\" target=\"_blank\">API密钥</a>。通过登录OpsGenie Web界面并转到“集成/已配置的集成”来找到API密钥。单击所需的集成，设置，然后阅读API密钥字段。", "Enter or paste the <a href=\"https://help.victorops.com/knowledge-base/api/\" target=\"_blank\">VictorOps API key</a>.": "输入或粘贴<a href=\"https://help.victorops.com/knowledge-base/api/\" target=\"_blank\">VictorOps API密钥</a> 。", "Enter or paste the <a href=\"https://portal.victorops.com/public/api-docs.html#/Routing32Keys\" target=\"_blank\">VictorOps routing key</a>.": "输入或粘贴<a href=\"https://portal.victorops.com/public/api-docs.html\\#/Routing32Keys\" target=\"_blank\">VictorOps路由密钥</a> 。", "Enter or paste the <b>public</b> SSH key of the user for any key-based authentication. <b>Do not paste the private key.</b>": "输入或粘贴用户的<b>公共</b> SSH密钥以进行基于密钥的身份验证。<b>请勿粘贴私钥。</b>", "Enter or paste the API key provided from <a href=\"https://portal.ixsystems.com/portal/login/\" target=\"_blank\">iXsystems Account Services</a>. Login or signup is required.": "输入或粘贴<a href=\"https://portal.ixsystems.com/portal/login/\" target=\"_blank\">iXsystems帐户服务</a>提供的API密钥。需要登录或注册。", "Enter or select the cloud storage location to use for this task.": "输入或选择要用于此任务的云存储位置。", "Enter password": "输入密码", "Enter password to unlock this display device": "输入密码解锁此显示设备", "Enter password.": "输入密码。", "Enter the <a href=\"https://docs.aws.amazon.com/sns/latest/dg/sms_supported-countries.html\" target=\"_blank\">AWS account region</a>.": "输入<a href=\"https://docs.aws.amazon.com/sns/latest/dg/sms_supported-countries.html\" target=\"_blank\">AWS账户区域</a> 。", "Enter the <a href=\"https://docs.influxdata.com/influxdb/\" target=\"_blank\">InfluxDB</a> hostname.": "输入<a href=\"https://docs.influxdata.com/influxdb/\" target=\"_blank\">InfluxDB</a>主机名。", "Enter the <a href=\"https://kb.iu.edu/d/aiuv\" target=\"_blank\">fully-qualified hostname (FQDN)</a> of the system. This name must be unique within a certificate chain.": "输入系统的<a href=\"https://kb.iu.edu/d/aiuv\" target=\"_blank\">全称域名（FQDN）</a>。该名称在证书链中必须是唯一的。", "Enter the Active Directory administrator account name.": "输入活动目录管理员帐户名称。", "Enter the Active Directory domain (<i>example.com</i>) or child domain (<i>sales.example.com</i>).": "输入活动目录的域（ <i>example.com</i> ）或子域（ <i>sales.example.com</i> ）。", "Enter the IP address and netmask of the server.": "输入服务器的IP地址和网络掩码。", "Enter the IP address of the gateway.": "输入网关的IP地址。", "Enter the IP address or hostname of the VMware host. When clustering, this is the vCenter server for the cluster.": "输入VMware主机的IP地址或主机名。集群时，这是集群的vCenter Server。", "Enter the IP address or hostname of the remote system that will store the copy. Use the format <i>username@remote_host</i> if the username differs on the remote host.": "输入存储副本的远程系统的IP地址或主机名。 如果远程主机的用户名与本地不同，请使用 <i>username@remote_host</i> 的格式。", "Enter the IP address which runs the  S3 service. <i>0.0.0.0</i> tells the server to listen  on all addresses.": "输入运行S3服务的IP地址。<i> 0.0.0.0 </i>告诉服务器监听所有地址。", "Enter the S3 access ID. See <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-sec-cred-types.html#access-keys-and-secret-access-keys\" target=\"_blank\">  Access keys</a> for more information.": "输入S3访问ID。有关更多信息，请参见<a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-sec-cred-types.html\\#access-keys-and-secret-access-keys\" target=\"_blank\">访问密钥</a>。", "Enter the S3 secret access key. See  <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-sec-cred-types.html#access-keys-and-secret-access-keys\" target=\"_blank\">  Access keys</a> for more information.": "输入S3秘密访问密钥。有关更多信息，请参见<a href=\"https://docs.aws.amazon.com/general/latest/gr/aws-sec-cred-types.html\\#access-keys-and-secret-access-keys\" target=\"_blank\">访问密钥</a>。", "Enter the SSH Port of the remote system.": "输入远程系统的SSH端口。", "Enter the TCP port which provides the S3 service.": "输入提供S3服务的TCP端口。", "Enter the command with any options.": "输入带有任何选项的命令。", "Enter the default gateway of the IPv4 connection.": "输入IPv4连接的默认网关。", "Enter the desired address into the field to override the randomized MAC address.": "在字段中输入所需的地址以覆盖随机MAC地址。", "Enter the device name of the interface. This cannot be changed after the interface is created.": "输入接口的设备名称。 创建接口后不能更改。", "Enter the email address of the new user.": "输入新用户的电子邮件地址。", "Enter the email address of the person responsible for the CA.": "输入负责CA的人员的电子邮件地址。", "Enter the email of the contact person. Use the format <i>name</i>@<i>domain.com</i>.": "输入联系人的电子邮件。使用格式<i>名称</i> @ <i> domain.com </i>。", "Enter the filesystem to snapshot.": "输入要快照的文件系统。", "Enter the full path to the command or script to                be run.": "输入要运行的命令或脚本的完整路径。", "Enter the hostname or IP address of the <b>NTP</b> server.": "输入<b>NTP</b>服务器的主机名或IP地址。", "Enter the hostname to connect to.": "输入要连接的主机名。", "Enter the location of the organization. For example, the city.": "输入组织的位置。例如城市。", "Enter the location of the system.": "输入系统的位置。", "Enter the maximum number of attempts before client is disconnected. Increase this if users are prone to typos.": "输入客户端断开连接之前的最大尝试次数。如果用户容易出现错字，请增加此值。", "Enter the name of the Key Distribution Center.": "输入密钥分发中心的名称。", "Enter the name of the company or organization.": "输入公司或组织名。", "Enter the name of the contact person.": "输入联系人的姓名。", "Enter the name of the realm.": "输入Realm的名称。", "Enter the name of the sysctl variable to configure. <i>sysctl</i> tunables are used to configure kernel parameters while the system is running and generally take effect immediately.": "输入要配置的 sysctl 变量的名称。 <i>sysctl</i> 可调参数用于在系统运行时配置内核参数，通常会立即生效。", "Enter the numeric tag configured in the switched network.": "输入在交换网络中配置的数字标签。", "Enter the passphrase for the Private Key.": "输入私钥的密码。", "Enter the password associated with <b>Username</b>.": "输入与<b>用户名</b>关联的密码。", "Enter the password for the SMTP server. Only plain ASCII characters are accepted.": "输入SMTP服务器的密码。仅接受纯ASCII字符。", "Enter the password used to connect to the IPMI interface from a web browser.": "输入用于从Web浏览器连接到IPMI界面的密码。", "Enter the phone number of the contact person.": "输入联系人的电话号码。", "Enter the pre-Windows 2000 domain name.": "输入Windows 2000以前的域名。", "Enter the pre-defined S3 bucket to use.": "输入要使用的预定义S3存储桶。", "Enter the relative distinguished name of the site object in the Active Directory.": "在活动目录中输入站点对象的相对专有名称。", "Enter the state or province of the organization.": "输入组织的州或省。", "Enter the subject for status emails.": "输入状态邮件的主题。", "Enter the user on the VMware host with permission to snapshot virtual machines.": "在具有快照虚拟机权限的VMware主机上输入用户。", "Enter the username if the SMTP server requires authentication.": "如果SMTP服务器需要身份验证，请输入用户名。", "Enter the version to roll back to.": "输入要回滚到的版本。", "Enter vm name to continue.": "输入虚拟机名称以继续。", "Enter {value} below to confirm.": "输入 {value} 以确认。", "Entering <i>0</i> uses the actual file size and requires that the file already exists. Otherwise, specify the file size for the new file.": "输入<i>0</i>使用实际文件大小，并要求该文件已存在。否则，请指定新文件的文件大小。", "Environment": "环境", "Error": "错误", "Error Downloading File": "下载文件时出错", "Error Updating Production Status": "更新生产状态时出错", "Error checking for updates.": "检查更新时出错。", "Error creating CA.": "创建CA时出错。", "Error creating VM.": "创建虚拟机时出错。", "Error deleting dataset {datasetName}.": "删除数据集 {datasetName} 时出错。", "Error details for ": "错误详情", "Error detected reading App": "读取应用程序时检测到错误", "Error exporting the Private Key": "导出私钥时出错", "Error exporting the certificate": "导出证书时出错", "Error exporting/disconnecting pool.": "导出/断开池时出错。", "Error getting chart data": "获取图表数据时出错", "Error importing pool": "导入存储池时发生错误", "Error restarting web service": "重启网络服务时出错", "Error saving ZVOL.": "保存ZVOL时出错。", "Error starting service OpenVPN {serviceLabel}": "启动 OpenVPN {serviceLabel} 时出错", "Error starting service {serviceName}.": "启动服务 {serviceName} 时出错。", "Error stopping service OpenVPN {serviceLabel}": "停止 OpenVPN {serviceLabel} 时出错", "Error stopping service {serviceName}.": "停止服务 {serviceName} 时出错。", "Error submitting file": "提交文件时出错", "Error updating disks": "更新磁盘时出错", "Error while creating the {device} device.\n {reason}": "创建 {device} 设备时出错。\n {reason}", "Error:": "错误", "Error: ": "错误： ", "Errors": "错误", "Establishing a connection requires  that one of the connection systems has open TCP ports. Choose which  system (<i>LOCAL</i> or <i>REMOTE</i>) will open ports. Consult your IT  department to determine which systems are allowed to open ports.": "建立连接要求其中一个连接系统具有开放的TCP端口。选择要打开端口的系统（<i>本地</i>或<i>远程</i>）。请咨询您的IT部门，以确定允许哪些系统打开端口。", "Estimated Raw Capacity": "预计原始容量", "Estimated data capacity available after extension.": "扩展后可用的估计数据容量。", "Estimated raw capacity:": "估计原始容量：", "Estimated total raw data capacity": "估计总原始数据容量", "Everything is fine": "一切都很好", "Example: blob.core.usgovcloudapi.net": "示例：blob.core.usgovcloudapi.net", "Excellent effort": "出色的工作", "Exclude": "排除", "Exclude Child Datasets": "排除子数据集", "Exclude specific child dataset snapshots from  the replication. Use with <b>Recursive</b> snapshots. List child  dataset names to exclude. Separate entries by pressing <code>Enter</code>.  Example: <i>pool1/dataset1/child1</i>. A recursive replication of  <i>pool1/dataset1</i> snapshots includes all child dataset snapshots  except <i>child1</i>.": "从复制中排除特定的子数据集快照。与<b>递归</b>快照一起使用。列出要排除的子数据集名称。通过按<code>Enter</code>分隔条目。示例：<i>pool1/dataset1/child1</i>。<i>pool1/dataset1</i>快照的递归复制包括除<i>child1</i>之外的所有子数据集快照。", "Exclude specific child datasets from the  snapshot. Use with recursive snapshots. List paths to any child  datasets to exclude. Example: <i>pool1/dataset1/child1</i>.  A recursive snapshot of <i>pool1/dataset1</i> will include all child  datasets except <i>child1</i>. Separate entries by pressing  <code>Enter</code>.": "从快照中排除特定的子数据集。与递归快照一起使用。列出要排除的任何子数据集的路径。示例：<i>pool1/dataset1/child1</i>。<i>pool1/dataset1</i>的递归快照将包括除<i>child1</i>之外的所有子数据集。通过按<code>Enter</code>分隔条目。", "Exec": "执行", "Execute": "执行", "Existing Pool": "现有池", "Expand": "扩展", "Expand Row": "扩展行", "Expand pool ": "扩展池", "Expand pool to fit all available disk space.": "扩展池以适合所有可用的磁盘空间。", "Expand row": "扩展行", "Expander Status": "扩展器状态", "Expected Finished Time:": "完成时间：", "Expiration Date": "失效日期", "Export All Keys": "导出所有密钥", "Export Key": "导出密钥", "Export Password Secret Seed": "导出密码秘密种子", "Export Read Only": "启用只读", "Export Recycle Bin": "启用回收站", "Export ZFS snapshots as <a href=\"https://docs.microsoft.com/en-us/windows/desktop/vss/shadow-copies-and-shadow-copy-sets\" target=_blank>Shadow Copies</a> for VSS clients.": "将ZFS快照导出为VSS客户端的<a href=\"https://docs.microsoft.com/en-us/windows/desktop/vss/shadow-copies-and-shadow-copy-sets\" target=_blank>卷影副本</a> 。", "Export/Disconnect": "导出/断开连接", "Export/Disconnect Pool": "导出/断开连接池", "Export/disconnect pool: {pool}": "导出/断开连接池：{pool}", "Exported": "导出", "Exported Pool": "导出池", "Exporting Pool": "正在导出池", "Exporting/disconnecting will continue after services have been managed.": "管理完服务后，导出/断开连接将继续。", "Expose zilstat via SNMP": "通过SNMP公开zilstat", "Extend": "扩展", "Extend Vdev": "扩展Vdev", "Extended Key Usage": "扩展密钥用法", "Extent": "区块", "Extent Name": "区块名称", "Extent Type": "区块类型", "Extents": "范围", "Extra Constraints": "额外的约束", "Extra Users": "附加用户", "FAULT": "故障", "FRONT": "前", "FTP Host to connect to. Example: <i>ftp.example.com</i>.": "要连接的FTP主机。例如：<i> ftp.example.com </i>。", "FTP Port number. Leave blank to use the default port <i>21</i>.": "FTP端口号。保留空白以使用默认端口<i> 21 </i>。", "Failed": "失败", "Failed Disks": "故障磁盘", "Failed S.M.A.R.T. Tests": "失败的 S.M.A.R.T. 测试", "Failed sending test alert!": "发送测试警报失败！", "Failed to load datasets": "无法加载数据集", "Failover": "故障转移", "Failover Configuration": "故障转移配置", "Failover Group": "故障转移组", "Failover is administratively disabled.": "故障转移在管理上被禁用。", "Failover is in an error state.": "故障转移处于错误状态。", "Features": "特征", "Feb": "二月", "Fenced is not running.": "Fenced 未运行", "Fetch DataStores": "获取数据存储", "Fetching Encryption Summary": "获取加密摘要", "Fetching Encryption Summary for ": "获取加密摘要", "Fetching data...": "正在获取数据...", "Fibre Channel Port {name} update successful.": "光纤通道端口 {name} 更新成功。", "Fibre Channel Ports": "光纤通道端口", "Field is required": "栏目是必需的", "File": "文件", "File Inherit": "文件继承", "File Mask": "文件掩码", "File Permissions": "文件权限", "File Ticket": "反馈(File Ticket)", "File size is limited to 50 MiB.": "文件大小限制为 50 MiB。", "File upload complete": "文件上传完成", "Filename Encryption": "文件名加密", "Files are split into chunks of this size  before upload. The number of chunks that can be simultaneously  transferred is set by the <b>Transfers</b> number. The single  largest file being transferred must fit into no more than 10,000  chunks.": "上传之前，文件将被拆分为该大小的块。可以同时传输的块数由<b> Transfers </b>数设置。传输的单个最大文件不得超过10,000个块。", "Files that are deleted from the same  dataset are moved to the Recycle Bin and do not take any additional  space. <b>Deleting files over NFS will remove the files permanently  </b>. When the files are in a different dataset or a child dataset,  they are copied to the dataset where the Recycle Bin is located. To  prevent excessive space usage, files larger than 20 MiB are deleted  rather than moved. <b>This is not a replacement  for ZFS snapshots.</b>": "从同一数据集删除的文件将移动到回收站，并且不会占用任何额外空间。<b>通过NFS删除文件将永久删除文件</b>。当多个文件位于不同的数据集或位于一个子数据集时，它们将被复制到回收站所在的数据集。为防止空间占用过大，大于 20 MiB 的文件将被直接删除而不是移动到回收站。<b>这不是ZFS快照的替代方案。</b>", "Filesize": "文件大小", "Filter": "过滤器", "Filter Groups": "过滤组", "Filter Users": "过滤用户", "Filter disks by capacity": "按容量过滤磁盘", "Filter disks by name": "按名称过滤磁盘", "Finding Pools": "查找池", "Finding pools to import...": "正在查找要导入的池...", "Finish Upgrade?": "完成升级吗？", "Finished": "已完成", "Finished Resilver on {date}": "{date}完成重新同步", "Finished Scrub on {date}": "{date}完成擦除", "First vdev has {n} disks, new vdev has {m}": "第一个vdev拥有 {n} 磁盘，新的vdev具有 {m}", "First vdev is a {vdevType}, new vdev is {newVdevType}": "第一个vdev是一个 {vdevType}，新的vdev是 {newVdevType}", "Fix Credential": "修复凭证", "Fix database": "修复数据库", "Flags": "标记", "Flags Advanced": "高级标记", "Flags Basic": "基本标志", "Flags Type": "标记类型", "Flashing stopped.": "刷入已停止", "Flip": "翻转", "Folder": "文件夹", "Follow Symlinks": "遵循符号链接", "Follow symlinks and copy the items to which they link.": "遵循符号链接并复制它们链接的项目。", "Following container images are available to update:\n": "以下容器映像可更新:\n", "Force": "强制", "Force Clear": "强制清除", "Force Create": "强制创建", "Force Delete": "强制删除", "Force Delete?": "强制删除？", "Force Stop After Timeout": "超时后强制停止", "Force delete": "强制删除", "Force deletion of dataset <i>{datasetName}</i>?": "强制删除数据集 <i>{datasetName}</i>？", "Force size": "强制大小", "Force the VM to stop if it has not already  stopped within the specified shutdown timeout. Without this option selected, the VM will  receive the shutdown signal, but may or may not complete the shutdown process.": "如果尚未在指定的关闭超时时间内停止虚拟机，则强制其停止。如果未选择此选项，则虚拟机将收到关闭信号，但可能不会完成关闭过程。", "Force unmount": "强制卸载", "Force using  <a href=\"https://docs.aws.amazon.com/general/latest/gr/signature-version-2.html\"  target=\"_blank\">Signature Version 2</a> to sign API requests. Set this  when configuring a custom Endpoint URL.": "强制使用<a href=\"https://docs.aws.amazon.com/general/latest/gr/signature-version-2.html\"  target=\"_blank\">签名版本2</a>签署API请求。在配置自定义端点URL时进行设置。", "Forces the addition of the <b>NTP</b> server, even if it is currently unreachable.": "强制添加<b>NTP</b>服务器，即使它当前无法访问。", "Forcing the other TrueNAS controller to  become active requires a failover. This will temporarily interrupt  system services. After confirmation, <b>SAVE AND FAILOVER</b> must  be clicked on the previous screen.": "强制另一个 TrueNAS 控制器变为活动状态需要故障转移。这将暂时中断系统服务。确认后，必须在前一个屏幕上点击 <b>SAVE AND FAILOVER</b>。", "Free": "空闲", "Free Space": "可用空间", "Frequency": "频率", "Fri": "周五", "Friday": "星期五", "From": "从", "From <a  href=\"https://www.samba.org/ftp/rsync/rsyncd.conf.html\"  target=\"_blank\">rsyncd.conf(5)</a>. A list of patterns to match with  the hostname and IP address of a connecting client. The connection is  rejected if no patterns match. Separate entries by pressing  <code>Enter</code>.": "来自<a  href=\"https://www.samba.org/ftp/rsync/rsyncd.conf.html\"  target=\"_blank\">rsyncd.conf(5)</a>。与连接客户端的主机名和IP地址匹配的模式列表。如果没有模式匹配，则拒绝连接。通过按<code> Enter </ code>分隔条目。", "From <a  href=\"https://www.samba.org/ftp/rsync/rsyncd.conf.html\"  target=\"_blank\">rsyncd.conf(5)</a>. A list of patterns to match with  the hostname and IP address of a connecting client. The connection is  rejected when the patterns match. Separate entries by pressing  <code>Enter</code>.": "来自<a  href=\"https://www.samba.org/ftp/rsync/rsyncd.conf.html\"  target=\"_blank\">rsyncd.conf(5)</a>。与连接客户端的主机名和IP地址匹配的模式列表。模式匹配时，连接被拒绝。通过按<code> Enter </ code>分隔条目。", "From Email": "发信电子邮件地址", "From Name": "发信人名称", "From {task_begin} to {task_end}": "从 {task_begin} 到 {task_end}", "Full": "完全", "Full Control": "完全控制", "Full Filesystem Replication": "完整的文件系统复制", "Full Name": "全名", "Full path to the pool or dataset to share. Mandatory.": "共享的存储池或数据集的完整路径。", "Full with random data": "用随机数据进行填充", "Full with zeros": "用0进行填充", "Fully qualified domain name of the host with the  dynamic IP address. Separate multiple domains with a space, comma  (,), or semicolon (;). Example:  <i>myname.dyndns.org; myothername.dyndns.org</i>.": "具有动态IP地址的主机的全限定域名。用空格，逗号（,）或分号（;）分隔多个域。示例：<i> myname.dyndns.org; myothername.dyndns.org </i>。", "Function": "功能", "GID": "GID", "GPU": "GPU", "GPU's": "GPU的", "GUI": "GUI", "GUI SSL Certificate": "GUI SSL证书", "GUI Settings": "GUI设置", "Gateway": "网关", "Gathering data...": "正在收集数据...", "General": "常规", "General Options": "常规选项", "General VM Settings": "常规虚拟机设置", "Generate": "产生", "Generate Debug File": "生成调试文件", "Generate Encryption Key": "生成加密密钥", "Generate Key": "生成密钥", "Generate Keypair": "生成密钥对", "Generate New": "新建", "Generate a name for the snapshot  using the naming schema from a previously created <b>Periodic Snapshot Task</b>.  This allows the snapshot to be replicated. Cannot be used with a <i>Name</i>.": "使用来自先前创建的<b>定期快照任务</b>的命名惯例为快照生成名称。这样可以复制快照。不能与<i>名称</i>一起使用。", "Generate an alert when the    pool has this percent space remaining. This is typically    configured at the pool level when using zvols or at the extent level    for both file and device based extents.": "当池剩余此百分比空间时生成警报。这是使用zvols或使用基于文件和设备的区块时的池级别配置。", "Generate idmap low range based on same algorithm that SSSD uses by default.": "根据SSSD默认使用的相同算法生成idmap低范围。", "Get Support": "获得支持", "GiB": "GiB", "Gives control of how much of the new device is made available to ZFS. Set to use the entire capacity of the new device.": "控制可以向ZFS提供多少新设备。设置为使用新设备的全部容量。", "Global Configuration": "全局配置", "Global SED Password": "全局 SED 密码", "Global password to unlock SEDs.": "用于解锁SED的全局密码。", "Gmail credentials have been applied.": "Gmail密钥已应用。", "Go To Encryption Root": "转到加密根目录", "Go to ACL Manager": "转到ACL管理器", "Go to Active Directory Form": "转到活动目录表单", "Go to HA settings": "转到HA设置", "Go to Storage": "转到存储", "Graph Age in Months": "每月的图表年龄", "Graphite Separate Instances": "Graphite独立实例", "Group": "群组", "Group Bind Path": "用户组 Bind 路径", "Group Configuration": "组配置", "Group Data Quota ": "组数据配额 ", "Group ID": "群组ID", "Group ID created in  Authorized Access. Required when the Discovery Authentication Method is set to  CHAP or Mutual CHAP.": "在授权访问中创建的组ID。将“发现身份验证方法”设置为CHAP或“相互CHAP”时需要。", "Group Obj": "组对象", "Group Object Quota": "组对象配额", "Group Quotas": "组配额", "Group deleted": "群组已删除", "Group members": "组成员", "Group name cannot begin with a hyphen (<i>-</i>) or contain a space, tab, or these characters: <i>, : + & # % ^ ( ) ! @ ~ * ? < > =</i>. <i>$</i> can only be used as the last character of the username.": "组名不能以连字符（ <i>-</i> ）开头，也不能包含空格，制表符或这些字符： <i>，+：＆＃％^（）！ @〜*？ &lt;&gt; =</i>。<i>$</i>只能用作用户名的最后一个字符。", "Group to run as during file transfers to and  from this module.": "在与该模块之间进行文件传输期间运行的组。", "Group to which this ACL entry applies.": "此ACL条目适用的组。", "Group:": "组：", "Groups could not be loaded": "无法加载群组", "Guest Account": "访客账户", "Guest Operating System": "客机操作系统", "Guide": "指导", "HA Disabled": "HA已禁用", "HA Enabled": "HA已启用", "HA Status": "HA状态", "HA is administratively disabled.": "HA在管理上被禁用。", "HA is enabled": "HA已启用", "HA is enabled.": "HA已启用。", "HA is in a faulted state": "HA处于故障状态", "HA is reconnecting.": "HA正在重新连接。", "HDD Standby": "HDD休眠", "HEX": "HEX", "HTTP Authentication": "HTTP身份验证", "HTTP Port": "HTTP端口", "HTTP Proxy": "HTTP代理", "HTTP Proxy:": "HTTP代理：", "HTTP host URL.": "HTTP主机地址。", "HTTPS Port": "HTTPS端口", "HTTPS Protocols": "HTTPS协议", "Hardware": "硬件", "Hardware Disk Encryption": "硬件磁盘加密", "Healthy": "健康", "Help": "帮助", "Hidden": "隐蔽", "Hidden columns are not available for sorting or filtering. Hiding columns improves performance.": "隐藏的栏目不可用于排序或筛选。隐藏栏目可以提高性能。", "Hide": "隐藏", "Hide Extra Columns": "隐藏多余的列", "Hide Standard Error": "隐藏标准错误", "Hide Standard Output": "隐藏标准输出", "Hide Stderr": "隐藏标准错误", "Hide Stdout": "隐藏标准输出", "Hide error output (stderr) from the command.                When unset, any error output is mailed to the user                account cron used to run the command.": "隐藏命令中的错误输出（stderr）。取消设置后，所有错误输出都会邮寄到用于运行命令的用户帐户cron。", "Hide from MSR": "从MSR隐藏", "Hide standard output (stdout) from the command.                When unset, any standard output is mailed to the user                account cron used to run the command.": "隐藏命令中的标准输出（stdout）。取消设置后，所有标准输出都会邮寄到用于运行命令的用户帐户cron。", "Highest Temperature": "最高温度", "Highest Usage": "最高使用率", "Highest Usage:": "最高使用率：", "Highest port number of the  active side listen address that is open to connections. The first  available port between the minimum and maximum is used.": "对连接开放的活动侧侦听地址的最高端口号。使用最小和最大之间的第一个可用端口。", "History": "历史", "Hold": "保持", "Home": "家", "Home Directory": "主目录", "Home Directory Permissions": "主目录权限", "Host": "主机", "Host Allow": "允许主机", "Host Deny": "禁止主机", "Host Name Database": "主机名数据库", "Host Sync": "同步主机", "Host name of second TrueNAS controller.": "第二个TrueNAS控制器的主机名。", "Host name or IP address of the central key server.": "中央密钥服务器的主机名或IP地址。", "Hostname": "主机名", "Hostname (TrueNAS Controller 2)": "主机名（TrueNAS 控制器 2）", "Hostname (Virtual)": "主机名（虚拟）", "Hostname Database:": "主机名数据库：", "Hostname and Domain": "主机名和域名", "Hostname or IP  address used to connect to the active side system. When the active side  is <i>LOCAL</i>, this defaults to the <i>SSH_CLIENT</i> environment  variable. When the active side is <i>REMOTE</i>, this defaults to the  SSH connection hostname.": "用于连接到活动侧系统的主机名或IP地址。当活动端为<i>本地</i>时，默认为<i> SSH_CLIENT </i>环境变量。 当活动端为<i>远程</i>时，默认为SSH连接主机名。", "Hostname or IP address of SMTP server to use for sending this email.": "用于发送此电子邮件的SMTP服务器的主机名或IP地址。", "Hostname or IP address of a remote   <a href=\"http://graphiteapp.org/\" target=\"_blank\">Graphite</a> server.": "远程<a href=\"http://graphiteapp.org/\" target=\"_blank\">Graphite</a>服务器的主机名或IP地址。", "Hostname or IP address of the remote system.": "远程系统的主机名或IP地址。", "Hostname or IP address of the remote system. A  valid URL scheme is required. Example:  <b>https://<i>***********</i></b>": "远程系统的主机名或IP地址。必须使用有效的URL方案。例如：<b>https://<i>***********</i></b>", "Hostname or IP address of the system to  receive SNMP trap notifications.": "接收SNMP陷阱通知的系统的主机名或IP地址。", "Hostname:": "主机名：", "Hostnames or IP addresses of the  ISNS servers to be registered with the iSCSI targets and portals of  the system. Separate entries by pressing <code>Enter</code>.": "要向系统的iSCSI目标和门户注册的ISNS服务器的主机名或IP地址。通过按<code> Enter </ code>分隔条目。", "Hosts": "主机", "Hosts Allow": "允许主机", "Hosts Deny": "禁止主机", "Hot Spare": "热备件", "Hottest": "最热", "Hottest:": "最热：", "Hour": "小时", "Hour and minute the system must stop  creating snapshots. Snapshots already in progress will continue until  complete.": "小时和分钟，系统必须在这个时间内停止创建快照。已经进行的快照将继续，直到完成。", "Hour and minute when the system can begin  taking snapshots.": "小时和分钟，系统可以开始创建快照的时间。", "Hour(s)": "小时", "Hours": "小时", "Hours when this task will run.": "此任务将运行的小时数。", "Hours/Days": "小时/天", "How long a snapshot remains on the  destination system. Enter a number and choose a measure of time from  the drop-down.": "快照在目标系统上保留多长时间。 输入一个数字，然后从下拉菜单中选择一个时间度量。", "How many non-self-issued intermediate certificates that can follow this certificate in a valid certification path. Entering <i>0</i> allows a single additional certificate to follow in the certificate path. Cannot be less than <i>0</i>.": "在有效的认证路径中可以遵循此证书的非自颁发的中间证书数。输入 <i>0</i>允许在证书路径中遵循单个附加证书。不能小于<i>0</i>。", "How often the IP is checked in seconds.": "检查IP的频率（以秒为单位）。", "How often to run the scrub task. Choose one  of the presets or choose <i>Custom</i> to use the advanced scheduler.": "多久运行一次校验任务。选择一种预设，或选择<i>自定义</ i>以使用高级计划程序。", "How permissions are applied to the share. <i>Allowed</i> denies all permissions by default except those that are manually defined. <i>Denied</i> allows all permissions by default except those that are manually defined.": "如何将权限应用于共享。默认情况下，<i>允许</ i>拒绝所有权限，但手动定义的权限除外。默认情况下，<i>拒绝</ i>允许所有权限，但手动定义的权限除外。", "How the <i>Permissions</i> are applied to the chosen <i>Who</>. Choose <i>Allow</i> to grant the specified permissions and <i>Deny</i> to restrict the specified permissions.": "<i>权限</ i>如何应用于所选的<i>谁</>。选择<i>允许</ i>授予指定的权限，选择<i>拒绝</ i>限制指定的权限。", "How the dataset is secured. Choose between securing with an encryption <i>Key</i> or a user-defined <i>Passphrase</i>. Creating a new key file invalidates any previously downloaded key file for this dataset. Delete any previous key file backups and back up the new key file.": "如何保护数据集。在使用加密<i>密钥</i>或用户定义的<i>密码</i>进行保护之间进行选择。创建新的密钥文件会使之前为该数据集下载的任何密钥文件失效。删除任何以前的密钥文件备份并备份新的密钥文件。", "How this ACE is applied to newly created directories and files within the dataset. Basic flags enable or disable ACE inheritance. Advanced flags allow further control of how the ACE is applied to files and directories in the dataset.": "如何将此ACE应用于数据集中的新创建目录和文件。基本标志启用或禁用ACE继承。高级标志允许进一步控制ACE如何应用于数据集中的文件和目录。", "How to configure the connection:<br><br>  <i>Manual</i> requires configuring authentication on the remote system.  This can include copying SSH keys and modifying the <i>root</i> user  account on that system.<br><br>  <i>Semi-automatic</i> only works when configuring an SSH connection  with a remote TrueNAS system. This method uses the URL and login  credentials of the remote system to connect and exchange SSH keys.": "如何配置连接：<br><br> <i>手册</i>需要在远程系统上配置身份验证。这可以包括复制SSH密钥和修改该系统上的<i>root</i>用户帐户。<br><br> <i>半自动</i>仅在配置与远程TrueNAS系统的SSH连接时有效。此方法使用远程系统的URL和登录凭据来连接和交换SSH密钥。", "I Agree": "我同意", "I Understand": "我了解", "I understand": "我明白", "IBurst": "突发", "ID": "ID", "IGNORE": "忽视", "INFO": "信息", "IP Address": "IP 地址", "IP Addresses": "IP地址", "IP address of the remote system with <i>UPS Mode</i> set as <i>Master</i>. Enter a valid IP address in the format <i>***********</i>.": "将<i> UPS模式</i>设置为<i> 主 </i>的远程系统的IP地址。输入<i> *********** </i>格式的有效IP地址。", "IP address on which the connection <b>Active Side</b> listens. Defaults to <i>0.0.0.0</i>.": "<b>Active Side</b>侦听连接的IP地址。默认为<i>0.0.0.0</i>。", "IPMI": "IPMI", "IPMI Configuration": "IPMI配置", "IPMI Flash Duration": "IPMI 闪光持续时间", "IPMI Identify": "IPMI 识别", "IPMI Password Reset": "IPMI密码重置", "IPv4": "IPv4", "IPv4 Address": "IPv4 地址", "IPv4 Default Gateway": "IPv4 默认网关", "IPv4 Netmask": "IPv4 子网掩码", "IPv6": "IPv6", "IPv6 Default Gateway": "IPv6默认网关", "ISNS Servers": "ISNS服务器", "ISO save location": "ISO保存位置", "ISO upload location": "ISO上传位置", "Icon URL": "图标URL", "Icon file to use as the profile  picture for new messages. Example:  <i>https://mattermost.org/wp-content/uploads/2016/04/icon.png</i>.<br>  Requires configuring Mattermost to <a  href=\"https://docs.mattermost.com/administration/config-settings.html#enable-integrations-to-override-profile-picture-icons\" target=\"_blank\">override profile picture icons</a>.": "用作新消息的个人资料图片的图标文件。示例：<i>https://mattermost.org/wp-content/uploads/2016/04/icon.png</i>。<br>要求将Mattermost配置为<a  href=\"https://docs.mattermost.com/administration/config-settings.html\\#enable-integrations-to-override-profile-picture-icons\" target=\"_blank\">覆盖个人资料图片图标</a>。", "Identification": "识别", "Identifier": "标识符", "Identifier and Type": "标识符和类型", "Identify Drive": "识别驱动器", "Identify Light": "识别灯", "Identify the purpose for this public key. Typically used for end entity certificates. Multiple usages can be selected. Do not mark this extension critical when the <i>Usage</i> is <i>ANY_EXTENDED_KEY_USAGE</i>.<br><br> Using both <b>Extended Key Usage</b> and <b>Key Usage</b> extensions requires that the purpose of the certificate is consistent with both extensions. See <a href=\"https://www.ietf.org/rfc/rfc3280.txt\" target=\"_blank\">RFC 3280, section *******3</a> for more details.": "确定此公钥的目的。通常用于最终实体证书。可以选择多种用法。当<i>用法</i>为<i> ANY_EXTENDED_KEY_USAGE </i>时，请勿将此扩展标记为关键。<br> <br>同时使用<b>扩展密钥用法</b>和<b>密钥用法 </b>的扩展时要求证书的用途与两个扩展都一致。有关更多详细信息，请参见<a href=\"https://www.ietf.org/rfc/rfc3280.txt\" target=\"_blank\">RFC 3280第*******3节</a>。", "Identify this certificate as a Certificate Authority (CA).": "将此证书标识为证书颁发机构（CA）。", "Identify this extension as critical for the certificate. Critical extensions must be recognized by the certificate-using system or this certificate will be rejected. Extensions identified as <i>not</i> critical can be ignored by the certificate-using system and the certificate still approved.": "将此扩展标识为证书的关键扩展。关键扩展必须由使用证书的系统识别，否则此证书将被拒绝。使用证书的系统可以忽略标识为<i>非</i>关键的扩展，并且仍批准该证书的使用。", "Idle": "空闲", "Idmap": "Idmap", "Idmap Backend": "Idmap后端", "If selected, sets several environment variables.": "如果选中，则设置多个环境变量。", "If set when troubleshooting a connection, logs more verbosely.": "如果在对连接进行故障排除时设置，会更详细的记录日志。", "If set, the DNS name of the client must resolve to its IP address and the cert must contain the same DNS name.": "如果设置，则客户端的DNS名称必须解析为其IP地址，并且证书必须包含相同的DNS名称。", "If set, the client certificate must contain the IP address that matches the IP address of the client.": "如果设置，则客户端证书必须包含与客户端的IP地址匹配的IP地址。", "If set, the password of the user can be sent unencrypted.": "如果设置，则可以未加密的方式发送用户的密码。", "If set, the user home directory is checked for a <b>.tlslogin</b> file which contains one or more PEM-encoded certificates. If not found, the user is prompted for password authentication.": "如果设置，将检查用户主目录中的<b> .tlslogin </ b>文件，该文件包含一个或多个PEM编码的证书。如果未找到，则提示用户进行密码身份验证。", "If the <i>Hex key</i> type is chosen, an encryption key will be auto-generated.": "如果选择<i>Hex key</i>类型，将自动生成加密密钥。", "If the IPMI out-of-band management interface is on a different VLAN from the management network, enter the IPMI VLAN.": "如果IPMI带外管理接口与管理网络位于不同的VLAN，请输入IPMI VLAN。", "If the destination system has  snapshots but they do not have any data in common with the source  snapshots, destroy all destination snapshots and do a full replication.  <b>Warning:</b> enabling this option can cause data loss or excessive  data transfer if the replication is misconfigured.": "如果目标系统具有快照，但是它们与源快照没有任何共同的数据，请销毁所有目标快照并进行完全复制。<b>警告：</b>如果复制配置错误，启用此选项可能会导致数据丢失或过多的数据传输。", "If {vmName} is still running, the Guest OS did not respond as expected. It is possible to use <i>Power Off</i> or the <i>Force Stop After Timeout</i> option to stop the VM.": "如果 {vmName} 仍在运行，客机操作系统未按预期响应。可以使用<i>关闭电源</i>或<i>超时后强制停止</i>选项来停止虚拟机。", "Ignore Builtin": "忽略内置", "Image ID": "镜像ID", "Image Name": "镜像名", "Image Size": "图片尺寸", "Image Tag": "镜像标签", "Images ( to be updated )": "镜像（待更新）", "Images not to be deleted": "不删除的镜像", "Immediately connect to TrueCommand.": "立即连接到TrueCommand。", "Import": "导入", "Import Certificate": "导入证书", "Import Certificate Signing Request": "导入证书签名请求", "Import Pool": "导入池", "Import the private key from an existing SSH keypair or  select <i>Generate New</i> to create a new SSH key for this credential.": "从现有的SSH密钥对导入私钥，或选择<i> 新建 </i>为该凭证创建新的SSH密钥。", "Important": "重要的", "Importing Pool": "导入存储池", "Importing Pool...": "导入存储池中...", "Importing pools.": "正在导入池。", "In": "接收", "In KiBs or greater. A default of <i>0 KiB</i> means unlimited. ": "在KiBs或更高。默认值为<i> 0 KiB </ i>表示无限制。", "In some cases it's possible that the provided key/passphrase is valid but the path where the dataset is supposed to be mounted after being unlocked already exists and is not empty. In this case, unlock operation would fail. This can be overridden by Force flag. When it is set, system will rename the existing directory/file path where the dataset should be mounted resulting in successful unlock of the dataset.": "在某些情况下，可能提供的密钥/密码短语是有效的，但在解锁后应该挂载数据集的路径已经存在，而且不是空的。此时解锁操作将会失败。这可以被 Force flag 覆写。设置后，系统将重命名应该挂载数据集的现有目录/文件路径，从而成功解锁数据集。", "Include Dataset Properties": "包括数据集属性", "Include dataset properties with the replicated  snapshots.": "在复制的快照中包括数据集属性。", "Include snapshots with the name": "包括带有名称的快照", "Incorrect Password": "密码错误", "Incorrect crontab value.": "错误的 crontab 值。", "Indefinitely": "无限期", "InfluxDB time series name for collected points.": "InfluxDB收集点的时间序列名称。", "Info": "信息", "Informational": "信息", "Inherit": "继承", "Inherit (encrypted)": "继承（加密）", "Inherit (non-encrypted)": "继承（未加密）", "Inherit Only": "仅继承", "Inherit domain from DHCP": "从 DHCP 继承域", "Inherit encryption properties from parent": "从父级继承加密属性", "Inherit from DHCP": "从 DHCP 继承", "Inherited": "继承", "Inherited Quotas": "继承配额", "Init/Shutdown Script": "初始化/关闭脚本", "Init/Shutdown Scripts": "开机/关机脚本", "Initial Deployment/Setup": "初始部署/设置", "Initial secret authentication key.  When an authentication key is not set, no <i>Authentication Protocol</i> is  used. If an authentication key is set and an  <i>Authentication Protocol</i> is not specified,  <a href=\"https://en.wikipedia.org/wiki/MD5\"  target=\"_blank\">MD5</a> is used as the default.<br>  Must be at least 8 and at most 32 octets long.": "初始秘密验证密钥。未设置验证密钥时，不使用<i>身份验证协议</i>。如果设置了验证密钥，但未指定<i>验证协议</i>，则默认使用<a href=\"https://en.wikipedia.org/wiki/MD5\"  target=\"_blank\"> MD5 </a>。<br>至少8个字节，最多32个字节。", "Initial secret encryption key.  If an encryption key is not set, no <i>Encryption Protocol</i> is used.  If an encryption key is set and an <i>Encryption Protocol</i> is  not specified,  <a href=\"https://en.wikipedia.org/wiki/Data_Encryption_Standard\"  target=\"_blank\">DES</a> is used as the default.<br>  Must be at least 8 and at most 32 octets long.": "初始秘密加密密钥。如果未设置加密密钥，则不使用<i>加密协议</i>。如果设置了加密密钥，但未指定<i>加密协议</i>，则默认使用<a href=\"https://en.wikipedia.org/wiki/Data_Encryption_Standard\"  target=\"_blank\"> DES </a>。<br>至少为8个字节，最多为32个字节。", "Initiate Failover": "启动故障转移", "Initiator": "启动器", "Initiator Group ID": "启动器组ID", "Initiators": "启动器", "Initiators Groups": "发起人组", "Initiators allowed access to this system. Enter an <a href=\"https://tools.ietf.org/html/rfc3720#section-3.2.6\" target=\"_blank\">iSCSI Qualified Name (IQN)</a> and click <i>+</i> to add it to the list. Example: <i>iqn.1994-09.org.freebsd:freenas.local</i>": "启动程序允许访问此系统。输入<a href=\"https://tools.ietf.org/html/rfc3720\\#section-3.2.6\" target=\"_blank\"> iSCSI合格名称（IQN）</a>，然后单击<i>+</i>将其添加到列表中。例如：<i> iqn.1994-09.org.freebsd：freenas.local </i>", "Initiators currently connected to the system. Shown in IQN format with an IP address. Set initiators and click an <b>-></b> (arrow) to add the initiators to either the <i>Allowed Initiators</i> or <i>Authorized Networks</i> lists. Clicking <i>Refresh</i> updates the <i>Connected Initiators</i> list.": "当前连接到系统的启动器。以IQN格式显示，并带有IP地址。设置启动器，然后单击<b>-> </b>（箭头）以将启动器添加到<i>允许启动器</i>或<i>授权网络</ i>列表中。单击<i>刷新</i>更新<i>已连接的启动器</i>列表。", "Input the pre-defined container to use.": "输入要使用的预定义容器。", "Inquiry": "询问", "Install": "安装", "Install Application": "安装应用", "Install Manual Update File": "安装手动更新文件", "Installation Media": "安装介质", "Installed Applications": "已安装应用", "Installing": "安装中", "Interface": "网口", "Interface Description": "网口描述", "Interface Settings": "网口设置", "Interface changes reverted.": "界面更改已恢复。", "Interfaces": "网口", "Interfaces marked <i>critical</i> are considered necessary for normal operation. When the last critical interface in a failover group is preempted by the other storage controller through the VRRP or CARP protocols, a failover is triggered.": "标记为<i>关键</ i>的接口被认为是正常操作所必需的。当故障转移组中的最后一个关键接口被另一个存储控制器通过VRRP或CARP协议抢占时，将触发故障转移。", "Internal": "内部", "Internal Certificate": "内部证书", "Internal identifier for the authenticator.": "身份验证器的内部标识符。", "Internal identifier of the certificate. Only alphanumeric characters, dash (<b>-</b>), and underline (<b>_</b>) are allowed.": "证书的内部标识符。 仅允许使用字母数字字符，破折号（<b>-</b>）和下划线（<b>_</b>）。", "Internal identifier of the certificate. Only alphanumeric, \"_\" and \"-\" are allowed.": "证书的内部标识符。仅允许使用字母数字，\"_\" 和 \"-\"。", "Internetwork control": "互联网控制", "Interval": "间隔", "Invalid Format.": "格式无效。", "Invalid IP address": "IP地址无效", "Invalid Pod name": "Pod名无效", "Invalid file": "不可用的文件", "Invalid format or character": "无效的格式或字符", "Invalid format or character.": "格式或字符无效。", "Invalid image": "不可用的镜像", "Invalid network address list. Check for typos or missing CIDR netmasks and      separate addresses by pressing <code>Enter</code>.": "无效的网络地址列表。通过按<code> Enter </ code>检查拼写错误或缺少CIDR网络掩码以及单独的地址。", "Invalid option selected": "选择了无效的选项", "Invalid regex filter": "无效的正则表达式过滤器", "Invalid value. Missing numerical value or invalid numerical value/unit.": "无效值。缺少数值或无效的数值/单位。", "Invalid value. Must be greater than or equal to ": "无效值。必须大于或等于", "Invalid value. Must be less than or equal to ": "无效值。必须小于或等于", "Invalid value. Valid values are numbers followed by optional unit letters,  like <code>256k</code> or <code>1 G</code> or <code>2 MiB</code>.": "无效值。有效值是数字，后跟可选的单位字母，例如<code>256k</code>或<code>1 G</code>或<code>2 MiB</code>。", "Is planned to be automatically destroyed at {datetime}": "计划在 {datetime} 自动销毁", "Isolated GPU Device(s)": "隔离GPU设备", "Isolated GPU PCI Id's": "隔离的GPU PCI Id", "Issuer": "颁发者", "It is not recommended to create a pool with vdevs containing different numbers of disks. Continue?": "不建议使用包含不同数量磁盘的vdev创建池。继续吗？", "It is not recommended to extend a pool with one or more vdevs containing different numbers of disks. Continue?": "不建议使用一个或多个包含不同数量磁盘的vdev扩展池。继续吗？", "It seems you haven't configured pools yet.": "您似乎还没有配置池。", "It seems you haven't setup any NFS Shares yet. Please click the button below to add an NFS Share.": "看起来您还没有设置任何 NFS 共享。 请点击下面的按钮来添加一个 NFS 共享。", "It seems you haven't setup any SMB Shares yet. Please click the button below to add an SMB Share.": "您似乎还没有设置任何SMB共享。请单击下面的按钮添加SMB共享。", "It seems you haven't setup any WebDAV Shares yet. Please click the button below to add an WebDAV Share.": "看起来您还没有设置任何 WebDAV 共享。 请点击下面的按钮来添加一个 WebDAV 共享。", "It seems you haven't setup any {item} yet.": "您似乎还没有设置任何 {item}。", "Item": "项目", "Item Name": "项目名称", "Items Delete Failed": "项目删除失败", "Items deleted": "项目已删除", "Jan": "一月", "Job {job} Completed Successfully": "作业 {job} 已成功完成", "Jobs": "任务", "Jul": "七月", "Jun": "六月", "KDC": "KDC", "KMIP Key Status": "KMIP密钥状态", "KMIP Server": "KMIP服务器", "KVM implements Hyper-V Enlightenments   for Windows guests. These features make Windows think they're   running on top of a Hyper-V compatible hypervisor and use Hyper-V specific features.   In some cases enabling these Enlightenments might improve usability and performance on the guest.": "KVM 为 Windows 来宾实施 Hyper-V 启蒙。 这些功能使 Windows 认为它们运行在与 Hyper-V 兼容的虚拟机管理程序之上，并使用 Hyper-V 特定功能。 在某些情况下，启用这些启蒙可能会提高来宾的可用性和性能。", "Keep": "保持", "Keep for": "保持为", "Keep snapshot for": "保留快照", "Keep the name short. Using a name longer than 63 characters can prevent accessing the block device.": "保持zvol名称简短。使用一个长于63个字符的zvol名称会使zvol不能作为一个设备进行访问。", "Keep the zvol name short. Using a zvol name longer than 63 characters can prevent accessing the zvol as a device.": "保持zvol名称简短。使用一个长于63个字符的zvol名称会使zvol不能作为一个设备进行访问。", "Keep this Boot Environment?": "保留此引导环境吗？", "Kerberos Keytab": "Kerberos密钥表", "Kerberos Keytabs": "Kerberos 密钥表", "Kerberos Principal": "Kerberos主体", "Kerberos Realm": "Kerberos领域", "Kerberos Realms": "Kerberos 领域", "Kerberos Settings": "Kerberos设置", "Kernel": "内核", "Key": "密钥", "Key Agreement": "密钥协议", "Key Cert Sign": "密钥证书签名", "Key Encipherment": "密钥加密", "Key ID": "密钥ID", "Key Length": "密钥长度", "Key Type": "密钥类型", "Key Usage": "密钥用法", "Key Usage Config": "密钥用法配置", "Key for {id}": "{id} 的密钥", "Keys Synced": "同步秘钥", "Keys for {id} datasets": "{id} 数据集的秘钥", "Keys pending to be synced between KMIP server and TN database were cleared.": "KMIP 服务器和 TN 数据库之间待同步的密钥已清除。", "KiB": "KiB", "Kubernetes Settings": "Kubernetes 设置", "LACPDU Rate": "LACPDU 速率", "LDAP": "LDAP", "LDAP - Primary Domain": "LDAP - 主域", "LDAP Domain": "LDAP域", "LDAP Realm": "LDAP 领域", "LDAP Server": "LDAP服务器", "LDAP Timeout": "LDAP 超时", "LDAP User DN": "LDAP 用户 DN", "LDAP User DN Password": "LDAP 用户 DN 密码", "LDAP is disabled.": "LDAP 已禁用。", "LDAP server hostnames or IP addresses. Separate entries with an empty space. Multiple hostnames or IP addresses can be entered to create an LDAP failover priority list. If a host does not respond, the next host in the list is tried until a new connection is established.": "LDAP服务器主机名或IP地址。用空格分隔条目。可以输入多个主机名或IP地址来创建LDAP故障转移优先级列表。如果主机没有响应，则尝试列表中的下一个主机，直到建立新连接。", "LDAP server to use for SID/uid/gid map entries. When undefined, idmap_ldap uses *ldap://localhost/*. Example: <i>ldap://ldap.netscape.com/o=Airius.com</i>.": "用于SID/uid/gid映射条目的LDAP服务器。未定义时，idmap_ldap使用*ldap://localhost/*。例如：<i>ldap//ldap.netscape.com/o=Airius.com </i>。", "LDAP timeout in seconds. Increase this value if a Kerberos ticket timeout occurs.": "DNS超时（秒）。如果DNS查询超时，请增加此值。", "LOCAL": "本地", "LONG": "长", "LUN ID": "LUN ID", "LUN RPM": "LUN RPM", "Language": "语言", "Last Scan": "上次扫描", "Last Scan Duration": "上次扫描持续时间", "Last Scan Errors": "上次扫描错误", "Last Scrub Run": "最后一次擦除", "Last Snapshot": "最后一个快照", "Last {result} Test": "上次 {result} 测试", "Launch Docker Image": "启动Docker镜像", "Leave Domain": "离开网域", "Leave at the default of 512 unless the initiator requires a different block size.": "除非启动器要求不同的块大小，否则请保留默认值512。", "Leave blank to allow all or enter a list of initiator hostnames.    Separate entries by pressing <code>Enter</code>.": "保留空白以允许全部输入或输入启动程序主机名列表。通过按<code> Enter </ code>分隔条目。", "Leave empty for default (<a href=\"https://api.opsgenie.com\" target=\"_blank\">OpsGenie API</a>)": "默认为空（ <a href=\"https://api.opsgenie.com\" target=\"_blank\">OpsGenie API</a> ）", "Leave empty or select number of existing portal to use.": "保留为空，或选择要使用的现有门户的编号。", "Leaving the domain requires sufficient privileges. Enter your credentials below.": "离开域需要足够的权限。在下面输入您的凭据。", "Legacy": "传统", "Legacy AFP Compatibility": "旧版AFP兼容性", "Legacy NetBIOS name server. Advertises the SMB  service <i>NetBIOS Name</i>. Can be required for legacy SMB1 clients  to discover the server. When advertised, the server appears in  <i>Network Neighborhood</i>).": "传统NetBIOS名称服务器。通告SMB服务的<i>NetBIOS 名称</i>。传统SMB1客户端可以发现服务器。播发时，服务器将显示在<i>网上邻居</i>）。", "Legend": "说明", "Level": "级别", "Level 1 - Minimum power usage with Standby (spindown)": "级别1-待机时的最低功耗（降低转速）", "Level 127 - Maximum power usage with Standby": "等级127-待机时的最大功耗", "Level 128 - Minimum power usage without Standby (no spindown)": "级别128-无待机状态下的最低功耗（无降速）", "Level 192 - Intermediate power usage without Standby": "等级192-无待机状态下的中等功耗", "Level 254 - Maximum performance, maximum power usage": "等级254-最高性能，最高功耗", "Level 64 - Intermediate power usage with Standby": "等级64-待机时的中等功耗", "Libdefaults Auxiliary Parameters": "Libdefaults辅助参数", "License": "许可", "Lifetime": "生命周期", "Limit": "限制", "Limit replication speed to this number of bytes per second.": "将复制速度限制为每秒此bytes数。", "Link Aggregation": "链路聚合", "Link Aggregation Interfaces": "链路聚合接口", "Link Aggregation Protocol": "链路聚合协议", "Link aggregation interface": "链路聚合接口", "Linked Service": "已连接服务", "List any existing dataset properties to remove from the replicated files.": "列出要从复制文件中删除的任何现有数据集属性。", "List of chat IDs": "会话ID列表", "List of files and directories to exclude from sync.<br>  Separate entries by pressing <code>Enter</code>. See  <a href=\"https://rclone.org/filtering/\" target=\"_blank\">rclone filtering</a>  for more details about the <code>--exclude</code> option.": "要从同步中排除的文件和目录列表。<br>通过按<code> Enter </ code>分隔条目。有关<code>-exclude </ code>选项的更多详细信息，请参见<a href=\"https://rclone.org/filtering/\" target=\"_blank\">rclone过滤</a>。", "Listen": "监听", "Load Previous Replication Task": "加载先前的复制任务", "Loading": "加载中", "Loading devices. Please wait.": "装载设备。 请稍等。", "Loading...": "加载中...", "Local": "本地", "Local Account": "本地帐号", "Local Groups": "本地组", "Local Master": "本地管理员", "Local User Download Bandwidth": "本地用户下载带宽：", "Local User Upload Bandwidth: ": "本地用户上传带宽：", "Local Users": "本地用户", "Local heartbeat interface does not exist.": "本地活动接口不存在。", "Local heartbeat interface is down.": "本地活动接口已关闭。", "Locality": "地区", "Localization": "本地化", "Localization Settings": "本地化设置", "Location": "位置", "Lock": "锁定", "Lock Dataset": "锁定数据集", "Lock Dataset {datasetName}?": "锁定数据集 {datasetName}？", "Lock User": "锁定用户", "Locked": "锁定", "Locked by ancestor": "被父层锁定", "Locking Dataset": "正在锁定数据集", "Locking dataset {datasetName}": "正在锁定数据集{datasetName}", "Log": "日志", "Log Excerpt": "日志摘录", "Log In To Gmail": "登录到Gmail", "Log In To Gmail Again": "再次登录到Gmail", "Log In To Provider": "登录到提供商", "Log Level": "日志级别", "Log Out": "登出", "Log Path": "日志路径", "Log VDEVs": "记录 VDEV", "Log VDev": "日志VDev", "Log in as Root with Password": "使用密码以root身份登录", "Log in to Gmail to set up Oauth credentials.": "登录到Gmail以设置Oauth密钥。", "Logged In To Jira": "已经登陆 Jira", "Logging Level": "日志级别", "Logical Block Size": "逻辑块大小", "Login Attempts": "登录尝试", "Login to Jira": "登录 Jira", "Logs": "日志", "Long": "长期", "Looking for help?": "需要帮助吗？", "Losing the ability to unlock the pool can result in losing all data on the disks with no chance of recovery. Always back up the encryption key file or passphrase for an encrypted pool! The key file for an encrypted pool is secured in the system database and can be exported at any time from the pool options": "失去解锁池的能力可能会导致磁盘上的所有数据丢失而无法恢复。 始终备份加密池的加密密钥文件或密码！ 加密池的密钥文件在系统数据库中受到保护，可以随时从池选项中导出", "Loss of Functionality": "功能丧失", "Low Capacity": "低容量", "Lowercase alphanumeric characters plus dot (.), dash (-), and colon (:) are allowed. See the <i>Constructing iSCSI names using the iqn.format</i> section of <a href=\"https://tools.ietf.org/html/rfc3721.html\" target=\"_blank\">RFC3721</a>.": "允许使用小写字母数字字符以及点号(.)，破折号(-)和冒号(:)。请参阅<i>使用iqn.format构造iSCSI名称</i><a href=\"https://tools.ietf.org/html/rfc3721.html\" target=\"_blank\">RFC3721</a>的部分。", "Lowest Temperature": "最低温度", "Lowest Usage:": "最低使用率：", "Lowest port number of the  active side listen address that is open to connections.": "对连接开放的活动侧侦听地址的最低端口号。", "MAC Address": "MAC地址", "MOTD Banner": "MOTD横幅", "MOVE": "迁移", "MTU": "MTU", "Mac Address": "MAC地址", "Mail Server Port": "邮件服务器端口", "Make Destination Dataset Read-only?": "将目标数据集设为只读？", "Make the currently active TrueNAS controller the default when both TrueNAS controllers are online and HA is enabled. To change the default TrueNAS controller, unset this option on the default TrueNAS controller and allow the system to fail over. This briefly interrupts system services.": "当两个 TrueNAS 控制器都在线并启用 HA 时，将当前活动的 TrueNAS 控制器设为默认值。要更改默认 TrueNAS 控制器，请在默认 TrueNAS 控制器上取消设置此选项并允许系统进行故障转移。这会短暂中断系统服务。", "Manage": "管理", "Manage Advanced Settings": "管理高级设置", "Manage Apps Settings": "管理应用程序设置", "Manage Catalogs": "管理目录", "Manage Certificates": "管理证书", "Manage Cloud Sync Tasks": "管理云同步任务", "Manage Configuration": "管理配置", "Manage Credentials": "管理凭据", "Manage Datasets": "管理数据集", "Manage Devices": "管理设备", "Manage Disks": "管理磁盘", "Manage Docker Images": "管理Docker镜像", "Manage Global SED Password": "管理全局 SED 密码", "Manage Group Quotas": "管理组配额", "Manage NFS Shares": "管理 NFS 共享", "Manage Replication Tasks": "管理复制任务", "Manage Rsync Tasks": "管理 Rsync 任务", "Manage S.M.A.R.T. Tasks": "管理 S.M.A.R.T. 任务", "Manage SED Password": "管理 SED 密码", "Manage SED Passwords": "管理SED密码", "Manage SMB Shares": "管理 SMB 共享", "Manage Services and Continue": "管理服务并继续", "Manage Snapshot Tasks": "管理快照任务", "Manage Snapshots": "管理快照", "Manage User Quotas": "管理用户配额", "Manage VM Settings": "管理虚拟机设置", "Manage ZFS Keys": "管理ZFS密钥", "Manage iSCSI Shares": "管理 iSCSI 共享", "Manage members of {name} group": "管理 {name} 组的成员", "Management": "管理", "Manual": "手动", "Manual S.M.A.R.T. Test": "手动S.M.A.R.T. 测试", "Manual Test": "手动测试", "Manual Update": "手动更新", "Mapall Group": "Mapall组", "Mapall User": "Mapall用户", "Maproot Group": "Maproot组", "Maproot User": "Maproot用户", "Mar": "三月", "Mask": "掩码", "Masquerade Address": "地址伪装", "Matching naming schema": "匹配命名模式", "Matching regular expression": "匹配正则表达式", "Matching the fixed size of data, as in a database, may result in better performance.": "在数据库中匹配固定大小的数据可能会有更好的性能。", "Mathematical instruction sets that determine how plaintext is converted      into ciphertext. See      <a href=\"https://en.wikipedia.org/wiki/Advanced_Encryption_Standard\" target=\"_blank\">Advanced Encryption Standard (AES)</a>      for more details.": "确定纯文本如何转换为密文的数学指令集。有关更多详细信息，请参见<a href=\"https://en.wikipedia.org/wiki/Advanced_Encryption_Standard\" target=\"_blank\">高级加密标准（AES）</a>。", "Mattermost username.": "Mattermost用户名。", "Max Connections": "最大连接数", "Max Poll": "最大轮询", "Max dataset nesting in ZFS is limited to 50. We are already at that limit in the parent dataset path. It is not possible to create anymore nested datasets under this path.": "ZFS 中的最大数据集嵌套限制为 50。我们在父数据集路径中已经处于该限制。 无法再在此路径下创建嵌套数据集。", "Max length allowed for pool name is 50": "池名称允许的最大长度为 50", "Maximum Passive Port": "最大被动端口", "Maximum Transmission Unit, the largest protocol data  unit that can be communicated. The largest workable MTU size varies  with network interfaces and equipment. <i>1500</i> and <i>9000</i>  are standard Ethernet MTU sizes. Leaving blank restores the field to  the default value of <i>1500</i>.": "最大传输单位，可以通信的最大协议数据单位。最大可用MTU大小随网络接口和设备的不同而不同。 <i> 1500 </ i>和<i> 9000 </ i>是标准的以太网MTU大小。保留为空白将字段恢复为默认值<i> 1500 </ i>。", "Maximum Upload Parts": "最大上传部分", "Maximum connections": "最大连接数", "Maximum number of connections to this module. <i>0</i>  is unlimited.": "与此模块的最大连接数。<i> 0 </i>表示无限。", "Maximum number of replication tasks being executed simultaneously.": "并发执行复制的任务最大数量。", "Maximum number of seconds a client is allowed to spend connected, after\nauthentication, without issuing a command which results in creating an active or passive data connection\n(i.e. sending/receiving a file, or receiving a directory listing).": "在身份验证后，允许客户端连接的最大秒数，\n而不发出导致创建主动或被动数据连接的命令\n（即发送/接收文件，或接收目录列表）。", "Maximum number of seconds that proftpd will allow clients to stay connected without receiving\nany data on either the control or data connection.": "proftpd 将允许客户端保持连接而不在控制器\n或数据连接上接收任何数据的最大秒数。", "Maximum time a graph is stored in months (allowed values are 1-60).  Changing this value causes the <i>Confirm RRD Destroy</i>  dialog to appear. Changes do not take effect until the existing  reporting database is destroyed.": "图形存储的最长时间（以月为单位）（允许值为1-60）。更改此值将导致<i>确认RRD销毁</i>对话框出现。在销毁现有报告数据库之前，更改不会生效。", "Maximum value is {max}": "最大值为 {max}", "May": "五月", "Media Subtype": "介质子类型", "Media Type": "介质类型", "Member Disk": "成员磁盘", "Member disk": "成员磁盘", "Members": "成员", "Members of this group are local admins and automatically have privileges to take ownership of any file in an SMB share, reset permissions, and administer the SMB server through the Computer Management MMC snap-in.": "该组的成员是本地管理员，并自动拥有对SMB共享中的任何文件的所有权，重置权限以及通过计算机管理MMC管理单元管理SMB服务器的权限。", "Memory": "内存", "Memory Reports": "内存报告", "Memory Size": "内存大小", "Memory device": "存储设备", "Memory overcommitment allows multiple VMs to be launched when there is not enough free memory for configured RAM of all VMs. Use with caution.": "内存过量使用功能允许在没有足够的可用内存来配置所有虚拟机的内存时启动多个虚拟机。请谨慎使用。", "Message verbosity level in the replication  task log.": "复制任务日志中的消息详细级别。", "Metadata": "元数据", "Metadata (Special) Small Block Size": "元数据（特殊）小块大小", "Metadata VDEVs": "元数据 VDEV", "Metadata VDev": "元数据VDev", "Method": "方法", "Method of snapshot transfer:<ul>  <li><i>SSH</i> is supported by most systems. It requires a previously  created connection in <b>System > SSH Connections</b>.</li>  <li><i>SSH+NETCAT</i> uses SSH to establish a connection to the  destination system, then uses  <a href=\"https://github.com/truenas/py-libzfs\"  target=\"_blank\">py-libzfs</a> to send an unencrypted data stream for  higher transfer speeds. This only works when replicating to a  TrueNAS, or other system with <i>py-libzfs</i> installed.</li>  <li><i>LOCAL</i> efficiently replicates snapshots to another dataset on  the same system without using the network.</li>  <li><i>LEGACY</i> uses the legacy replication engine from FreeNAS 11.2  and earlier.</li></ul>": "快照传输方法：<ul> <li><i>SSH</i> 被大多数系统支持。 它需要之前在 <b>System > SSH Connections</b> 中创建的连接。</li> <li><i>SSH+NETCAT</i> 使用 SSH 建立到目标系统的连接，然后使用 < a href=\"https://github.com/truenas/py-libzfs\" target=\"_blank\">py-libzfs</a> 发送未加密的数据流以获得更高的传输速度。 这仅在复制到 TrueNAS 或安装了 <i>py-libzfs</i> 的其他系统时有效。</li> <li><i>LOCAL</i> 有效地将快照复制到同一系统上的另一个数据集 无需使用网络。</li> <li><i>LEGACY</i> 使用 FreeNAS 11.2 及更早版本的旧版复制引擎。</li></ul>", "Metrics": "指标", "MiB": "MiB", "MiB. Units  smaller than MiB are not allowed.": "MiB。不允许使用小于MiB的单位。", "Microsoft Onedrive  <a href=\"https://docs.microsoft.com/en-us/onedrive/developer/rest-api/getting-started/authentication\"  target=\"_blank\">Access Token</a>. Log in to the Microsoft account to  add an access token.": "Microsoft Onedrive <a href=\"https://docs.microsoft.com/en-us/onedrive/developer/rest-api/getting-started/authentication\"  target=\"_blank\">访问令牌</a>。登录到Microsoft帐户以添加访问令牌。", "Migrate applications to the new pool": "将应用程序迁移到新池", "Min Poll": "最小轮询", "Minimum": "最小", "Minimum Passive Port": "最小被动端口", "Minimum size is 1 MiB": "最小大小为1 MiB", "Minimum value is {min}": "最小值为 {min}", "Minimum value is {value}.": "最小值为 {value}。", "Minute": "分钟", "Minutes": "分钟", "Minutes of inactivity before the drive enters standby mode. This <a href=\"https://forums.freenas.org/index.php?threads/how-to-find-out-if-a-drive-is-spinning-down-properly.2068/\" target=\"_blank\">forum post</a> describes identifying spun down drives. Temperature monitoring is disabled for standby disks.": "在驱动器进入待机模式之前的无活动状态分钟数。该<a href=\"https://forums.freenas.org/index.php?threads/how-to-find-out-if-a-drive-is-spinning-down-properly.2068/\" target=\"_blank\">论坛帖子</a>介绍了如何识别驱动器盘片是否旋转。备用磁盘的温度监控已禁用。", "Minutes when this task will run.": "此任务将在哪分钟运行。", "Minutes/Hours": "分钟/小时", "Minutes/Hours/Days": "分钟/小时/天", "Mirror": "镜像", "Mixing disks of different sizes in a vdev is not recommended.": "不建议在 vdev 中混合使用不同大小的磁盘。", "Mode": "模式", "Model": "型号", "Model:": "型号：", "Modify": "调整", "Module name that matches the name requested by  the rsync client.": "与rsync客户端请求的名称匹配的模块名称。", "Mon": "周一", "Monday": "周一", "Monitor": "监控", "Monitor Password": "监控密码", "Monitor User": "监控用户", "Month": "月", "Month(s)": "月", "Months": "月数", "More Options": "更多选项", "More info...": "更多信息...", "Move existing keys from the current key server to a new key server.  To switch to a different key server, key synchronization must be <i>Enabled</i>, then  enable this setting, update the key server connection configuration, and click <b>SAVE</b>.": "将现有密钥从当前密钥服务器移至新的密钥服务器。要切换到其他密钥服务器，必须先<i>启用</i>密钥同步，然后启用此设置，更新密钥服务器连接配置，然后单击<b>保存</b>。", "Multi-domain support. Enter additional domains to  secure. Separate domains by pressing <code>Enter</code>  For example, if the primary domain is <i>example.com</i>,  entering <i>www.example.com</i> secures both addresses.": "多域支持。输入其他域以确保安全。通过按<code> Enter </code>分隔域。例如，如果主域是<i> example.com </ i>，则输入<i> www.example.com </i>可以保护两个地址。", "Multicast DNS. Uses the system <i>Hostname</i> to  advertise enabled and running services. For example, this controls if  the server appears under <i>Network</i> on MacOS clients.": "组播DNS。使用系统<i>主机名</i>通告已启用且正在运行的服务。例如，它控制服务器是否出现在MacOS客户端的<i>网络</i>下。", "Must be part of a pool to check errors": "必须是池的一部分才能检查错误", "Must match Windows workgroup name.  When this is unconfigured and Active Directory or LDAP are active,  TrueNAS will detect and set the correct workgroup from these services.": "必须匹配Windows工作组名称。当未配置且Active Directory或LDAP处于活动状态时，TrueNAS将从这些服务中检测并设置正确的工作组。", "Mutual secret password. Required when Peer User is set. Must be different than the <i>Secret</i>.": "相互的密码。设置对等用户时必需。必须不同于<i> 秘密</i>。", "N/A": "不可用", "NAA": "NAA", "NAS/browser time mismatch can lead to the reporting data display misbehavior.": "NAS/浏览器时间不匹配会导致报告数据显示异常。", "NFS": "NFS", "NFS Share": "NFS 共享", "NFSv3 ownership model for NFSv4": "NFSv4的NFSv3所有权模型", "NFSv4": "NFSv4", "NIC": "网卡", "NIC To Attach": "要附加的 NIC", "NONE": "无", "NOTICE": "注意", "NTLMv1 Auth": "NTLMv1认证", "NTP Server Settings": "NTP服务器设置", "NTP Servers": "网络授时服务器(NTP)", "Name": "名称", "Name And Method": "名称和方法", "Name and Naming Schema cannot be provided at the same time.": "不能同时提供名称和命名惯例。", "Name and Options": "名称和选项", "Name and Provider": "名称和提供者", "Name and Type": "名称和类型", "Name and port of the server that reports the  external IP address. For example, entering <i>checkip.dyndns.org:80</i>  uses  <a href=\"https://help.dyn.com/remote-access-api/checkip-tool/\" target=\"_blank\">Dyn IP detection</a>.  to discover the remote socket IP address.": "报告外部IP地址的服务器的名称和端口。 例如，输入<i>checkip.dyndns.org:80</i>使用<a href=\"https://help.dyn.com/remote-access-api/checkip-tool/\" target=\"_blank\">动态IP检测</a>。查找远程套接字IP地址。", "Name must start with an alphabetic character and end with an alphanumeric character. Hyphen is allowed in the middle.": "名称必须以字母字符开头并以字母数字字符结尾。 中间允许使用连字符。", "Name not found": "未找到名称", "Name of the <a href=\"https://docs.mattermost.com/help/getting-started/organizing-conversations.html#managing-channels\" target=\"_blank\">channel</a> to receive notifications. This overrides the default channel in the incoming webhook settings.": "要接收通知的<a href=\"https://docs.mattermost.com/help/getting-started/organizing-conversations.html\\#managing-channels\" target=\"_blank\">频道的</a>名称", "Name of the InfluxDB database.": "InfluxDB数据库的名称。", "Name of the WebDAV site, service, or software being used.": "正在使用的WebDAV站点，服务或软件的名称。", "Name of the extent. If the <i>Extent size</i> is not <i>0</i>, it cannot be an existing file within the pool or dataset.": "区块的名称。如果<i>区块大小</i>不是<i> 0 </i>，则它不能是池或数据集中的现有文件。", "Name of the new alert service.": "新的警报服务的名称。", "Name of the new cloned boot environment. Alphanumeric characters, dashes (-), underscores (_),     and periods (.) are allowed.": "新克隆的引导环境的名称。允许使用字母数字字符，破折号(-)，下划线(_)和句点(.)。", "Name of the new dataset created from the  cloned snapshot.": "从克隆快照创建的新数据集的名称。", "Name of the pool is required": "存储池的名称是必需的", "Name of the pool must be correct": "存储池的名称必须正确", "Name of the zvol is required": "zvol 的名称是必需的", "Name of the zvol must be correct": "zvol 的名称必须正确", "Name of this SSH connection. SSH connection names  must be unique.": "此SSH连接的名称。SSH连接名称必须唯一。", "Name of this replication configuration.": "此复制配置的名称。", "Name or Naming Schema must be provided.": "必须提供名称或命名惯例。", "Nameserver (DHCP)": "域名服务器 (DHCP)", "Nameserver 1": "域名服务器 1", "Nameserver 2": "域名服务器 2", "Nameserver 3": "域名服务器 3", "Nameserver {n}": "域名服务器 {n}", "Nameservers": "域名服务器", "Naming Schema": "命名惯例", "Navigation": "导航", "NetBIOS": "NetBIOS", "NetBIOS Alias": "NetBIOS 别名", "NetBIOS Name": "NetBIOS 名称", "NetBIOS Name (TrueNAS Controller 2)": "NetBIOS 名称（TrueNAS 控制器 2）", "Netbios Name": "NetBIOS 名称", "Netbios Name of this NAS. This name must differ from the <i>Workgroup</i> name and be no greater than 15 characters.": "这个NAS的Netbios名称。该名称必须与<i> Workgroup </i>名称不同，并且不得超过15个字符。", "Netcat Active Side": "Netcat活动侧", "Netcat Active Side Connect Address": "Netcat活动侧连接地址", "Netcat Active Side Listen Address": "Netcat活动侧侦听地址", "Netcat Active Side Max Port": "Netcat活动侧最大端口", "Netcat Active Side Min Port": "Netcat活动侧最小端口", "Netmask": "子网掩码", "Netwait IP List": "Netwait IP列表", "Netwait:": "等待网络：", "Network": "网络", "Network Interface": "网络接口", "Network Reports": "网络报告", "Network Timeout Before Initiating Failover": "启动故障转移之前的网络超时", "Network Widgets": "网络小部件", "Network addresses allowed to use this initiator. Leave blank to allow all      networks or list network addresses with a CIDR mask. Separate entries by pressing      <code>Enter</code>.": "允许使用此启动器的网络地址。保留空白以允许所有网络或使用CIDR掩码列出网络地址。通过按<code> Enter </ code>分隔条目。", "Network addresses allowed use this initiator. Each address can include an optional <a href=\"https://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing\" target=\"_blank\">CIDR</a> netmask. Click <i>+</i> to add the network address to the list. Example: <i>***********/24</i>.": "允许的网络地址使用此启动器。每个地址都可以包含一个可选的<a href=\"https://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing\" target=\"_blank\"> CIDR </a>网络掩码。 单击<i> + </i>将网络地址添加到列表中。例如：<i> ***********/24 </i>。", "Network changes applied successfully.": "网络更改已成功应用。", "Network community string.  The community string acts like a user ID or password. A user with  the correct community string has access to network information. The  default is <i>public</i>. For more information, see  <a href=\"https://community.helpsystems.com/knowledge-base/intermapper/snmp/snmp-community-strings/\"  target=\"_blank\">What is an SNMP Community String?</a>.": "网络社区字符串。社区字符串的作用类似于用户ID或密码。具有正确社区字符串的用户可以访问网络信息。默认值为<i> public </i>。有关更多信息，请参见<a href=\"https://community.helpsystems.com/knowledge-base/intermapper/snmp/snmp-community-strings/\"  target=\"_blank\">什么是SNMP社区字符串？</a>。", "Network control (highest)": "网络控制（最高）", "Network interface changes have been made permanent.": "网络接口更改已永久生效。", "Network interface changes have been temporarily applied for testing. Keep changes permanently? Changes are automatically reverted after the testing delay if they are not permanently applied.": "网络接口更改已暂时应用于测试。 永久保留更改？ 如果更改未永久应用，则会在测试延迟后自动恢复。", "Network interface settings have been temporarily changed for testing. The settings will revert to the previous configuration after": "网络接口设置已临时更改以进行测试。 设置将恢复到之前的配置", "Network interfaces to include in the bridge.": "包含在网桥中的网络接口。", "Networking": "网络", "Networks": "网络", "Never": "从不", "Never Delete": "永不删除", "New Disk": "新磁盘", "New Password": "新密码", "New Pool": "新池", "New SSH Connection": "新的 SSH 连接", "New password and confirmation should match.": "新密码和确认密码必须匹配。", "New users are not given <b>su</b> permissions if <i>wheel</i> is their primary group.": "如果<i>wheel</i>是其主要组，则不向新用户授予<b>su</b>权限。", "Newer Clone": "较新的克隆", "Newer Intermediate, Child, and Clone": "较新的中级，子级和克隆", "Next": "下一步", "Next Run": "下次运行", "No": "No", "No Applications Installed": "没有安装应用", "No Applications are Available": "没有可用的应用", "No Changelog": "没有更新日志", "No Communication Warning Time": "没有通讯警告时间", "No Data": "没有数据", "No Datasets": "没有数据集", "No Encryption (less secure, but faster)": "没有加密（安全性较低，但速度更快）", "No Inherit": "无继承", "No Isolated GPU Device(s) configured": "未配置隔离 GPU 设备", "No Item": "没有项目", "No Logs": "没有日志", "No NFS Shares have been configured yet": "当前没有配置 NFS 共享", "No Pods Found": "未找到Pod", "No Pools": "没有池", "No Pools Found": "未找到池", "No Propagate Inherit": "无传播继承", "No Recent Events": "没有最近的活动", "No SMB Shares have been configured yet": "当前没有配置 SMB 共享", "No Safety Check (CAUTION)": "没有安全检查（注意）", "No Search Results.": "没有搜索结果。", "No Traffic": "无通信", "No Version": "无版本", "No WebDAV Shares have been configured yet": "当前没有配置 WebDAV 共享", "No active interfaces are found": "未找到活动接口", "No arguments are passed": "不传递任何参数", "No errors": "没有错误", "No interfaces configured with Virtual IP.": "未使用虚拟IP配置接口。", "No items have been added yet.": "尚未添加任何项目。", "No logs are available": "没有可用的日志", "No logs are available for this task.": "没有日志可用于此任务。", "No longer keep this Boot Environment?": "不再保留此引导环境？", "No network interfaces are marked critical for failover.": "没有网络接口被标记为故障转移的关键接口。", "No options": "没有选项", "No pools are configured.": "没有配置池。", "No pools on this enclosure.": "此机柜中没有池。", "No ports are being used.": "没有使用任何端口。", "No snapshots are available.": "没有可用的快照。", "No snapshots sent yet": "尚未发送快照", "No temperature data was reported by the system. There can be a number of reasons why this might occur. For troubleshooting tips please go here": "系统没有报告温度数据。发生这种情况的原因可能有多种。有关故障排除提示，请转到此处", "No update found.": "没有找到更新。", "No updates available.": "没有可用更新。", "No vdev info for disk.": "没有磁盘的 vdev 信息。", "No {item}": "没有 {item}", "No {title} configured": "未配置 {title}", "Nobind": "无绑定", "Node IP": "节点IP", "Node set allows setting NUMA nodes for multi NUMA processors when CPU set was defined. Better memory locality can be achieved by setting node set based on assigned CPU set. E.g. if cpus 0,1 belong to NUMA node 0 then setting nodeset to 0 will improve memory locality": "节点集允许在定义 CPU 集时为多个 NUMA 处理器设置 NUMA 节点。 通过根据分配的 CPU 集设置节点集可以实现更好的内存局部性。 例如。 如果 cpus 0,1 属于 NUMA 节点 0，则将 nodeset 设置为 0 将提高内存局部性", "Nodes Virtual IP states do not agree.": "节点的虚拟 IP 状态不一致。", "None": "无", "None requested": "没有需求", "Normal": "正常", "Normal vdev type, used for primary storage operations. ZFS pools always have at least one DATA vdev.": "正常的vdev类型，用于主存储操作。ZFS池始终至少具有一个数据vdev。", "Not Available": "无法使用", "Not Set": "没有设置", "Notes about this disk.": "关于此磁盘的注释。", "Notes about this extent.": "有关此区块的说明。", "Notice": "通知", "Notransfer Timeout": "无传输超时", "Nov": "十一月", "Now": "现在", "Now flashing...": "正在刷入...", "Now/Reboot": "马上/重启", "Number of CPUs": "处理器数量", "Number of Cores": "核心数", "Number of Graph Points": "图形点数量", "Number of Threads": "线程数", "Number of bytes": "字节数", "Number of days to renew certificate before expiring.": "证书过期前要续期的天数。", "Number of objects that can be owned by each of the selected groups.  Entering <code>0</code> (zero) allows unlimited objects.": "每个选定组可以拥有的对象数。输入<code> 0 </code>（零）允许无限制的对象数。", "Number of objects that can be owned by each of the selected users.  Entering <code>0</code> (zero) allows unlimited objects.": "每个选定用户可以拥有的对象数。输入<code> 0 </code>（零）允许无限制的对象数。", "Number of password-based key derivation function 2 (PBKDF2) iterations to use for reducing vulnerability      to brute-force attacks. Entering a number larger than <i>100000</i> is required. See      <a href=\"https://en.wikipedia.org/wiki/PBKDF2\" target=\"_blank\">PBKDF2</a> for more details.": "基于密码的密钥派生功能2（PBKDF2）迭代的次数，可用于减少对暴力攻击的脆弱性。输入的数字必须大于<i> 100000 </i>。有关更多详细信息，请参见<a href=\"https://en.wikipedia.org/wiki/PBKDF2\" target=\"_blank\">PBKDF2</a>。", "Number of points for each hourly, daily,  weekly, monthly, or yearly graph (allowed values are 1-4096). Changing this value causes the  <i>Confirm RRD Destroy</i> dialog to appear. Changes do not take  effect until the existing reporting database is destroyed.": "每个每小时，每天，每周，每月或每年的图的点数（允许值为1-4096）。更改此值将导致<i>确认RRD销毁</i>对话框出现。在销毁现有报告数据库之前，更改不会生效。", "Number of retries for failed replications": "复制失败的重试次数", "Number of seconds before a timeout. Increase this value if AD DNS queries time out.": "DNS超时（秒）。如果DNS查询超时，请增加此值。", "Number of seconds before timeout. To view the AD  connection status, open the interface <i>Task Manager</i>.": "超时前的秒数。要查看AD连接状态，请打开界面<i>任务管理器</i>。", "Number of seconds to wait after a network failure  before triggering a failover. <i>0</i> means a failover occurs  immediately, or after two seconds when the system is using a link  aggregation.": "网络故障后等待触发故障转移之前要等待的秒数。<i> 0 </i>表示故障转移立即发生，或者在系统使用链路聚合时两秒钟后发生。", "Number of simultaneous file transfers. Enter a number based on the available bandwidth and destination system performance. See <a href=\"https://rclone.org/docs/#transfers-n\" target=\"_blank\">rclone --transfers</a>.": "同时传输的文件数。根据可用带宽和目标系统性能输入数字。请参见<a href=\"https://rclone.org/docs/\\#transfers-n\" target=\"_blank\"> rclone --transfers </a>。", "Number of threads": "线程数", "Number of times the replication is attempted  before stopping and marking the task as failed.": "在停止并将任务标记为失败之前尝试复制的次数。", "Number of virtual CPUs to allocate to the virtual machine. The VM operating system might have operational or licensing restrictions on the number of CPUs.": "分配给虚拟机的虚拟CP 数量。虚拟机操作系统可能对CPU数量有操作或许可限制。", "OAuth Authentication": "OAuth认证", "OAuth Client ID": "Oauth客户端ID", "OAuth Client Secret": "Oauth Client 秘密", "OAuth Token for current session": "当前会话的 OAuth 令牌", "OFFLINE": "离线", "OK": "是", "ONLINE": "在线", "OQ % Used": "已使用对象配额 %", "OQ Used": "使用过的 OQ", "OS Version": "操作系统版本", "OTHER": "其他", "Object Quota": "对象配额", "Oct": "十月", "Off": "关", "Off by default. When set, <a href=\"https://www.samba.org/samba/docs/current/man-html/smbd.8.html\" target=\"_blank\">smbd(8)</a> attempts to authenticate users with the insecure and vulnerable NTLMv1 encryption. This setting allows backward compatibility with older versions of Windows, but is not recommended and should not be used on untrusted networks.": "默认关闭。 当设置时，<a href=\"https://www.samba.org/samba/docs/current/man-html/smbd.8.html\" target=\"_blank\">smbd(8)</a> 将尝试使用不安全的NTLMv1加密方式验证用户。启用此选项可以对老版本的Windows有更好的兼容性，但是不应在不安全的网络中启用。", "Offline": "离线", "Offline Disk": "离线磁盘", "Offline disk {name}?": "离线磁盘 {name}？", "Ok": "是", "On": "开", "On a Different System": "在不同的系统上", "On this System": "在这个系统上", "One or more data vdevs has disks of different sizes.": "一个或多个数据vdev具有不同大小的磁盘。", "One-Time Password (OTP) Digits": "一次性数字密码（OTP）", "One-Time Password (if necessary)": "一次性密码（如有必要）", "One-Time Password if two factor authentication is enabled.": "如果启用了两因素身份验证，则为一次性密码。", "Online": "在线", "Online Disk": "在线磁盘", "Online disk {name}?": "在线磁盘{name}？", "Only Replicate Snapshots Matching Schedule": "仅复制快照匹配计划", "Only appears if <i>Device</i> is selected. Select the unused zvol or zvol snapshot.": "仅在选择<i>设备</i>时出现。选择未使用的zvol或zvol快照。", "Only appears if a <i>File</i> or  zvol is selected. When the specified percentage of free space is reached, the system issues an alert.": "仅在选择<i>文件</i>或zvol时显示。当达到指定的可用空间百分比时，系统将发出警报。", "Only entered when configuring mutual CHAP. Usually the same value as <i>User</i>.": "仅在配置相互CHAP时输入。通常与<i> User </i>相同。", "Only first {number} examples are shown.": "只有前 {number} 个示例被显示。", "Only image(s) will be updated": "仅更新镜像", "Only needed when connecting to a Team Drive. The ID of the top  level folder of the Team Drive.": "仅在连接到团队驱动器时需要。Team Drive的顶级文件夹的ID。", "Only numeric ids are allowed.": "只允许使用数字 ID。", "Only one can be active at a time.": "一次只能激活一个。", "Only override the default if the initiator requires a different block size.": "仅当启动器要求不同的块大小时才覆盖默认值。", "Only replicate snapshots that match a  defined creation time. To specify which snapshots will be replicated,  set this checkbox and define the snapshot creation times that will be  replicated. For example, setting this time frame to <i>Hourly</i> will  only replicate snapshots that were created at the beginning of each hour.": "仅复制与定义的创建时间匹配的快照。要指定将复制哪些快照，请设置此复选框并定义将复制的快照创建时间。例如，将此时间范围设置为<i>每小时</i>将仅复制在每小时开始时创建的快照。", "Only set LDAP authentication for SMB shares is required and the LDAP server is already configured with Samba attributes. DEPRECATED:  Support for Samba Schema is <a href=\"https://www.samba.org/samba/history/samba-4.13.0.html\" target=\"_blank\">officially deprecated in Samba 4.13</a>.  The feature will be removed after Samba 4.14. Users should begin  upgrading legacy Samba domains to Samba AD domains.": "只需要为SMB共享设置LDAP身份验证，并且LDAP服务器已经配置了Samba属性。已弃用：对Samba架构的支持<a href=\"https://www.samba.org/samba/history/samba-4.13.0.html\" target=\"_blank\">在Samba 4.13中正式弃用< /a>。该功能将在Samba 4.14之后移除。用户应该开始将旧的Samba域升级到Samba AD域。", "Open": "开启", "Open Network": "开放网络", "Open Ticket": "打开工单", "Open TrueCommand User Interface": "打开TrueCommand用户界面", "Open VPN Client": "Open VPN客户端", "OpenVPN": "OpenVPN", "OpenVPN Client Settings": "OpenVPN客户端设置", "OpenVPN Server": "OpenVPN 服务器", "OpenVPN Server Settings": "OpenVPN服务器设置", "OpenVPN {serviceLabel} service failed to start.": "OpenVPN {serviceLabel} 服务无法启动。", "OpenVPN {serviceLabel} service failed to stop.": "OpenVPN {serviceLabel} 服务无法停止。", "Openstack API key or password. This is the OS_PASSWORD from an <a href=\"https://rclone.org/swift/#configuration-from-an-openstack-credentials-file\" target=\"_blank\">OpenStack credentials file</a>.": "Openstack API密钥或密码。这是来自OS_PASSWORD的<a href=\"https://rclone.org/swift/\\#configuration-from-an-openstack-credentials-file\" target=\"_blank\"> OpenStack凭证文件</a>。", "Openstack user name for login. This is the OS_USERNAME from an <a href=\"https://rclone.org/swift/#configuration-from-an-openstack-credentials-file\" target=\"_blank\">OpenStack credentials file</a>.": "Openstack登录用户名。这是来自OS_USERNAME的<a href=\"https://rclone.org/swift/\\#configuration-from-an-openstack-credentials-file\" target=\"_blank\"> OpenStack凭证文件</a>。", "Operating System": "操作系统", "Operation": "操作", "Optional description. Portals are automatically assigned a numeric group.": "可选描述。门户会自动分配一个数字组。", "Optional user-friendly name.": "可选的用户友好名称。", "Optional.": "可选的。", "Optional. Enter a server description.": "可选。输入服务器描述。", "Optional. Only needed for private images.": "可选的。仅用于私人镜像。", "Optional: CPU Set (Examples: 0-3,8-11)": "可选：CPU 组（示例：0-3,8-11）", "Optional: Choose installation media image": "可选：选择安装媒体镜像", "Optional: NUMA nodeset (Example: 0-1)": "可选：NUMA 节点集（示例：0-1）", "Options": "选项", "Options cannot be loaded": "选项无法被加载", "Options for encrypting the LDAP connection:  <ul>  <li><i>OFF:</i> do not encrypt the LDAP connection.</li>  <li><i>ON:</i> encrypt the LDAP connection with <i>SSL</i> on port  <i>636</i>.</li>  <li><i>START_TLS:</i> encrypt the LDAP connection with <i>STARTTLS</i>  on the default LDAP port <i>389</i>.</li>  </ul>": "用于加密LDAP连接的选项：<ul> <li> <i>关闭：</i>不加密LDAP连接。</li><li> <i>打开：</i>使用<i> 636 </i>端口上的<i> SSL </i>加密LDAP连接。</li><li> <i> START_TLS：</i>使用默认LDAP端口<i> 389 </i>上的<i> STARTTLS </i>加密LDAP连接。</li> </ul>", "Order": "命令", "Organization": "组织", "Organizational Unit": "组织单位", "Organizational unit of the entity.": "实体组织单位。", "Other": "其他", "Other Options": "其他选项", "Other Settings": "其他设定", "Other TrueNAS controller cannot be reached.": "无法访问其他 TrueNAS 控制器。", "Other TrueNAS controller has no license.": "其他 TrueNAS 控制器没有许可证。", "Other TrueNAS controller has not finished booting.": "其他 TrueNAS 控制器尚未完成启动。", "Other node is currently processing a failover event.": "其他节点当前正在处理故障转移事件。", "Others": "其他", "Out": "发送", "Outbound Activity": "出境活动", "Outbound Network": "出站网络", "Outbound Network:": "出站网络：", "Outgoing Mail Server": "邮件发送服务器", "Overrides default directory creation mask of <i>0777</i>  which grants directory read, write and execute access for everybody.": "覆盖<i>0777</i>的默认目录创建掩码，该掩码授予每个人对目录的读取，写入和执行访问权限。", "Overrides default file creation mask of <i>0666</i>  which creates files with read and write access for everybody.": "覆盖<i>0666</i>的默认文件创建掩码，该掩码创建对所有人具有读写访问权限的文件。", "Overview": "概述", "Owner": "所有者", "Owner Group": "所有者组", "Owner:": "所有者：", "Ownership": "所有权", "Ownership of all files in the share will  be changed to user <samp>webdav</samp> and group <samp>webdav</samp>.  Existing permissions will not be changed, but the ownership change  might make files inaccesible to their original owners. This operation  cannot be undone! If unset, ownership of files to be accessed through  WebDAV must be manually set to the <samp>webdav</samp> or  <samp>www</samp> user/group.": "共享中所有文件的所有权将更改为用户<samp> webdav </samp>和组<samp> webdav </samp>。现有权限不会更改，但是所有权更改可能会使文件的原始所有者无法访问。此操作无法撤消！如果未设置，则必须将要通过WebDAV访问的文件的所有权手动设置为<samp> webdav </samp>或<samp> www </samp>用户/组。", "PASSPHRASE": "密码", "PCI Passthrough Device": "PCI 直通设备", "POSIX": "POSIX", "POSIX Permissions": "POSIX权限", "PULL": "拉取", "PUSH": "推送", "PagerDuty client name.": "PagerDuty客户端名称。", "Pair this certificate's public key with the Certificate Authority private key used to sign this certificate.": "将此证书的公钥与用于签署此证书的证书颁发机构私钥配对。", "Parent": "父", "Parent Interface": "父接口", "Parent dataset path (read-only).": "父数据集路径(只读)", "Parent path": "父路径", "Passphrase": "密码", "Passphrase and confirmation should match.": "密码和确认密码应该匹配。", "Passthrough": "直通", "Password": "密码", "Password Disabled": "密码已禁用", "Password Server": "密码服务器", "Password Servers": "密码服务器", "Password and confirmation should match.": "密码和确认密码应该匹配。", "Password associated with the LDAP User DN.": "与LDAP用户DN关联的密码。", "Password for logging in to the provider and  updating the record. ": "登录供应商和更新记录的密码。 ", "Password for the Active Directory administrator account.  Required the first time a domain is configured. After initial configuration, the password  is not needed to edit, start, or stop the service.": "活动目录管理员帐户的密码。首次配置域时需要。初始配置后，不需要密码即可编辑，启动或停止服务。", "Password for the Bind DN.": "绑定DN的密码。", "Password for the SSH Username account.": "SSH用户名帐户的密码。", "Password for the user account.": "用户帐户的密码。", "Password is not set": "未设置密码", "Password is set": "密码已设置", "Password to encrypt and decrypt remote  data. <b>Warning:</b> Always securely back up this password! Losing the  encryption password will result in data loss.": "加密和解密远程数据的密码。<b>警告：</b>一定要安全备份此密码！丢失加密密码将导致数据丢失。", "Password updated.": "密码已更新。", "Passwords cannot contain a <b>?</b>. Passwords should be at least eight characters and contain a mix of lower and upper case, numbers, and special characters.": "密码不能包含 <b>?</b>。密码至少应包含八个字符，并且包含大小写，数字和特殊字符的组合。", "Passwords do not match": "密码不匹配", "Paste either or both public and private keys. If only a public key is entered,  it will be stored alone. If only a private key is pasted, the public key will be automatically  calculated and entered in the public key field.  Click <b>Generate Keypair</b> to create a new keypair.  Encrypted keypairs or keypairs with passphrases are not supported.": "粘贴公钥和私钥之一或两者。如果仅输入公共密钥，它将被单独存储。如果仅粘贴私钥，则将自动计算公钥并将其输入到公钥字段中。点击<b>生成密钥对</ b>以创建一个新的密钥对。不支持加密的密钥对或带有密码短语的密钥对。", "Paste the <a href=\"https://api.slack.com/incoming-webhooks\" target=\"_blank\">incoming webhook</a> URL associated with this service.": "粘贴与此服务关联的<a href=\"https://api.slack.com/incoming-webhooks\" target=\"_blank\">传入webhook</a> URL。", "Paste the certificate for the CA.": "将证书粘贴到CA。", "Paste the contents of your Certificate Signing Request here.": "在此处粘贴您的证书签名请求的内容。", "Paste the private key associated with the Certificate when available. Please provide a key at least 1024 bits long.": "粘贴与证书关联的私钥（如果可用）。请提供至少1024位长密钥。", "Paste value above or choose a file.": "粘贴上面的值或选择一个文件。", "Path": "路径", "Path Length": "路径长度", "Path Suffix": "路径后缀", "Path to the <b>CheckIP Server</b>. For example,  <i>no-ip.com</i> uses a <b>CheckIP Server</b> of  <i>dynamic.zoneedit.com</i> and <b>CheckIP Path</b> of  <i>/checkip.html</i>.": "<b> CheckIP服务器</b>的路径。例如，<i>no-ip.com</i> 使用 <i>dynamic.zoneedit.com</i> 的  <b>CheckIP服务器</b> 和 <i>/checkip.html</i> 的 <b>CheckIP路径</b> 。", "Path to the Extent": "区块的路径", "Pattern of naming custom  snapshots to include in the replication with the periodic snapshot  schedule. Enter the  <a href=\"https://man7.org/linux/man-pages/man3/strftime.3.html\"  target=\"_blank\">strftime(3)</a> strings that match the snapshots to  include in the replication.<br><br>  When a periodic snapshot is not linked to the replication, enter the  naming schema for manually created snapshots. Has the same <i>&percnt;Y</i>,  <i>&percnt;m</i>, <i>&percnt;d</i>, <i>&percnt;H</i>, and <i>&percnt;M</i> string requirements as  the <b>Naming Schema</b> in a <b>Periodic Snapshot Task</b>. Separate  entries by pressing <code>Enter</code>.": "命名自定义快照以包含在具有定期快照计划的复制中的模式。 输入匹配的 <a href=\"https://man7.org/linux/man-pages/man3/strftime.3.html\" target=\"_blank\">strftime(3)</a> 字符串 要包含在复制中的快照。<br><br> 如果定期快照未链接到复制，请输入手动创建的快照的命名架构。 具有相同的 <i>&percnt;Y</i>、<i>&percnt;m</i>、<i>&percnt;d</i>、<i>&percnt;H</i> 和 <i >&percnt;M</i> 字符串要求作为<b>定期快照任务</b>中的<b>命名架构</b>。 按 <code>Enter</code> 分隔条目。", "Pattern of naming custom snapshots to be  replicated. Enter the name and  <a href=\"https://man7.org/linux/man-pages/man3/strftime.3.html\" target=\"_blank\">strftime(3)</a>  <i>&percnt;Y</i>, <i>&percnt;m</i>, <i>&percnt;d</i>, <i>&percnt;H</i>, and <i>&percnt;M</i> strings that  match the snapshots to include in the replication. Separate entries by  pressing <code>Enter</code>. The number of snapshots matching the  patterns are shown.": "命名要复制的自定义快照的模式。 输入名称和<a href=\"https://man7.org/linux/man-pages/man3/strftime.3.html\" target=\"_blank\">strftime(3)</a> < i>&percnt;Y</i>、<i>&percnt;m</i>、<i>&percnt;d</i>、<i>&percnt;H</i>和<i>&percnt;M </i> 与要包含在复制中的快照匹配的字符串。 按 <code>Enter</code> 分隔条目。 显示匹配模式的快照数量。", "Peer Secret": "对等秘密", "Peer Secret (Confirm)": "对等秘密 (确认)", "Peer User": "对等用户", "Pending Network Changes": "待处理的网络更改", "Pending Sync": "待同步", "Pending Sync Keys Cleared": "已清除待处理的同步键", "Pending Upgrade": "待升级", "Perform Reverse DNS Lookups": "执行 DNS 反向解析", "Performance": "性能", "Performs authentication from an LDAP server.": "从LDAP服务器执行身份验证。", "Periodic Snapshot Task": "定期快照任务", "Periodic Snapshot Tasks": "定期快照任务", "Permission": "允许", "Permissions": "权限", "Permissions Advanced": "高级权限", "Permissions Basic": "基本权限", "Permissions Editor": "权限编辑器", "Permissions Type": "权限类型", "Phone": "电话", "Phone Number": "电话号码", "PiB": "PiB", "Pick a display device to open": "选择要打开的显示设备", "Pin vcpus": "Pin vcpus", "Plain (No Encryption)": "普通（无加密）", "Platform": "平台", "Please accept the terms of service for the given ACME Server.": "请接受给定ACME服务器的服务条款。", "Please click the button below to create a pool.": "请单击下面的按钮创建一个池。", "Please input password.": "请输入密码。", "Please input user name.": "请输入用户名。", "Please make sure to select a file": "请确保选择一个文件", "Please select a tag": "请选择一个标签", "Please select a valid SSH Connection": "请选择一个有效的SSH连接", "Please select a valid path": "请选择一个有效的路径", "Please specifies tag of the image": "请指定镜像的标签", "Please specify a valid git repository uri.": "请指定有效的git存储库uri。", "Please specify branch of git repository to use for the catalog.": "请指定用于目录的git存储库的分支。", "Please specify name to be used to lookup catalog.": "请指定用于查找目录的名称。", "Please specify the name of the image to pull. Format for the name is \"registry/repo/image\"": "请指定要拉取的镜像的名称。名称格式为\"registry/repo/image\"", "Please specify trains from which UI should retrieve available applications for the catalog.": "请指定UI应从中检索目录可用应用程序的列车。", "Please visit our support page for help.": "请访问我们的支持页面寻求帮助。", "Please wait": "请稍后", "Pod Logs": "pod日志", "Pod Name": "pod名", "Pod Shell": "Pod Shell", "Pods": "Pods", "Pool": "池", "Pool Available Space Threshold (%)": "池可用空间阈值 (%)", "Pool Manager": "池管理器", "Pool Options for {pool}": "{pool} 的池选项", "Pool Status": "池状态", "Pool has been unset.": "池已取消设置。", "Pool options for {poolName} successfully saved.": "{poolName} 的池选项已成功保存。", "Pool {name} successfully upgraded.": "池 {name} 已成功升级。", "Pool/Dataset": "池/数据集", "Pools": "池", "Pools in Enclosure": "机柜中的池", "Port": "端口", "Port number on the remote system to use for the SSH  connection.": "远程系统上用于SSH连接的端口号。", "Port or Hostname": "端口或主机名", "Portal": "门户", "Portal Group ID": "门户组 ID", "Portals": "门户", "Ports": "端口", "Post Init": "初始化后期", "Post-script": "后脚本", "Power": "电源", "Power Management": "电源管理", "Power Mode": "电源模式", "Power Off": "关闭电源", "Power Off UPS": "关闭UPS电源", "Pre Init": "初始化前期", "Pre-script": "预脚本", "Predefined certificate extensions. Choose a profile that best matches your certificate usage scenario.": "预定义的证书扩展。选择最适合您证书使用情况的配置文件。", "Predefined permission combinations:<br><i>Read</i>: Read access and Execute permission on the object (RX).<br><i>Change</i>: Read access, Execute permission, Write access, and Delete object (RXWD).<br><i>Full</i>: Read access, Execute permission, Write access, Delete object, change Permissions, and take Ownership (RXWDPO).<br><br> For more details, see <a href=\"https://www.samba.org/samba/docs/current/man-html/smbcacls.1.html\" target=\"_blank\">smbacls(1)</a>.": "预定义的权限组合：<br> <i>读取</ i>：对对象（RX）的读取访问权限和执行权限。<br> <i>更改</ i>：读取权限，执行权限，写入权限和删除对象（RXWD）。<br> <i>完整</ i>：读取访问权限，执行权限，写入权限，删除对象，更改权限并获得所有权（RXWDPO）。<br> <br>有关更多详细信息，请参见<a href=\"https://www.samba.org/samba/docs/current/man-html/smbcacls.1.html\" target=\"_blank\"> smbacls(1)</a>。", "Prefer": "首选", "Preferred Trains": "首选列车", "Preserve Extended Attributes": "保留扩展属性", "Preserve Permissions": "保留权限", "Preset": "预设", "Presets": "预设", "Prevent source system snapshots  that have failed replication from being automatically removed by the  <b>Snapshot Retention Policy</b>.": "防止复制失败的源系统快照被<b>快照保留策略</b>自动删除。", "Prevent the user from logging in or  using password-based services until this option is unset. Locking an  account is only possible when <b>Disable Password</b> is <i>No</i> and  a <b>Password</b> has been created for the account.": "在未设置此选项之前，请阻止用户登录或使用基于密码的服务。仅当<b>禁用密码</ b>为<i>否</ i>并且已为该帐户创建<b>密码</ b>时，才可以锁定帐户。", "Preview JSON Service Account Key": "预览JSON服务帐户密钥", "Primary Contact": "主要联系人", "Primary DNS server.": "主DNS服务器。", "Primary Group": "主用户组", "Priority Code Point": "优先代码点", "Privacy Passphrase": "隐私密码", "Privacy Protocol": "隐私协议", "Private Key": "私钥", "Privileges are the same as the guest account.  Guest access is disabled by default in Windows 10 version 1709 and  Windows Server version 1903. Additional client-side configuration is  required to provide guest access to these clients.<br><br>  <i>MacOS clients:</i> Attempting to connect as a user that does not  exist in TrueNAS <i>does not</i> automatically connect as the guest  account. The <b>Connect As:</b> <i>Guest</i> option must be  specifically chosen in MacOS to log in as the guest account. See the  <a href=\"https://support.apple.com/guide/mac-help/connect-mac-shared-computers-servers-mchlp1140/\" target=\"_blank\">Apple documentation</a>  for more details.": "权限与来宾帐户相同。 默认情况下，在 Windows 10 版本 1709 和 Windows Server 版本 1903 中禁用访客访问。需要额外的客户端配置才能提供对这些客户端的访客访问。<br><br> <i>MacOS 客户端：</i> 以 TrueNAS 中不存在的用户身份连接<i>不会</i>自动以访客帐户身份连接。 <b>Connect As:</b> <i>Guest</i> 选项必须在 MacOS 中专门选择才能以访客帐户登录。 请参阅 <a href=\"https://support.apple.com/guide/mac-help/connect-mac-shared-computers-servers-mchlp1140/\" target=\"_blank\">Apple 文档</ a> 了解更多详情。", "Proactive Support": "主动支持", "Proactive support settings is not available.": "主动支持设置不可用。", "Proceed": "继续", "Proceed with upgrading the pool? WARNING: Upgrading a pool is a one-way operation that might make some features of the pool incompatible with older versions of TrueNAS: ": "继续升级池？警告：升级池是一种单向操作，它可能会使池的某些功能与较旧的TrueNAS版本不兼容：", "Product": "产品", "Product ID": "产品ID", "Production": "作业", "Production status successfully updated": "生产状态已成功更新", "Profiles": "简介", "Prohibits writes to this share.": "禁止写入此共享。", "Properties Exclude": "排除属性", "Properties Override": "覆盖属性", "Protocol": "协议", "Prototyping": "原型制作", "Provide static key for authentication/encryption of all control  channel packets when <code>tls_crypt_auth_enabled</code> is enabled.": "启用<code>tls_crypt_auth_enabled</code>时，为所有控制通道数据包的身份验证/加密提供静态密钥。", "Provider": "提供者", "Provides a plugin interface for Winbind to use varying backends to store  SID/uid/gid mapping tables. The correct setting depends on the environment in which the NAS is deployed.": "为Winbind提供一个插件接口，以使用各种后端来存储SID/uid/gid映射表。正确的设置取决于NAS部署的环境。", "Provisioning Type": "供应类型", "Provisioning URI (includes Secret - Read only):": "设置URI（包括秘密（Secret）-只读）：", "Public IP address or hostname. Set if FTP clients cannot connect through a NAT device.": "公网IP地址或域名。设置FTP客户端是否无法通过NAT设备连接。", "Public Key": "公钥", "Pull": "拉取", "Pull Image": "拉取镜像", "Pulling...": "拉取中...", "Purpose": "目的", "Push": "推送", "Quick": "快速", "Quiet": "静默", "Quota critical alert at, %": "配额严重警告，％", "Quota for this dataset": "此数据集的配额", "Quota for this dataset and all children": "此数据集和所有子级的配额", "Quota size is too small, enter a value of 1 GiB or larger.": "配额大小太小，请输入1 GiB或更大的值。", "Quota warning alert at, %": "配额警告提醒，％", "Quotas set for {n, plural, one {# group} other {# groups}}": "为 {n, plural, 1 {# group} other {# groups}}设置配额", "Quotas set for {n, plural, one {# user} other {# users}}": "为 {n, plural, 1 {# user} other {# users}}设置配额", "REAR": "REAR", "REMOTE": "远程", "REQUIRE": "REQUIRE", "RPM": "RPM", "RSYNC Modules": "RSYNC 模块", "Raid-z": "Raid-z(5)", "Raid-z2": "Raid-z2(6)", "Raid-z3": "Raid-z3(7)", "Randomly generate an encryption key for securing this dataset. Disabling requires manually      defining the encryption key.<br> WARNING: the encryption key is the only means to decrypt the information stored in this      dataset. Store the encryption key in a secure location.": "随机生成用于保护此数据集的加密密钥。禁用需要手动定义加密密钥。<br>警告：加密密钥是解密此数据集中存储的信息的唯一方法。务必将加密密钥存储在安全的位置。", "Range High": "范围高", "Range Low": "范围低", "Range Low and Range High set the range of UID/GID numbers which this  IDMap backend translates. If an external credential like a Windows SID maps to a UID or GID  number outside this range, the external credential is ignored.": "范围下限和范围上限设置此IDMap后端转换的UID / GID号的范围。如果外部凭据（例如Windows SID）映射到此范围之外的UID或GID号，则将忽略该外部凭据。", "Range Size": "范围大小", "Raw File": "原始文件", "Raw Filesize": "原始文件大小", "Re-Open": "重新打开", "Re-Open All Alerts": "重新打开所有警报", "Read": "只读", "Read ACL": "读取ACL", "Read Attributes": "读取属性", "Read Data": "读取数据", "Read Errors": "读取错误", "Read Named Attributes": "读取命名属性", "Read Only": "只读", "Read and Write": "读和写", "Read-only": "只读", "Realm": "领域", "Reboot": "重启", "Reboot After Update": "更新后重启", "Reboot standby TrueNAS controller": "重启备用 TrueNAS 控制器", "Rebuild Directory Service Cache": "重建目录服务缓存", "Received": "已收到", "Recommended block size based on pool topology:": "基于池拓扑的推荐块大小：", "Recommended when <i>Max. Poll</i> is greater than 10. Only use on personal NTP servers or those under direct control. <b>Do not</b> enable when using public NTP servers.": "当<i> 最大轮询 </i>大于10时推荐使用。仅在个人NTP服务器或直接控制的服务器上使用。<b>请勿</b>在使用公共NTP服务器时启用。", "Reconnect": "重新连接", "Record SMB service messages up to the specified log level.  By default, error and warning level messages are logged.": "将SMB服务消息记录到指定的日志级别。默认情况下，会记录错误和警告级别的消息。", "Record Size": "记录大小", "Recursive": "递归的", "Redirect <i>HTTP</i> connections to <i>HTTPS</i>. A  <i>GUI SSL Certificate</i> is required for <i>HTTPS</i>. Activating  this also sets the <a  href=\"https://en.wikipedia.org/wiki/HTTP_Strict_Transport_Security\"  target=\"_blank\">HTTP Strict Transport Security (HSTS)</a> maximum age  to <i>31536000</i> seconds (one year). This means that after a  browser connects to the web interface for the first time, the browser  continues to use HTTPS and renews this setting every year.": "将<i> HTTP </i>连接重定向到<i> HTTPS </i>。<i> HTTPS </i>需要<i> GUI SSL证书</i>。激活它还会将<a  href=\"https://en.wikipedia.org/wiki/HTTP_Strict_Transport_Security\"  target=\"_blank\"> HTTP严格传输安全性（HSTS）</a>的最大年龄设置为<i>31536000</i>秒（一年）。这意味着在浏览器首次连接到Web界面后，浏览器将继续使用HTTPS并每年更新此设置。", "Reenter Password": "重新输入密码", "Referenced": "被引用", "Refresh": "刷新", "Refresh All": "刷新全部", "Refresh Events": "刷新事件", "Refreshing": "刷新中", "Region": "区域", "Region Name": "区域名称", "Region name - optional <a href=\"https://rclone.org/swift/#standard-options\" target=\"_blank\">(rclone documentation)</a>.": "区域名称-可选的<a href=\"https://rclone.org/swift/\\#standard-options\" target=\"_blank\">（rclone文档）</a>。", "Register": "注册", "Register Default Gateway": "注册默认网关", "Release Notes": "发行笔记", "Reload now": "重新加载", "Reload the page": "重新加载页面", "Reload the page for the license to take effect.": "重新加载页面以使许可证生效。", "Remaining": "剩余", "Remote": "远程", "Remote Controller": "遥控器", "Remote Encryption": "远程加密", "Remote Graphite Server Hostname": "远程Graphite服务器主机名", "Remote Host": "远程主机", "Remote Host Key": "远程主机密钥", "Remote Module Name": "远程模块名", "Remote Monitor": "远程监控", "Remote Path": "远程路径", "Remote Port": "远程端口", "Remote SSH Port": "远程 SSH 端口", "Remote syslog server DNS hostname or IP address. Nonstandard port numbers can be used by adding a colon and the port number to the hostname, like <samp>mysyslogserver:1928</samp>. Log entries are written to local logs and sent to the remote syslog server.": "远程系统日志服务器DNS主机名或IP地址。可以通过在主机名上添加冒号和端口号来使用非标准端口号，例如<samp>mysyslogserver:1928</samp>。 日志条目将写入本地日志，然后发送到远程syslog服务器。", "Remote system SSH key for this system to  authenticate the connection. When all other fields are properly  configured, click <b>DISCOVER REMOTE HOST KEY</b> to query the remote  system and automatically populate this field.": "该系统用于验证连接的远程系统SSH密钥。正确配置所有其他字段后，单击<b>发现远程主机密钥</b>以查询远程系统并自动填充此字段。", "Remove": "删除", "Remove Invalid Quotas": "删除无效配额", "Remove Keep Flag": "删除保留标志", "Remove device": "删除设备", "Remove device {name}?": "移除设备 {name}吗？", "Remove file": "删除文件", "Remove file?": "删除文件？", "Remove the ACL and permissions from child datasets of the current dataset": "从当前数据集的子数据集中删除ACL和权限", "Remove the existing API key and generate a new random key. A dialog shows the new key and has an option to copy the key. Back up and secure the API key! The key string is displayed only one time, at creation.": "删除现有的API密钥并生成一个新的随机密钥。对话框会显示新密钥，并具有复制密钥的选项。请备份并保护好API密钥！关键字字符串在创建时仅显示一次。", "Removes the ACL and permissions recursively  from all child datasets of the current dataset, including all directories and files contained within those child datasets. This can make data inaccessible until new permissions are created.": "从当前数据集的所有子数据集中递归删除ACL和权限，包括这些子数据集中包含的所有目录和文件。这可能会使数据无法访问，直到创建新权限。", "Rename": "重命名", "Rename Boot Environment": "重命名引导环境", "Renew": "更新", "Renew Certificate Days": "续签证书天数", "Renew Secret": "更新秘密（Secret）", "Renew Static Key": "续订静态密钥", "Renewing the secret will cause a new URI and a     new QR code to be generated, making it necessary to update your two-factor device or app.": "更新秘密（secret）将导致生成新的URI和新的QR代码，因此有必要更新您的双重设备或应用。", "Reorder": "重新排序", "Repeat Data VDev": "重复数据VDev", "Repeat Vdev": "重复Vdev", "Repeat first Vdev": "重复第一个 Vdev", "Replace": "更换", "Replace Disk": "更换磁盘", "Replace existing dataset properties with these new defined properties in the replicated files.": "使用复制文件中这些新定义的属性替换现有数据集属性。", "Replacing Boot Pool Disk": "更换引导池磁盘", "Replacing Disk": "正在更换磁盘", "Replacing disk {name}": "更换磁盘 {name}", "Replacing disk...": "正在更换磁盘...", "Replicate Custom Snapshots": "复制自定义快照", "Replicate Specific Snapshots": "复制特定快照", "Replicate all child dataset snapshots. When  set, <b>Exclude Child Datasets</b> becomes available.": "复制所有子数据集快照。设置后，<b>排除子数据集</b>变为可用。", "Replicate snapshots that have not been  created by an automated snapshot task. Requires setting a naming schema  for the custom snapshots.": "复制尚未由自动快照任务创建的快照。需要为自定义快照设置命名惯例。", "Replicate «{name}» now?": "现在复制 «{name}» 吗？", "Replication": "复制", "Replication <i>{name}</i> has started.": "复制 <i>{name}</i> 已开始。", "Replication Schedule": "复制计划", "Replication Summary": "复制摘要", "Replication Task": "复制任务", "Replication Task Wizard": "复制任务向导", "Replication Tasks": "复制任务", "Replication Tasks Limit": "复制任务限制", "Replication from scratch": "从头开始复制", "Replication «{name}» has started.": "复制 «{name}» 已开始。", "Report CPU usage in percent": "以百分比报告CPU使用率", "Report history is cleared when <i>Graph Age</i>, or <i>Graph Points</i> are changed.": "当 <i>Graph Age</i> 或 <i>Graph Points</i> 改变时，报告历史会被清除。", "Report if drive temperature is at or above this temperature in Celsius. <i>0</i> disables the report.": "报告驱动器温度是否达到或高于此温度（摄氏度）。<i>0</i>禁用这个报告。", "Report if the temperature of a drive has changed by this many degrees Celsius since the last report. <i>0</i> disables the report.": "报告自上次报告以来驱动器的温度是否已改变了这么多摄氏度。<i> 0 </i>禁用报告。", "Reporting": "报告", "Reports": "报告", "Reports Config": "报告配置", "Reports Configuration": "报告配置", "Repository": "存储库", "Requested action performed for selected Applications": "为选定的应用程序执行的请求操作", "Require IDENT Authentication": "要求 IDENT 认证", "Require Kerberos for NFSv4": "NFSv4 需要 Kerberos", "Required unless <b>Enable password login</b> is <i>No</i>. Passwords cannot contain a <b>?</b>.": "除非<b>启用密码登录</b>为<i>否</i>，否则为必填项。密码不能包含 <b>?</b>。", "Reserved for Dataset": "为数据集保留", "Reserved for Dataset & Children": "为数据集和子项保留", "Reserved space for this dataset": "此数据集的保留空间", "Reserved space for this dataset and all children": "此数据集及其子数据集的保留空间", "Reset": "重置", "Reset Config": "重置配置", "Reset Configuration": "重置配置", "Reset Layout": "重置布局", "Reset Zoom": "重置缩放", "Reset configuration for ": "重置配置", "Reset system configuration to default settings. The system  will restart to complete this operation. You will be required to reset your password.": "将系统配置重置为默认设置。系统将重新启动以完成此操作。您将需要重设密码。", "Reset the token": "重置 token", "Reset to Defaults": "重置为默认值", "Reset to default": "重置为默认", "Resetting system configuration to default settings. The system will restart.": "将系统配置重置为默认设置。系统将重新启动。", "Resetting. Please wait...": "重置中。请稍等...", "Resilver Priority": "重排优先级", "Resilvering": "重新同步", "Resilvering Status": "重新同步状态", "Resilvering pool: ": "重新同步 池", "Resolution": "分辨率", "Restart": "重启", "Restart SMB Service": "重启SMB服务", "Restart SMB Service?": "重启SMB服务吗？", "Restart Service": "重启服务", "Restart Web Service": "重启网络服务", "Restart the system?": "重启系统吗？", "Restore": "恢复", "Restore Cloud Sync Task": "恢复云同步任务", "Restore Default": "恢复默认", "Restore Replication Task": "恢复复制任务", "Restrict PAM": "限制PAM", "Restrict share visibility to users with read or write access to the share. See the <a href=\"https://www.samba.org/samba/docs/current/man-html/smb.conf.5.html\" target=_blank>smb.conf</a> manual page.": "将共享可见性限制为对共享具有读取或写入访问权限的用户。 请参阅 <a href=\"https://www.samba.org/samba/docs/current/man-html/smb.conf.5.html\" target=_blank>smb.conf</a> 手册页 .", "Restricted": "受限制的", "Retention": "保留", "Retrieving catalog": "检索目录", "Return to pool list": "返回池列表", "Revert Changes": "还原更改", "Revert Network Interface Changes": "还原网络接口更改", "Revert interface changes? All changes that are being tested will be lost.": "还原界面更改？正在测试的所有更改都将丢失。", "Revoke": "撤销", "Revoke Certificate": "撤销证书", "Revoke Certificate Authority": "撤销证书颁发机构", "Revoked": "已撤销", "Revoking Certificate": "撤销证书中", "Revoking this CA will revoke the complete CA chain. This is a one way action and cannot be reversed. Are you sure you want to revoke this CA?": "撤销此CA将撤销完整的CA链。这是一种单向操作，无法逆转。您确定要撤销此CA吗？", "Roles": "角色", "Roll Back": "回滚", "Roll back snapshots": "回滚快照", "Roll back snapshots of ix_volumes": "回滚ix_volumes的快照", "Rollback": "回滚", "Rolling back...": "回滚中...", "Root CA": "根CA", "Root dataset ACL cannot be edited.": "根数据集ACL无法编辑。", "Root node is not a valid value": "根节点无效", "Rotation Rate": "转速", "Rotation Rate (RPM)": "转速（RPM）", "Route v4 Gateway": "路由v4网关", "Route v4 Interface": "路由v4接口", "Routing Key": "路由键", "Rsync Mode": "Rsync模式", "Rsync Task": "同步任务", "Rsync Tasks": "同步任务", "Rsync task «{name}» has started.": "Rsync 任务 «{name}» 已启动。", "Run As User": "以哪个用户身份运行", "Run Automatically": "自动运行", "Run Manual Test": "进行手动测试", "Run Now": "立即运行", "Run On a Schedule": "按计划运行", "Run Once": "运行一次", "Run job": "运行任务", "Run this cloud sync now?": "现在运行此云同步？", "Run this job now?": "立即执行这个任务？", "Run this rsync now?": "现在运行此Rsync任务？", "Running": "运行中", "S.M.A.R.T. Enabled": "S.M.A.R.T. 已启用", "S.M.A.R.T. Extra Options": "S.M.A.R.T. 额外选项", "S.M.A.R.T. Info for {disk}": "S.M.A.R.T. 信息 {disk}", "S.M.A.R.T. Options": "S.M.A.R.T. 选项", "S.M.A.R.T. Tasks": "S.M.A.R.T. 任务", "S.M.A.R.T. Test": "S.M.A.R.T.测试", "S.M.A.R.T. Tests": "S.M.A.R.T.测试", "S.M.A.R.T. extra options": "S.M.A.R.T.附加选项", "S.M.A.R.T./SED": "S.M.A.R.T./SED", "SAN": "SAN", "SAS Expanders": "SAS 扩展器", "SAVE": "保存", "SED Password": "SED密码", "SED password and confirmation should match.": "SED 密码和确认密码应该匹配。", "SED password updated.": "SED 密码已更新。", "SET": "SET", "SFTP Log Facility": "SFTP日志功能", "SFTP Log Level": "SFTP日志级别", "SHORT": "短", "SHOW EXPANDER STATUS": "显示扩展器状态", "SHOW POOLS": "显示池", "SHOW STATUS": "显示状态", "SID": "SID", "SMB - Primary Domain": "SMB - 主域", "SMB Service": "SMB服务", "SMB Service is not currently running. Start the service now?": "SMB 服务当前未运行。 现在开始服务？", "SMB Share": "SMB 共享", "SMB/NFSv4": "SMB/NFSv4", "SMTP Authentication": "SMTP 认证", "SMTP port number. Typically <i>25,465</i> (secure SMTP), or <i>587</i> (submission).": "SMTP端口号。通常为<i> 25,465 </i>（安全SMTP）或<i> 587 </i>（提交）。", "SNMP Community": "SNMP社区", "SNMP v3 Options": "SNMP v3选项", "SNMP v3 Support": "SNMP v3支持", "SNMPv3 Security Model": "SNMPv3安全模型", "SSH": "SSH", "SSH Connection": "SSH连接", "SSH Connections": "SSH连接", "SSH Host to connect to.": "要连接的SSH主机。", "SSH Keypair": "SSH密钥对", "SSH Keypairs": "SSH密钥对", "SSH Transfer Security": "SSH传输安全性", "SSH Username.": "SSH用户名.", "SSH connection from the keychain": "来自钥匙串的 SSH 连接", "SSH port number. Leave empty to use the default port <i>22</i>.": "SSH端口号。保留为空以使用默认端口<i> 22 </i>。", "SSH private key stored in user's home directory": "SSH 私钥存储在用户的主目录中", "SSH+NETCAT": "SSH+NETCAT", "SSL": "SSL", "SSL (Implicit TLS)": "SSL（隐式TLS）", "SSSD Compat": "SSSD兼容性", "START REPLICATION": "开始复制", "SYNC": "同步", "Samba Authentication": "Samba认证", "Samba Schema (DEPRECATED - see help text)": "Samba模式（已弃用 - 请参阅帮助文本）", "Same as Source": "与来源相同", "Sat": "周六", "Saturday": "周六", "Save": "保存", "Save Access Control List": "保存访问控制列表", "Save And Failover": "保存与故障转移", "Save Changes": "保存更改", "Save Configuration": "保存配置", "Save Debug": "保存调试", "Save Pending Snapshots": "保存待处理的快照", "Save Without Restarting": "保存而不重启", "Save and Restart SMB Now": "保存并重启SMB", "Save configuration settings from this machine before updating?": "更新之前保存本机的配置设置吗？", "Save network interface changes?": "保存更改的网络接口吗？", "Saving Debug": "正在保存调试", "Saving KMIP Config": "正在保存KMIP配置", "Saving Permissions": "正在保存权限", "Saving Permissions...": "正在保存权限...", "Scan This QR Code": "扫描此二维码", "Schedule": "计划", "Schedule Preview": "计划预览", "Scheduled Scrub Task": "计划的擦除任务", "Schema": "架构", "Schema Mode": "架构模式", "Script": "脚本", "Script to execute after running sync.": "在运行rsync后运行的脚本。", "Script to execute before running sync.": "在运行rsync前运行的脚本。", "Scrub": "校验", "Scrub Boot Pool": "校验启动池", "Scrub In Progress": "正在擦除", "Scrub Pool": "校验池", "Scrub Started": "开始校验", "Scrub Task": "校验任务", "Scrub Tasks": "校验任务", "Scrub interval (in days)": "校验间隔（天）", "Scrub interval set to {scrubIntervalValue} days": "校验间隔设置为 {scrubIntervalValue} 天", "Search": "搜索", "Secondary Contact": "次要联系人", "Secondary DNS server.": "备用DNS服务器。", "Secondary Email": "次要电子邮件", "Secondary Name": "次要名称", "Secondary Phone Number": "次要电话号码", "Secondary Title": "次要标题", "Secret": "秘密", "Secret (Confirm)": "秘密 (确认)", "Secret (Read only)": "秘密（只读）", "Secret Access Key": "秘密访问密钥", "Secret Access Key for the linked AWS account.": "链接的AWS账户的秘密访问密钥。", "Secret Authentication Key": "秘密认证密钥", "Secret Encryption Key": "秘密加密密钥", "Secret Key": "秘密密钥", "Secret and Peer Secret can not be the same.": "秘密和对等秘密不能相同。", "Secret and confirmation should match.": "密码和确认密码应该匹配。", "Secure data within this dataset. Data is unusable until      unlocked with an encryption key or passphrase.": "保护此数据集中的数据。直到使用加密密钥或密码将其解锁后，数据才可用。", "Security": "安全", "See <a href=\"https://crypto.stackexchange.com/questions/1190/why-is-elliptic-curve-cryptography-not-widely-used-compared-to-rsa\" target=\"blank\"> Why is elliptic curve cryptography not widely used, compared to RSA?</a> for more information about key types.": "参见<a href=\"https://crypto.stackexchange.com/questions/1190/why-is-elliptic-curve-cryptography-not-widely-used-compared-to-rsa\" target=\"blank\">为什么与RSA相比，椭圆曲线密码术没有得到广泛使用？</a>有关密钥类型的更多信息。", "See <i>Public key authentication</i> in <a href=\"https://man7.org/linux/man-pages/man1/ssh.1.html\" target=\"_blank\">SSH/Authentication</a>.": "请参阅 <a href=\"https://man7.org/linux/man-pages/man1/ssh.1.html\" target=\"_blank\">SSH/身份验证</a> 中的 <i>公钥认证</i>。", "See the <a href=\"http://networkupstools.org/stable-hcl.html\" target=\"_blank\">Network UPS Tools compatibility list</a> for a list of supported UPS devices.": "有关支持的UPS设备列表，请参阅<a href=\"http://networkupstools.org/stable-hcl.html\" target=\"_blank\">网络UPS工具兼容性列表</a> 。", "Select <i>Bug</i> when reporting an issue or <i>Suggestion</i> when requesting new functionality.": "报告问题时选择 <i>Bug</i> 或请求新功能时选择 <i>Suggestion</i>。", "Select <i>Command</i> for an executable or     <i>Script</i> for an executable script.": "为可执行文件选择<i> Command </i>或为可执行脚本选择<i> Script </i>。", "Select <i>Create new disk image</i> to create a new zvol on an existing dataset. This is used as a virtual hard drive for the VM. Select <i>Use existing disk image</i> to use an existing zvol or file for the VM.": "选择<i>创建新磁盘映像</i>，以在现有数据集上创建新的zvol。这用作虚拟机的虚拟硬盘驱动器。选择<i>使用现有磁盘映像</i>为虚拟机使用现有的zvol或文件。", "Select <i>None</i> or an integer. This value represents the number of existing authorized accesses.": "选择<i>无</i>或一个整数。该值表示现有授权访问的数量。", "Select <i>UEFI</i> for newer operating systems or <i>UEFI-CSM</i> (Compatibility Support Mode) for older operating systems that only support BIOS booting. <i>Grub</i> is not recommended but can be used when the other options do not work.": "选择<b>UEFI</b>用于较新的操作系统，<b>UEFI-CSM</b>（兼容支持模式）用于只支持BIOS引导的操作系统。", "Select All": "全选", "Select Client Certificate": "选择客户端证书", "Select Configuration File": "选择配置文件", "Select Disk Type": "选择磁盘类型", "Select Existing zvol": "选择现有的zvol", "Select IP addresses to listen to for NFS requests. Leave empty for NFS to listen to all available addresses.  Static IPs need to be configured on the interface to appear on the list.": "选择要侦听 NFS 请求的 IP 地址。为 NFS 留空以侦听所有可用地址。需要在接口上配置静态 IP 才能出现在列表中。", "Select Image Tag": "选择图像标签", "Select Pool": "选择池", "Select a compression algorithm to reduce the size of the data being replicated. Only appears when <i>SSH</i> is  chosen for <i>Transport</i> type.": "选择一种压缩算法以减小要复制的数据的大小。仅在<i>传输</i>类型选择为<i> SSH </i>时显示。", "Select a dataset for the new zvol.": "为新的zvol选择数据集。", "Select a dataset or zvol.": "选择数据集或zvol。", "Select a keyboard layout.": "选择键盘布局。", "Select a language from the drop-down menu.": "从下拉菜单中选择一种语言。", "Select a physical interface to associate with the VM.": "选择要与虚拟机关联的物理接口。", "Select a pool to import.": "选择要导入的池。", "Select a pool, dataset, or zvol.": "选择一个池，数据集，或zvol。", "Select a power management profile from the menu.": "从菜单中选择电源管理配置文件。", "Select a preset ACL": "选择ACL预设", "Select a preset configuration for the share. This applies predetermined values and disables changing some share options.": "选择共享的预设配置。这将应用预定值，并禁止更改某些共享选项。", "Select a preset schedule or  choose <i>Custom</i> to use the advanced scheduler.": "选择一个预设计划，或选择<i>自定义</i>以使用高级计划程序。", "Select a preset schedule or choose  <i>Custom</i> to use the advanced scheduler.": "选择一个预设计划，或选择<i>自定义</i>以使用高级计划程序。", "Select a previously imported or created CA.": "选择一个以前导入或创建的CA。", "Select a saved remote system SSH  connection or choose <i>Create New</i> to create a new SSH connection.": "选择一个已保存的远程系统SSH连接，或选择<i>新建</i>创建一个新的SSH连接。", "Select a schedule preset or choose <i>Custom</i>                to open the advanced scheduler.": "选择计划预设或选择<i>自定义</i>以打开高级计划程序。", "Select a schedule preset or choose <i>Custom</i>  to open the advanced scheduler. Note that an in-progress cron task postpones  any later scheduled instance of the same task until the running task is  complete.": "选择一个计划预设或选择<i>自定义</i>以打开高级计划程序。请注意，正在进行的定时任务会推迟同一任务的任何后续计划实例，直到正在运行的任务完成。", "Select a schedule preset or choose <i>Custom</i> to open the advanced scheduler.": "选择计划预设或选择<i>“自定义”</i>以打开高级计划程序。", "Select a schema when Samba Schema is set.": "设置Samba架构时选择架构。", "Select a screen resolution to use for VNC sessions.": "选择一个用于VNC会话的分辨率。", "Select a sector size in bytes. <i>Default</i> leaves the sector size unset and uses the ZFS volume values. Setting a sector size changes both the logical and physical sector size.": "选择以字节为单位的扇区大小。<i>默认</ i>未设置扇区大小，并使用ZFS卷值。设置扇区大小会同时更改逻辑和物理扇区大小。", "Select a time zone.": "选择时区。", "Select a user account to run the command. The                user must have permissions allowing them to run                the command or script.": "选择一个用户帐户以运行命令。用户必须具有允许他们运行命令或脚本的权限。", "Select a valid SSL certificate created or imported  in <b>System > Certificates</b> to use for encrypted connections.": "选择在<b>系统>证书</b>中创建或导入的有效SSL证书，以用于加密连接。", "Select an IP address to use for VNC sessions.": "选择要用于VNC会话的IP地址。", "Select an existing CSR.": "选择一个现有的CSR.", "Select an existing SSH connection  to a remote system or choose <i>Create New</i> to create a new SSH  connection.": "选择与远程系统的现有SSH连接，或选择<i>新建</i>创建新的SSH连接。", "Select an existing SSH connection to a remote system or   choose Create New to create a new SSH connection.": "选择与远程系统的现有 SSH 连接，或选择新建以创建新的 SSH 连接。", "Select an existing extent.": "选择现有区块。", "Select an existing portal or choose <i>Create New</i> to configure a new portal.": "选择一个现有入口或选择<i>新建</i>来配置一个新portal。", "Select an existing realm that was added  in <b>Directory Services > Kerberos Realms</b>.": "选择在<b>目录服务>Kerberos领域</b>中添加的现有领域。", "Select an existing target.": "选择现有目标。", "Select an unused disk to add to this vdev. <br>WARNING: any data stored on the unused disk will be erased!": "选择一个未使用的磁盘以添加到该vdev。<br>警告：未使用的磁盘上存储的所有数据都将被清除！", "Select desired disk type.": "选择所需的磁盘类型。", "Select interfaces for SSH to listen on. Leave all options unselected for SSH to listen on all interfaces.": "选择要侦听的SSH接口。取消选中所有选项，SSH即可在所有接口上侦听。", "Select one or more screenshots that illustrate the problem.": "选择一个或多个说明问题的屏幕截图。", "Select permissions to apply to the chosen <i>Who</i>. Choices change depending on the <i>Permissions Type</i>.": "选择权限以应用于所选的<i>谁</i>。选项根据<i>权限类型</i>而变化。", "Select pool to import": "选择导入的池", "Select pool, dataset, or directory to share.": "选择要共享的池，数据集或目录。", "Select the <a href=\"https://man7.org/linux/man-pages/man3/syslog.3.html\" target=\"_blank\">syslog(3)</a> facility of the SFTP server.": "选择 SFTP 服务器的设施 <a href=\"https://man7.org/linux/man-pages/man3/syslog.3.html\" target=\"_blank\">syslog(3)</a>。", "Select the <a href=\"https://man7.org/linux/man-pages/man3/syslog.3.html\" target=\"_blank\">syslog(3)</a> level of the SFTP server.": "选择SFTP 服务器的级别 <a href=\"https://man7.org/linux/man-pages/man3/syslog.3.html\" target=\"_blank\">syslog(3)</a>。", "Select the Certificate Signing Request to sign the Certificate Authority with.": "选择证书签名请求以签署证书颁发机构。", "Select the Class of Service. The available 802.1p Class of Service ranges from <i>Best effort (default)</i>  to <i>Network control (highest)</i>.": "选择服务等级。可用的802.1p服务等级的范围从<i>尽量（默认）</i>到<i>网络控制（最高）</i>。", "Select the IP addresses to be listened on  by the portal. Click ADD to add IP addresses with a different network  port. The address <i>0.0.0.0</i> can be selected to listen on all IPv4  addresses, or <i>::</i> to listen on all IPv6 addresses.": "选择门户网站要侦听的IP地址。单击“添加”以使用其他网络端口添加IP地址。 可以选择地址<i> 0.0.0.0 </ i>侦听所有IPv4地址，或者选择<i> :: </ i>侦听所有IPv6地址。", "Select the VLAN Parent Interface. Usually an Ethernet card connected to a switch port configured for the VLAN. New link aggregations are not available until the system is restarted.": "选择VLAN父接口。通常，以太网卡连接到为VLAN配置的交换机端口。在重新启动系统之前，新的链路聚合不可用。", "Select the account to use for TFTP requests. This account must have permission to the <b>Directory</b>.": "选择用于TFTP请求的帐户。此帐户必须具有对<b>目录</b>的许可。", "Select the appropriate environment.": "选择适当的环境。", "Select the appropriate level of criticality.": "选择适当的关键程度。", "Select the certificate of the Active Directory server if SSL connections are used. When no certificates are available, move to the Active Directory server and create a Certificate Authority and Certificate. Import the certificate to this system using the System/Certificates menu.": "如果使用SSL连接，则选择活动目录服务器的证书。如果没有可用的证书，请移至活动目录服务器并创建证书颁发机构和证书。使用“系统/证书”菜单将证书导入到该系统。", "Select the cloud storage provider credentials from the list of available Cloud Credentials.": "从可用的云凭据列表中选择云存储提供商凭据。", "Select the country of the organization.": "选择组织的国家。", "Select the days to run resilver tasks.": "选择运行重新同步任务的日期。", "Select the device to attach.": "选择要连接的设备。", "Select the directories or files to be sent to the cloud for Push syncs, or the destination to be written for Pull syncs. Be cautious about the destination of Pull jobs to avoid overwriting existing files.": "选择要发送到云以进行推同步的目录或文件，或选择要写给拉同步的目录或文件。注意“拉”作业的目的地，以避免覆盖现有文件。", "Select the disk to import. The import will copy the data from the chosen disk to an existing ZFS dataset. Only one disk can be imported at a time.": "选择要导入的磁盘。导入会将数据从所选磁盘复制到现有ZFS数据集。一次只能导入一个磁盘。", "Select the disks to monitor.": "选择要监视的磁盘。", "Select the group to control the dataset. Groups created manually or imported from a directory service appear in the drop-down menu.": "选择组以控制数据集。手动创建或从目录服务导入的组显示在下拉菜单中。", "Select the interfaces to use in the aggregation.<br>  Warning: Link Aggregation creation fails if any of the selected interfaces have been manually configured.<br>The order is important because the FAILOVER lagg protocol will mark the first interface as the \"primary\" interface.": "选择要在聚合中使用的接口。<br>警告：如果已手动配置任何选定的接口，则链路聚合创建将失败。<br>顺序很重要，因为 FAILOVER lagg 协议会将第一个接口标记为 \"主要\" 接口。", "Select the interfaces to use in the aggregation.<br> Warning: Link Aggregation creation fails if any of the selected interfaces have been manually configured.": "选择要在聚合中使用的接口。<br>警告：如果已手动配置任何选定的接口，则链路聚合创建将失败。", "Select the level of severity.": "选择严重性级别。", "Select the locale for the MSDOSFS device to see files of that locale properly": "选择MSDOSFS设备的locale设置以正确查看该区域设置的文件", "Select the location of the principal in the  keytab created in <b>Directory Services > Kerberos Keytabs</b>.": "在<b>目录服务>Kerberos密钥表</b>中创建的密钥表中选择主体的位置。", "Select the physical interface to associate with the VM.": "选择要与虚拟机关联的物理接口。", "Select the pre-defined S3 bucket to use.": "选择要使用的预定义S3存储桶。", "Select the pre-defined container to use.": "选择要使用的预定义容器。", "Select the script.     The script will be run using     <a href=\"https://man7.org/linux/man-pages/man1/sh.1p.html\"     target=\"_blank\">sh(1)</a>.": "选择脚本。该脚本将使用 <a href=\"https://man7.org/linux/man-pages/man1/sh.1p.html\" target=\"_blank\">sh(1)</a> 运行。", "Select the serial port address in hex.": "选择十六进制的串行端口地址。", "Select the set of ACE inheritance <i>Flags</i> to display. <i>Basic</i> shows nonspecific inheritance options. <i>Advanced</i> shows specific inheritance settings for finer control.": "选择要显示的ACE继承集<i> Flags </i>。<i>基础</i>显示非特定的继承选项。<i>高级</i>显示用于更好控制的特定继承设置。", "Select the shell to use for local and SSH logins.": "选择用于本地和SSH登录的shell。", "Select the system services that will be allowed to communicate externally.": "选择允许与外部通信的系统服务。", "Select the type of LDAP server to use. This can be the        LDAP server provided by the Active Directory server or a stand-alone LDAP server.": "选择要使用的LDAP服务器的类型。这可以是活动目录服务器提供的LDAP服务器，也可以是独立的LDAP服务器。", "Select the unused zvol or zvol snapshot. Select <i>Create New</i> to create a new zvol.": "选择未使用的zvol或zvol快照。选择<i>新建</i>创建一个新的zvol。", "Select the user to control the dataset. Users created manually or imported from a directory service appear in the drop-down menu.": "选择用户来控制数据集。手动创建或从目录服务导入的用户将显示在下拉菜单中。", "Select the user to run the rsync task. The user                selected must have permissions to write to the                specified directory on the remote host.": "选择要运行rsync任务的用户。所选用户必须具有写入远程主机上指定目录的权限。", "Select the value or enter a value between <i>0</i> and <i>1023</i>. Some initiators expect a value below <i>256</i>. Leave this field blank to automatically assign the next available ID.": "选择该值或输入一个介于<i> 0 </i>和<i> 1023 </i>之间的值。一些启动器更希望其值小于<i> 256 </i>。将此字段留空以自动分配下一个可用的ID。", "Select when the command or script runs:<br>     <i>Pre Init</i> is early in the boot process, after mounting     filesystems and starting networking.<br> <i>Post Init</i> is at the     end of the boot process, before TrueNAS services start.<br>     <i>Shutdown</i> is during the system power off process.": "选择命令或脚本运行的时间：<br> <i>Pre Init</i> 处于引导过程的早期，在挂载文件系统和启动网络之后。<br> <i>Post Init</i> 处于最后 在 TrueNAS 服务启动之前启动过程。<br> <i>Shutdown</i> 是在系统关机过程中。", "Select which existing initiator group has access to the target.": "选择哪个现有启动程序组可以访问目标。", "Select {this} to include all {these} addresses. When this has been chosen, additional addresses cannot be selected.": "选择 {this} 以包含所有 {these} 地址。选择此项后，无法选择其他地址。", "Selecting a dataset to use with Minio <strong>removes all existing permissions for the dataset and any nested directories!</strong> Permissions are reset to minio:minio. To avoid dataset permissions conflicts, please create a separate, dedicated dataset for Minio.": "选择要与Minio一起使用的数据集<strong>删除该数据集和所有嵌套目录的所有现有权限！</strong>权限被重置为minio:minio。为避免数据集权限冲突，请为Minio创建一个单独的专用数据集。", "Self Healed": "自我修复", "Self-Encrypting Drive": "自加密驱动器", "Self-Encrypting Drive (SED) passwords can be managed with KMIP. Enabling this option allows the key server to manage creating or updating the global SED password, creating or updating individual SED passwords, and retrieving SED passwords when SEDs are unlocked. Disabling this option leaves SED password management with the local system.": "可以使用KMIP管理自加密驱动器（SED）密码。启用此选项后，密钥服务器可以在未锁定SED的情况下管理创建或更新全局SED密码，创建或更新单个SED密码以及检索SED密码。禁用此选项将使SED密码管理留在本地系统上。", "Semi-automatic (TrueNAS only)": "半自动（仅限 TrueNAS）", "Send Email Status Updates": "状态更新时发送邮件", "Send Mail Method": "发送邮件方法", "Send Test Alert": "发送测试警报", "Send Test Mail": "发送测试邮件", "Send failed HTTP request data which can include client and server IP addresses, failed method call tracebacks, and middleware log file contents to iXsystems.": "发送失败的HTTP请求数据，其中包括客户端和服务器IP地址，失败的方法调用回溯以及中间件日志文件内容到iXsystems。", "Send initial debug": "发送初始调试", "Sent": "发送", "Sep": "九月", "Separate multiple values by pressing <code>Enter</code>.": "通过按<code> Enter </code>分隔多个值。", "Separate values with commas, and without spaces.": "用逗号分隔值，且不包含空格。", "Serial": "序列号", "Serial Number": "序列号", "Serial Port": "串行端口", "Serial Shell": "串口 Shell", "Serial Speed": "串行速度", "Serial number for this disk.": "该磁盘的序列号。", "Serial numbers of each disk being edited.": "正在编辑的每个磁盘的序列号。", "Serial or USB port connected to the UPS. To  automatically detect and manage the USB port settings, select  <i>auto</i>.<br><br> When an SNMP driver is selected, enter the IP  address or hostname of the SNMP UPS device.": "串行或USB端口连接到UPS。要自动检测和管理USB端口设置，请选择<i>自动</ i>。<br> <br>选择SNMP驱动程序后，输入SNMP UPS设备的IP地址或主机名。", "Series": "系列", "Serve UDP NFS clients": "服务UDP NFS客户端", "Server": "服务器", "Server Certificate": "服务器证书", "Server Side Encryption": "服务器端加密", "Service": "服务", "Service Account Key": "服务帐号密钥", "Service Announcement": "服务公告", "Service Announcement:": "服务公告：", "Service CIDR": "服务CIDR", "Service Key": "服务密钥", "Service Status": "服务状态", "Service failed to start": "服务启动失败", "Service failed to stop": "服务停止失败", "Services": "服务", "Session": "会期", "Set": "组/集", "Set ACL": "设置ACL", "Set ACL for this dataset": "为此数据集设置ACL", "Set Frequency": "频率设定", "Set Keep Flag": "设置保留标志", "Set Quotas": "设置配额", "Set Warning Level": "设定警告等级", "Set enable sending messages to the address defined in the <b>Email</b> field.": "启用发送信息到在<b>电子邮件</b>栏中指定的地址。", "Set font size": "设置字体大小", "Set for the LDAP server to disable authentication and allow read and write access to any client.": "设置LDAP服务器以禁用身份验证，并允许对任何客户端进行读写访问。", "Set for the UPS to power off after shutting down the system.": "设置为关闭系统后，UPS关闭电源。", "Set for the default configuration to listen on all interfaces using the known values of user: <i>upsmon</i> and password: <i>fixmepass</i>.": "设置为默认配置，以使用用户<i> upsmon </i>和密码：<i> fixmepass </i>的已知值在所有接口上进行侦听。", "Set if NFS clients need to use UDP.": "设置NFS客户端是否需要使用UDP。", "Set if the client cannot connect, and it is suspected the client is poorly handling the server certificate request.": "如果客户端无法连接，并且怀疑客户端无法很好地处理服务器证书请求，则设置该属性。", "Set if the initiator does not support physical block size values over 4K (MS SQL).": "如果启动器不支持超过4K的物理块大小值（MS SQL），则设置。", "Set new root account password:": "设置新的root账户密码：", "Set only if required by the NFS client. Set to allow serving non-root mount requests.": "仅在NFS客户端要求时设置。设置为允许服务非root安装请求。", "Set or change the password of this SED.  This password is used instead of the global SED password.": "设置或更改此SED的密码。使用此密码代替全局SED密码。", "Set production status as active": "将生产状态设置为活动", "Set specific times to snapshot the  <i>Source Datasets</i> and replicate the snapshots to the  <i>Destination Dataset</i>. Select a preset schedule or choose  <i>Custom</i> to use the advanced scheduler.": "设置特定时间以对<i>源数据集</i>进行快照，并将快照复制到<i>目标数据集</i>。选择一个预设计划，或选择<i>自定义</i>以使用高级计划程序。", "Set the maximum number of connections per IP address. <i>0</i> means unlimited.": "设置每个IP地址最大并发连接数。<i>0</i>表示无限制。", "Set the number of data copies on this dataset.": "设置此数据集上的数据副本数。", "Set the port the FTP service listens on.": "设置FTP服务监听的端口。", "Set the read, write, and execute permissions for the dataset.": "设置数据集的读取，写入和执行权限。", "Set the root directory for anonymous FTP connections.": "设置匿名FTP连接的根目录。", "Set the share name. If left empty, share name is the list of selected <b>Path</b> entries.": "设置共享名称。如果保留为空，则共享名是所选<b> Path </b>条目的列表。", "Set this replication on a schedule or  just once.": "按计划或仅一次设置此复制。", "Set to allow FTP clients to resume interrupted transfers.": "设置为允许FTP客户端恢复中断的传输。", "Set to allow an initiator to bypass normal access control and access any scannable target. This allows <a href=\"https://docs.microsoft.com/en-us/previous-versions/windows/it-pro/windows-server-2012-R2-and-2012/cc771254(v=ws.11)\" target=\"_blank\">xcopy</a> operations which are otherwise blocked by access control.": "设置为允许启动程序绕过常规访问控制并访问任何可扫描目标。这允许<a href=\"https://docs.microsoft.com/en-us/previous-versions/windows/it-pro/windows-server-2012-R2-and-2012/cc771254(v=ws.11)\" target=\"_blank\"> xcopy </a>操作，否则这些操作将被访问控制阻止。", "Set to allow group to be used for Samba permissions and authentication.": "设置为允许组用于Samba权限和身份验证。", "Set to allow the client to mount any subdirectory within the <b>Path</b>.": "设置为允许客户端安装<b> Path </b>中的任何子目录。", "Set to allow user to authenticate to Samba shares.": "设置为允许用户验证Samba共享。", "Set to allow users to bypass firewall restrictions using the SSH port <a href=\"https://www.symantec.com/connect/articles/ssh-port-forwarding\" target=\"_blank\">forwarding feature</a>.": "设置为允许用户使用SSH端口<a href=\"https://www.symantec.com/connect/articles/ssh-port-forwarding\" target=\"_blank\">转发功能</a>绕过防火墙限制。", "Set to also replicate all snapshots contained  within the selected source dataset snapshots. Unset to only replicate  the selected dataset snapshots.": "设置为复制所选源数据集快照中包含的所有快照。未设置为仅复制所选数据集快照。", "Set to attempt to reduce latency over slow networks.": "设置为尝试减少慢速网络上的延迟。", "Set to automatically configure the IPv6. Only one interface can be configured this way.": "设置为自动配置IPv6，这样只能配置一个接口。", "Set to automatically create the defined <b>Remote                Path</b> if it does not exist.": "设置为自动创建已定义的<b>远程路径</b>（如果不存在）。", "Set to boot a debug kernel after the next system  reboot.": "设置为在下次系统重新引导后引导调试内核。", "Set to create a new primary group with the same name as the user. Unset to select an existing group for the user.": "设置为创建一个与用户同名的新主组。取消选择来为用户选择一个现有组。", "Set to determine if the system participates in a browser election. Leave unset when the network contains an AD or LDAP server, or when Vista or Windows 7 machines are present.": "设置以确定系统是否参与浏览器选举。当网络包含AD或LDAP服务器，或者存在Vista或Windows 7计算机时，请保持未设置状态。", "Set to disable caching AD users and groups. This can help when unable to bind to a domain with a large number of users or groups.": "设置为禁用缓存AD用户和组。当无法绑定到具有大量用户或组的域时，这可以提供帮助。", "Set to display image upload options.": "设置为显示镜像上传选项。", "Set to either start this replication task  immediately after the linked periodic snapshot task completes or  continue to create a separate <b>Schedule</b> for this replication.": "设置为在链接的定期快照任务完成后立即启动此复制任务，或者继续为此复制创建单独的<b>计划</b>。", "Set to enable DHCP. Leave unset to create a static IPv4 or IPv6 configuration. Only one interface can be configured for DHCP.": "设置来启用DHCP。保持未设置来创建一个静态IPv4或IPv6配置。只有一个网络接口可以配置用于 DHCP。", "Set to enable Samba to do DNS updates when joining a domain.": "设置为在加入域时启用Samba进行DNS更新。", "Set to enable connecting to the VNC web interface.": "设置为启用连接到VNC Web界面。", "Set to enable the File eXchange Protocol. This option makes the server vulnerable to FTP bounce attacks so it is not recommended.": "设置为启用文件更改协议。此选项使服务器容易受到 FTP 反弹攻击，因此不建议这样做。", "Set to enable the iSCSI extent.": "设置为启用iSCSI区块。", "Set to enable the web user interface for the S3 service.  Access the minio web interface by entering the IP address and port number  separated by a colon in the browser address bar.": "设置为启用S3服务的Web用户界面。通过在浏览器地址栏中输入用冒号分隔的IP地址和端口号来访问minio Web界面。", "Set to export the certificate environment variables.": "设置为导出证书环境变量。", "Set to force NFS shares to fail if the Kerberos ticket is unavailable.": "如果Kerberos票证不可用，则设置为强制NFS共享失败。", "Set to generate and attach to the new issue a report containing an overview of the system hardware, build string, and configuration. This can take several minutes.": "设置为生成报告并将其附加到新发行版中，其中包含系统硬件，构建字符串和配置的概述。这可能需要几分钟。", "Set to ignore mapping requests for the <i>BUILTIN</i>  domain.": "设置为忽略<i> BUILTIN </ i>域的映射请求。", "Set to ignore the Virtual  Machine status during the delete operation. Unset to prevent deleting  the Virtual Machine when it is still active or has an undefined state.": "设置为在删除操作期间忽略虚拟机状态。取消设置以防止在虚拟机仍处于活动状态或状态未定义时删除该虚拟机。", "Set to include all subdirectories of the specified                directory. When unset, only the specified directory                is included.": "设置为包括指定目录的所有子目录。取消设置时，仅包括指定的目录。", "Set to include child datasets of the  chosen dataset.": "设置为包括所选数据集的子数据集。", "Set to include the Fully-Qualified Domain Name (FQDN) in logs to precisely identify systems with similar hostnames.": "设置为在日志中包含全称域名（FQDN），以精确识别具有相似主机名的系统。", "Set to inhibit some syslog diagnostics to avoid error messages. See <a href=\"https://man7.org/linux/man-pages/man5/exports.5.html\" target=\"_blank\">exports(5)</a> for examples.": "设置为禁止某些系统日志诊断以避免错误消息。 有关示例，请参见 <a href=\"https://man7.org/linux/man-pages/man5/exports.5.html\" target=\"_blank\">exports(5)</a>。", "Set to log <a href=\"https://man7.org/linux/man-pages/man8/mountd.8.html\" target=\"_blank\">mountd(8)</a> syslog requests.": "设置为记录 <a href=\"https://man7.org/linux/man-pages/man8/mountd.8.html\" target=\"_blank\">mountd(8)</a> 系统日志请求 .", "Set to log <a href=\"https://man7.org/linux/man-pages/man8/statd.8.html\" target=\"_blank\">rpc.statd(8)</a> and <a href=\"https://linux.die.net/man/8/rpc.lockd\" target=\"_blank\">rpc.lockd(8)</a> syslog requests.": "设置为记录 <a href=\"https://man7.org/linux/man-pages/man8/statd.8.html\" target=\"_blank\">rpc.statd(8)</a> 和 <a href=\"https://linux.die.net/man/8/rpc.lockd\" target=\"_blank\">rpc.lockd(8)</a> 系统日志请求。", "Set to log attempts to join the domain to /var/log/messages.": "设置为记录尝试将域加入/ var / log / messages。", "Set to log authentication failures in <i>/var/log/messages</i> instead of the default of <i>/var/log/samba4/log.smbd</i>.": "设置为在<i>/ var / log / messages中</i>记录身份验证失败，而不是默认的<i>/var/log/samba4/log.smbd</i> 。", "Set to make the module <i>read-only</i>. No new ranges  are allocated or new mappings created in the idmap pool.": "设置为将模块设置为<i>只读</ i>。没有分配新的范围，也没有在idmap池中创建新的映射。", "Set to override safety checks and add the disk to the pool. <br>WARNING: any data stored on the disk will be erased!": "设置为覆盖安全检查并将磁盘添加到池中。<br>警告：磁盘上存储的所有数据都将被清除！", "Set to perform reverse DNS lookups on client IPs. This can cause long delays if reverse DNS is not configured.": "设置为在客户端IP上执行反向DNS查找。如果未配置反向DNS，则可能导致较长的延迟。", "Set to preserve modification times of files.": "设置为保留文件的修改时间。", "Set to preserve original file permissions. This is                useful when the user is set to <i>root</i>.": "设置为保留原始文件权限。当用户设置为<i> root </i>时，这很有用。", "Set to prevent the dataset from being modified.": "设置为防止数据集被修改。", "Set to prevent the initiator from initializing this LUN.": "设置为防止启动程序初始化此LUN。", "Set to prevent the zvol from being modified.": "设置以防止修改zvol。", "Set to prohibit users from writing to this share.": "设置为禁止用户写入此共享。", "Set to prohibit writing to the share.": "设置为禁止写入共享。", "Set to query the cn instead of uid attribute for the user name in LDAP.": "设置为查询cn而不是uid属性来查询LDAP中的用户名。", "Set to reduce the size of data to transmit.                Recommended for slow connections.": "设置以减小要传输的数据大小。建议用于慢速连接。", "Set to remove all ACLs from the current dataset. ACLs are also recursively stripped from directories and child datasets when those options are set.": "设置为从当前数据集中删除所有ACL。设置了这些选项后，ACL也会从目录和子数据集中递归剥离。", "Set to remove the data associated with this  Virtual Machine (which will result in data loss if the data is not backed up). Unset to  leave the data intact.": "设置为删除与此虚拟机关联的数据（如果不备份数据，将导致数据丢失）。未设置保留数据不变。", "Set to restrict SSH access in certain circumstances to only members of  BUILTIN\\Administrators": "设置为在某些情况下仅对BUILTIN\\\\Administrators成员限制SSH访问", "Set to run resilver tasks between the configured times.": "设置为在配置的时间之间运行重新同步任务。", "Set to save the temporary file from each updated                file to a holding directory until the end of the                transfer when all transferred files are renamed                into place.": "设置为将所有传输文件重命名到位时，将每个更新文件中的临时文件保存到保留目录，直到传输结束。", "Set to start this VM when the system boots.": "设置为在系统启动时启动此虚拟机。", "Set to store the encryption key in the TrueNAS database.": "设置加密密钥存储在TrueNAS数据库中。", "Set to suppress informational messages from the                remote server.": "设置为禁止来自远程服务器的信息性消息。", "Set to take a snapshot of the dataset before a <i>PUSH</i>.": "设置为在<i> PUSH </i>之前创建数据集的快照。", "Set to take separate snapshots of the  dataset and each of its child datasets. Leave unset to take a  single snapshot only of the specified dataset <i>without</i> child  datasets.": "设置为创建数据集及其每个子数据集的单独快照。保留未设置状态可仅对<i>没有</i>子数据集的指定数据集创建单个快照。", "Set to to enable support for <a href=\"https://tools.ietf.org/html/rfc3410\" target=\"_blank\">SNMP version 3</a>. See <a href=\"http://net-snmp.sourceforge.net/docs/man/snmpd.conf.html\" target=\"_blank\">snmpd.conf(5)</a> for configuration details.": "设置为启用对<a href=\"https://tools.ietf.org/html/rfc3410\" target=\"_blank\">SNMP版本3</a>的支持。有关配置详细信息，请参见<a href=\"http://net-snmp.sourceforge.net/docs/man/snmpd.conf.html\" target=\"_blank\">snmpd.conf(5)</a>。", "Set to use encryption when replicating data. Additional encryption options will appear.": "复制数据时设置为使用加密。 将出现其他加密选项。", "Set to use the <i>Schedule</i> in place  of the <i>Replicate Specific Snapshots</i> time frame. The Schedule values are  read over the <i>Replicate Specific Snapshots</i> time frame.": "设置使用<i>计划</i>代替<i>复制特定快照</i>时间范围。在<i>复制特定快照</i>时间范围内读取计划值。", "Set when NFSv4 ACL support is needed without requiring the client and the server to sync users and groups.": "在需要NFSv4 ACL支持时设置，无需客户端和服务器同步用户和组。", "Set when a user is a member of more than 16 groups. This assumes group membership is configured correctly on the NFS server.": "当用户是超过16个组的成员时设置。这假定在NFS服务器上正确配置了组成员身份。", "Set when network devices need to send files to the system.": "设置网络设备何时需要将文件发送到系统。", "Set when using Xen as the iSCSI initiator.": "使用Xen作为iSCSI启动器时设置。", "Set whether processes can be executed from within this dataset.": "设置是否可以在此数据集中执行流程。", "Sets default Unix permissions of the user home directory. This is read-only for built-in users.": "设置用户主目录的默认Unix权限。这对于内置用户是只读的。", "Sets default permissions for newly created directories.": "为新创建的目录设置默认权限。", "Sets default permissions for newly created files.": "为新创建的文件设置默认权限。", "Sets the data write synchronization. <i>Inherit</i> takes the sync settings from the parent dataset, <i>Standard</i> uses the settings that have been requested by the client software, <i>Always</i> waits for data writes to complete, and <i>Disabled</i> never waits for writes to complete.": "设置数据写入同步。<i>继承</ i>从父数据集中获取同步设置，<i>标准</ i>使用客户端软件已请求的设置，<i>始终</ i>等待数据写入完成，而<i> 禁用</ i>从不等待写入完成。", "Setting default permissions will reset the permissions of this share and any others within its path.": "设置默认权限将重置此共享及其路径中任何其他共享的权限。", "Setting permissions recursively affects this directory and any others below it. This can make data inaccessible.": "递归设置权限会影响此目录及其下的任何其他目录。这会使数据无法访问。", "Setting permissions recursively will affect this directory and any others below it. This might make data inaccessible.": "递归设置权限将影响此目录及其下的任何其他目录。这可能使数据不可访问。", "Setting this option changes the destination dataset to be read-only. To continue using the default or existing dataset read permissions, leave this option unset.": "设置此选项会将目标数据集更改为只读。要继续使用默认或现有的数据集读取权限，请取消设置此选项。", "Setting this option is <b>not</b> recommended as it breaks several security measures. Refer to <a href=\"http://www.proftpd.org/docs/contrib/mod_tls.html\" target=\"_blank\">mod_tls</a> for more details.": "设置此选项<b>不建议</b>，因为它会破坏一些安全措施。有关更多详细信息，请参见<a href=\"http://www.proftpd.org/docs/contrib/mod_tls.html\" target=\"_blank\">mod_tls</a>。", "Setting this option is discouraged as it increases security risk.": "不建议设置此选项，因为它会增加安全风险。", "Setting this option reduces the security of the connection, so only use it if the client does not understand reused SSL sessions.": "设置此选项会降低连接的安全性，因此仅在客户端不了解重用的SSL会话时才使用它。", "Setting this option will result in timeouts if <b>identd</b> is not running on the client.": "如果<b>identd</b>未在客户端上运行，则设置此选项将导致超时。", "Setting up LDAP": "设置 LDAP", "Settings": "设置", "Settings Requiring Re-Initialization": "需要重新初始化的设置", "Settings saved.": "设置已保存。", "Setup Method": "设置方法", "Several providers are supported. If a specific  provider is not listed, select <i>Custom Provider</i> and enter the  information in the <i>Custom Server</i> and <i>Custom Path</i> fields.": "支持多个提供商。如果未列出特定的提供商，请选择<i>自定义提供商</ i>，然后在<i>自定义服务器</i>和<i>自定义路径</i>字段中输入信息。", "Share ACL for {share}": "为 {share} 共享 ACL", "Share Attached": "分享附件", "Share Path updated": "共享路径已更新", "Share Type": "共享类型", "Shares": "共享", "Sharing": "分享中", "Sharing Platform": "共享平台", "Shell": "命令行", "Shell Commands": "Shell 命令", "Short": "简短", "Short Description": "简述", "Should only be used for highly accurate <b>NTP</b> servers such as those with time monitoring hardware.": "仅应用于高度准确的<b>NTP</b>服务器，例如具有时间监控硬件的服务器。", "Show": "显示", "Show All Groups": "显示所有组", "Show All Users": "显示所有用户", "Show Built-In Groups": "显示内置组", "Show Built-in Users": "显示内置用户", "Show Console Messages": "显示控制台消息", "Show Extra Columns": "显示多余的列", "Show QR": "显示二维码", "Show Text Console without Password Prompt": "显示没有密码提示的文本控制台", "Show all available groups, including those that do not have quotas set.": "显示所有可用组，包括未设置配额的组。", "Show all available users, including those that do not have quotas set.": "显示所有可用用户，包括未设置配额的用户。", "Show disks with non-unique serial numbers": "显示具有非唯一序列号的磁盘", "Show extra columns": "显示额外的列", "Show only those users who have quotas. This is the default view.": "仅显示具有配额的用户。这是默认视图。", "Showing extra columns in the table is useful for data filtering, but can cause performance issues.": "在表中显示额外的列对于数据过滤很有用，但可能会导致性能问题。", "Shows only the groups that have quotas. This is the default view.": "仅显示具有配额的组。这是默认视图。", "Shrinking a ZVOL is not allowed in the User Interface. This can lead to data loss.": "用户界面中不允许收缩ZVOL。这可能导致数据丢失。", "Shut Down": "关闭", "Shut down": "关闭", "Shut down the system?": "关闭系统吗？", "Shutdown": "关机", "Shutdown Command": "关机命令", "Shutdown Mode": "关机模式", "Shutdown Timeout": "关机超时", "Shutdown Timer": "关机倒计时", "Sign": "注册", "Sign CSR": "签发CSR", "Signed By": "被...签名", "Signed Certificates": "签名证书", "Signing Certificate Authority": "签名证书颁发机构", "Signing Request": "签名请求", "Signup": "注册", "Signup for account": "注册帐户", "Silver / Gold Coverage Customers can enable iXsystems Proactive Support. This  automatically emails iXsystems when certain conditions occur on this TrueNAS system. The iX Support  Team will promptly communicate with the Contacts saved below to quickly resolve any issue that may  have occurred on the system.": "银牌/金牌客户可以启用iXsystems主动支持。当此TrueNAS系统上发生某些情况时，它将自动通过电子邮件发送给iXsystem。iX支持团队将立即与下面保存的联系人联系，以快速解决系统上可能发生的任何问题。", "Site Name": "站点名称", "Size": "大小", "Size for this zvol": "此zvol的大小", "Skip automatic detection of the Endpoint URL region. Set this  when configuring a custom Endpoint URL.": "跳过对端点URL区域的自动检测。在配置自定义端点URL时进行设置。", "Sleep": "睡眠", "Slot": "插槽", "Snapshot": "快照", "Snapshot Lifetime": "快照保留周期", "Snapshot Name Regular Expression": "快照名称正则表达式", "Snapshot Retention Policy": "快照保留策略", "Snapshot Tasks": "快照任务", "Snapshot added successfully.": "快照添加成功。", "Snapshot directory": "快照目录", "Snapshot name format string. The default  is <code>auto-&percnt;Y-&percnt;m-&percnt;d_&percnt;H-&percnt;M</code>. Must include the strings <i>&percnt;Y</i>,  <i>&percnt;m</i>, <i>&percnt;d</i>, <i>&percnt;H</i>, and <i>&percnt;M</i>, which are replaced with  the four-digit year, month, day of month, hour, and minute as defined  in <a href=\"https://man7.org/linux/man-pages/man3/strftime.3.html\" target=\"_blank\">strftime(3)</a>.<br><br>  For example, snapshots of <i>pool1</i> with a Naming Schema of  <i>customsnap-&percnt;Y&percnt;m&percnt;d.&percnt;H&percnt;M</i> have names like  <i>pool1@customsnap-20190315.0527</i>.": "快照名称格式字符串。 默认为 <code>auto-&percnt;Y-&percnt;m-&percnt;d_&percnt;H-&percnt;M</code>。 必须包含字符串 <i>&percnt;Y</i>、<i>&percnt;m</i>、<i>&percnt;d</i>、<i>&percnt;H</i> 和 < i>&percnt;M</i>，替换为 <a href=\"https://man7.org/linux/man 中定义的四位数年、月、月日、小时和分钟 -pages/man3/strftime.3.html\" target=\"_blank\">strftime(3)</a>。<br><br> 例如，<i>pool1</i> 的快照带有 <i>customsnap-&percnt;Y&percnt;m&percnt;d.&percnt;H&percnt;M</i> 的命名架构具有类似 <i>pool1@customsnap-20190315.0527</i> 的名称。", "Snapshot schedule for this  replication task. Choose from previously configured  <b>Periodic Snapshot Tasks</b>. This replication task must have the  same <b>Recursive</b> and <b>Exclude Child Datasets</b> values  as the chosen periodic snapshot task. Selecting a periodic snapshot  schedule removes the <b>Schedule</b> field.": "此复制任务的快照计划。从以前配置的<b>定期快照任务</b>中选择。此复制任务必须具有与所选定期快照任务相同的<b>递归</b>和<b>排除子数据集</b>值。选择一个定期快照计划会删除<b>计划</b>字段。", "Snapshots": "快照", "Snapshots could not be loaded": "无法加载快照", "Some authentication tools default to 30 seconds and do not support custom intervals.": "某些身份验证工具默认为30秒，并且不支持自定义间隔。", "Something went wrong": "出问题了", "Source": "源", "Source Dataset": "源数据集", "Source Location": "源位置", "Space": "空间", "Space Available to Dataset": "数据集可用空间", "Space Available to Zvol": "Zvol 可用空间", "Space-delimited list of allowed IP addresses <i>(************)</i> or hostnames <i>(www.freenas.com)</i>. Leave empty to allow all.": "以空格分隔的允许IP地址<i>（************）</i>或主机名<i>（www.freenas.com）</i>的列表。留空以允许所有。", "Space-delimited list of allowed networks in network/mask CIDR notation. Example: <i>*******/24</i>. Leave empty to allow all.": "网络/掩码CIDR表示法中以空格分隔的允许网络列表。例如：<i> *******/24 </i>。留空以允许所有。", "Spaces are allowed.": "允许使用空格。", "Spare": "备用", "Spare VDEVs": "备用 VDEV", "Spare VDev": "备用VDev", "Spares": "备用", "Sparse": "备用", "Special Allocation class, used to create Fusion pools. Optional vdev type which is used to speed up metadata and small block IO.": "特殊分配类，用于创建融合池。可选的vdev类型，用于加速元数据和小块IO。", "Specifies the auxiliary directory service ID provider.": "指定辅助目录服务ID提供程序。", "Specify a port for encrypted connections. The default port <i>8081</i> is recommended. Do not reuse a port.": "为加密连接指定端口。推荐默认端口<i>8081</i>。不要重复一个端口。", "Specify a port for unencrypted connections. The default port <i>8080</i> is recommended. Do not reuse a port.": "为未加密连接指定端口。推荐默认端口<i>8080</i>。不要重复一个端口。", "Specify a size and value such as <i>10 GiB</i>.": "指定大小和值，例如<i>10 GiB</i> 。", "Specify the PCI device to pass thru (bus#/slot#/fcn#).": "Specify the PCI device to pass thru (bus\\#/slot\\#/fcn\\#).", "Specify the logical cores that VM is allowed to use. Better cache locality can be achieved by setting CPU set base on CPU topology. E.g. to assign cores: 0,1,2,5,9,10,11 you can write: 1-2,5,9-11": "指定允许 VM 使用的逻辑内核。 通过基于 CPU 拓扑设置 CPU 集可以实现更好的缓存局部性。 例如。 分配核心：0,1,2,5,9,10,11 你可以写：1-2,5,9-11", "Specify the message displayed to local login users after authentication. Not displayed to anonymous login users.": "指定身份验证后显示给本地登录用户的消息。对匿名登录用户不显示。", "Specify the number of cores per virtual CPU socket.": "指定每个虚拟CPU插槽的内核数。", "Specify the number of threads per core.": "指定每个内核的线程数。", "Specify the size of the new zvol.": "指定新zvol的大小。", "Specify this certificate's valid Key Usages. Web certificates           typically need at least Digital Signature and possibly Key Encipherment           or Key Agreement, while other applications may need other usages.": "指定此证书的有效密钥用法。 Web 证书通常至少需要数字签名，可能还需要密钥加密或密钥协议，而其他应用程序可能需要其他用途。", "Specify whether the issued certificate should include Authority Key Identifier information,          and whether the extension is critical. Critical extensions must be recognized by the client or be rejected.": "指定颁发的证书是否应包含授权密钥标识符信息，以及扩展是否至关重要。 关键扩展必须得到客户的认可或被拒绝。", "Specify whether to use the certificate for a Certificate Authority           and whether this extension is critical. Clients must recognize critical extensions           to prevent rejection. Web certificates typically require you to disable           CA and enable Critical Extension.": "指定是否将证书用于证书颁发机构以及此扩展是否至关重要。 客户必须识别关键扩展以防止拒绝。 Web 证书通常要求您禁用 CA 并启用关键扩展。", "Speeds up the initial synchronization (seconds instead of minutes).": "加速初始同步（秒而不是分钟）。", "Staging": "分期", "Standard": "标准", "Standby": "待机", "Standby {controller}.": "待机 {controller}。", "Start": "启动", "Start Automatically": "自启", "Start Scrub": "开始校验", "Start a dry run test of this cloud sync task? The  system will connect to the cloud service provider and simulate  transferring a file. No data will be sent or received.": "开始对此云同步任务进行试运行？ 系统将连接到云服务提供商，并模拟传输文件。没有数据将被发送或接收。", "Start on Boot": "开机启动", "Start scrub on pool <i>{poolName}</i>?": "开始校验池 <i>{poolName}</i>？", "Start the scrub now?": "立即开始校验吗？", "Start time for the replication task.": "复制任务的开始时间。", "Start {service} Service": "启动 {service} 服务", "Started": "已启动", "Starting": "正在启动", "State": "状态", "Statefulsets": "有状态集", "Static IP addresses which SMB listens on for connections.  Leaving all unselected defaults to listening on all active interfaces.": "SMB侦听连接的静态IP地址。将所有未选中的默认值保留为侦听所有活动接口。", "Static IPv4 address of the IPMI web interface.": "IPMI Web 界面的静态 IPv4 地址。", "Static Routes": "静态路由", "Stats": "状态", "Stats Per Thread": "每线程统计", "Stats/Settings": "统计/设置", "Status": "状态", "Status of TrueCommand": "TrueCommand的状态", "Status: ": "状态： ", "Step Back": "退后", "Step Forward": "向前一步", "Stop": "停止", "Stop Rollback if Snapshots Exist:": "如果存在快照，则停止回滚：", "Stop Scrub": "停止校验", "Stop TrueCommand Cloud Connection": "停止TrueCommand云连接", "Stop the scrub on {poolName}?": "停止校验池 {poolName}？", "Stop the {serviceName} service and close these connections?": "停止 {serviceName} 服务并关闭这些连接？", "Stop this cloud sync?": "停止此云同步吗？", "Stop {serviceName}?": "停止 {serviceName}？", "Stop {vmName}?": "停止 {vmName}？", "Stopped": "已停止", "Stopping": "正在停止", "Stopping {rowName}": "停止 {rowName} 中", "Stops the rollback when the safety  check finds any related clone snapshots that are newer than the  rollback snapshot.": "当安全检查找到比回滚快照新的克隆快照时，则停止回滚。", "Stops the rollback when the safety check  finds any related intermediate, child dataset, or clone snapshots that  are newer than the rollback snapshot.": "当安全检查找到比回滚快照新的intermediate，子数据集或克隆快照时，则停止回滚。", "Storage": "存储", "Storage Class": "存储类别", "Storage URL": "存储URL", "Storage URL - optional <a href=\"https://rclone.org/swift/#standard-options\" target=\"_blank\">(rclone documentation)</a>.": "存储URL-可选的<a href=\"https://rclone.org/swift/\\#standard-options\" target=\"_blank\">（rclone文档）</a>。", "Storage Widgets": "存储小部件", "Storage location for the original  snapshots that will be replicated.": "将要复制的原始快照的存储位置。", "Storage location for the replicated  snapshots.": "复制快照的存储位置。", "Store Encryption key in Sending TrueNAS database": "在发送TrueNAS数据库中存储加密密钥", "Store system logs on the system dataset. Unset to store system logs in <i>/var/</i> on the operating system device.": "将系统日志存储在系统数据集上。取消设置将系统日志存储在操作系统设备上的<i>/var/</i>中。", "Stream Compression": "流压缩", "Strip ACL": "剥离ACL", "Strip ACLs": "条带ACLs", "Stripe": "条带", "Stripping ACLs": "正在剥离ACL", "Stripping ACLs...": "正在剥离ACL...", "Subject": "主题", "Subject Alternate Names": "主题备用名称", "Subnet mask of the IPv4 address.": "IPv4地址的子网掩码。", "Success": "成功", "Success! The API key has been created or reset.": "成功！ API 密钥已创建或重置。", "Successfully expanded pool {name}.": "已成功扩展池 {name}。", "Successfully exported/disconnected {pool}.": "成功导出/断开连接 {pool} 。", "Successfully replaced disk {disk}.": "已成功更换磁盘 {disk}。", "Successfully saved IPMI settings.": "已成功保存 IPMI 设置。", "Successfully saved proactive support settings.": "成功保存了主动支持设置。", "Successfully saved {n, plural, one {Disk} other {Disks}} settings.": "成功保存 {n, plural, 1 {Disk} other {Disks}} 设置。", "Suggest Layout": "建议布局", "Suggestion": "建议", "Summary": "概括", "Sun": "周日", "Sunday": "周日", "Support": "支持", "Support >16 groups": "支持多于16个组", "Switch Train": "切换分支", "Switch to Wizard": "切换到向导", "Switch update trains?": "切换更新分支？", "Sync": "同步", "Sync From Peer": "从端点同步数据", "Sync Keys": "同步密钥", "Sync To Peer": "同步数据到端点", "Sync from Peer": "从相同的一方同步", "Sync from peer succeeded.": "从相同的一方同步成功。", "Sync to Peer": "同步到相同的一方", "Sync to peer succeeded.": "与相同的一方同步成功。", "Synced": "已同步", "Synchronize": "同步", "Sysctl": "系统控制", "Syslog": "系统日志", "Syslog Level": "系统日志级别", "Syslog Server": "系统日志服务器", "Syslog TLS Certificate": "系统日志TLS证书", "Syslog TLS Certificate Authority": "Syslog TLS 证书颁发机构", "Syslog Transport": "系统日志传输", "System": "系统", "System Clock": "系统时钟", "System Dataset": "系统数据集", "System Dataset Pool": "系统数据集池", "System Info": "系统信息", "System Information": "系统信息", "System Information (Standby)": "系统信息（待机）", "System Serial": "系统序列号", "System Settings": "系统设置", "System Time Zone:": "系统时区：", "System Widgets": "系统小部件", "System domain name, like <i>example.com</i>": "系统域名，例如<i>example.com</i>", "System hostname.": "系统主机名。", "System is failing over...": "系统正在故障转移...", "System is restarting...": "系统正在重启...", "System is shutting down...": "系统正在关闭...", "System-Generated Settings": "系统生成的设置", "TCP Port": "TCP端口", "TCP port used to access the iSCSI target. Default is <i>3260</i>.": "用于访问iSCSI目标的TCP端口。默认设置是 <i>3260</i>.", "TLS (STARTTLS)": "TLS (STARTTLS)", "TLS Allow Client Renegotiations": "TLS允许客户端重新协商", "TLS Allow Dot Login": "TLS允许点登录", "TLS Allow Per User": "TLS允许每个用户", "TLS Common Name Required": "需要TLS通用名", "TLS Crypt Auth": "TLS Crypt验证", "TLS Crypt Auth Enabled": "启用TLS Crypt验证", "TLS DNS Name Required": "需要TLS DNS名称", "TLS Enable Diagnostics": "启用TLS诊断", "TLS Export Certificate Data": "TLS导出证书数据", "TLS Export Standard Vars": "TLS导出标准Vars", "TLS IP Address Required": "需要TLS IP地址", "TLS No Certificate Request": "TLS无证书请求", "TLS No Empty Fragments": "TLS无空片段", "TLS No Session Reuse Required": "TLS无需会话重用", "TLS Policy": "TLS政策", "TLS Server Hostname": "TLS 服务器主机名", "Tag": "标签", "Tags": "标签", "Tail Lines": "Tail 行", "Take Snapshot": "创建快照", "Target": "目标", "Target Alias": "目标别名", "Target Dataset": "目标数据集", "Target Global Configuration": "目标全局配置", "Target Mode": "目标模式", "Target Name": "目标名称", "Targets": "目标", "Task Aborted": "任务中止", "Task Name": "任务名称", "Task Stopped": "任务已停止", "Task is on hold": "任务暂停", "Task is running": "任务正在运行", "Task started": "任务已开始", "Tasks could not be loaded": "无法加载任务", "Team Drive ID": "团队驱动器ID", "Telegram Bot API Token (<a href=\"https://core.telegram.org/bots#3-how-do-i-create-a-bot\" target=\"_blank\">How to create a Telegram Bot</a>)": "Telegram Bot API Token (<a href=\"https://core.telegram.org/bots\\#3-how-do-i-create-a-bot\" target=\"_blank\">How to create a Telegram Bot</a>)", "Temperature": "温度", "Temperature Alerts": "温度警报", "Temperature data missing.": "温度数据丢失。", "Tenant Domain": "租户域", "Tenant ID": "租户ID", "Tenant ID - optional for v1 auth, this or tenant required otherwise <a href=\"https://rclone.org/swift/#standard-options\" target=\"_blank\">(rclone documentation)</a>.": "租户ID-可选v1身份验证，否则需要此租户<a href=\"https://rclone.org/swift/\\#standard-options\" target=\"_blank\">（rclone文档）</a>。", "Tenant Name": "租户名称", "Tenant domain - optional <a href=\"https://rclone.org/swift/#standard-options\" target=\"_blank\">(rclone documentation)</a>.": "租户域-可选的<a href=\"https://rclone.org/swift/\\#standard-options\" target=\"_blank\">（rclone文档）</a>。", "Terms of Service": "服务条款", "Test Changes": "测试更改", "Test Cloud Sync": "测试云同步", "Test alert sent": "已发送测试警报", "Test email sent.": "已发送测试电子邮件。", "Test network interface changes for ": "测试网络接口更改", "Test network interface changes? Network connectivity can be interrupted.": "测试网络接口变化？网络连接可能会中断。", "Testing": "测试中", "Tests are only performed when <i>Never</i> is selected.": "仅在选择<i>从不</i>时才执行测试。", "Tests the server connection and verifies the chosen <b>Certificate</b>  chain. To test, configure the <i>Server</i> and <i>Port</i> values, select a  <i>Certificate</i> and <i>Certificate Authority</i>, enable this setting, and  click <b>SAVE</b>.": "测试服务器连接并验证选择的<b>证书</b>链。要进行测试，请配置<i>服务器</i>和<i>端口</i>值，选择<i>证书</i>和<i>证书颁发机构</i>，启用此设置，然后点击<b>保存</b>。", "The <a href=\"https://www.ixsystems.com/community/\" target=\"_blank\" class=\"external-link\">TrueNAS Community Forums</a> are the best place to ask questions and interact with fellow TrueNAS users.": "<a href=\"https://www.ixsystems.com/community/\" target=\"_blank\" class=\"external-link\">TrueNAS 社区论坛</a> 是最好的地方 提出问题并与其他 TrueNAS 用户互动。", "The <a href=\"https://www.truenas.com/docs/\" target=\"_blank\">TrueNAS Documentation Site</a> is a collaborative website with helpful guides and information about your new storage system.": "<a href=\"https://www.truenas.com/docs/\" target=\"_blank\">TrueNAS 文档站点</a> 是一个协作网站，其中包含有关您的新存储系统的有用指南和信息 .", "The <i><b>{name}</b></i> dataset and all snapshots stored with it <b>will be permanently deleted</b>.": "<i><b>{name}</b></i> 数据集及其存储的所有快照<b>将被永久删除</b>。", "The <i>Alias</i> field can either be left empty or  have an alias defined for each path in the share.": "<i>别名</i>字段可以留空，也可以为共享中的每个路径定义别名。", "The <i>Use Apple-style character encoding</i> value has changed.  This parameter affects how file names are read from and written to storage. Changes to  this parameter after data is written can prevent accessing or deleting files containing  mangled characters.": "<i>使用Apple风格的字符编码</i>值已更改。此参数影响文件名如何从存储读取和写入存储。写入数据后更改此参数可以防止访问或删除包含损坏字符的文件。", "The Group ID (GID) is a unique number used to identify a Unix group. Enter a number above 1000 for a group with user accounts. Groups used by a service must have an ID that matches the default port number used by the service.": "组ID（GID）是用于标识Unix组的唯一编号。为具有用户帐户的组输入大于1000的数字。服务使用的组必须具有与服务使用的默认端口号匹配的ID。", "The Idmap cache should be cleared after finalizing idmap changes.  Click \"Continue\" to clear the cache.": "完成idmap更改后，应清除idmap缓存。单击\"继续\" 以清除缓存。", "The OU in which new computer accounts are created.  The OU string is read from top to bottom without RDNs. Slashes (\"/\") are used as  delimiters, like <samp>Computers/Servers/NAS</samp>. The backslash (\"\\\") is  used to escape characters but not as a separator. Backslashes are interpreted at  multiple levels and might require doubling or even quadrupling to take effect.  When this field is blank, new computer accounts are created in the Active Directory  default OU.": "在其中创建新计算机帐户的OU。OU字符串是从上到下读取的，没有RDN。斜杠（\"/\"）用作分隔符，例如<samp>Computers/Servers/NAS</samp>。反斜杠（\"\\\\\"）用于转义字符，但不能用作分隔符。反斜杠在多个级别进行解释，可能需要加倍甚至四倍才能生效。如果此字段为空，则在活动目录默认OU中创建新的计算机帐户。", "The SMB service has been restarted.": "SMB服务已重新启动。", "The SSL certificate to be used for TLS FTP connections. To create a certificate, use <b>System --> Certificates</b>.": "用于TLS FTP连接的SSL证书。要创建证书，请使用<b>系统->证书</b>。", "The TrueNAS controllers do not have the same quantity of disks.": "TrueNAS 控制器没有相同数量的磁盘。", "The UDP port number that listens for TFTP requests. Example: 8050": "监听TFTP请求的UDP端口号。例子：8050", "The URI used to provision an OTP. The URI (which contains the secret) is encoded in a QR Code.  To set up an OTP app like Google Authenticator, use the app to scan the QR code or enter the secret manually  into the app. The URI is produced by the system when Two-Factor Authentication is first activated.": "用于提供OTP的URI。URI（包含秘密）以QR码编码。要设置Google身份验证器之类的OTP应用，请使用该应用扫描QR码或将密码手动输入到该应用中。首次激活双重身份验证时，系统会生成URI。", "The VM could not start because the current configuration could potentially require more RAM than is available on the system.  Would you like to overcommit memory? ": "虚拟机无法启动，因为当前配置可能需要用到比系统上可用的RAM更多的RAM。您想过量使用内存吗？", "The authority key identifier extension provides a means of  identifying the public key corresponding to the private key used to  sign a certificate. This extension is used where an issuer has  multiple signing keys (either due to multiple concurrent key pairs or  due to changeover). The identification MAY be based on either the  key identifier (the subject key identifier in the issuer's  certificate) or on the issuer name and serial number.<br>  See <a href=\"https://www.ietf.org/rfc/rfc3280.txt\">RFC 3280, section *******</a>  for more information.": "授权密钥标识符扩展提供了一种识别与用于签署证书的私钥相对应的公钥的方法。 此扩展用于发行人有多个签名密钥（由于多个并发密钥对或由于转换）。 标识可以基于密钥标识符（颁发者证书中的主题密钥标识符）或基于颁发者名称和序列号。<br>参见 <a href=\"https://www.ietf.org/rfc /rfc3280.txt\">RFC 3280，第 ******* 节</a> 了解更多信息。", "The base name is automatically prepended if the target name does not start with <i>iqn</i>. Lowercase alphanumeric characters plus dot (.), dash (-), and colon (:) are allowed. See the <i>Constructing iSCSI names using the iqn.format</i> section of <a href=\"https://tools.ietf.org/html/rfc3721.html\" target=\"_blank\">RFC3721</a>.": "如果目标名称不以<i>iqn</i>开头，则会自动添加基础名称。允许字母数字、“.”、“-”, 和“:”。查看 <a href=\"https://tools.ietf.org/html/rfc3721.html\" target=\"_blank\">RFC3721</a>的<i>Constructing iSCSI names using the iqn.format</i>章节。", "The basic constraints extension identifies whether the  subject of the certificate is a CA and the maximum depth of valid  certification paths that include this certificate. <br>  See <a href=\"https://www.ietf.org/rfc/rfc3280.txt\">RFC 3280, section *******0</a>  for more information.": "基本约束扩展标识证书的主题是否是CA以及包含此证书的有效证书路径的最大深度。 <br>有关详细信息，请参阅 <a href=\"https://www.ietf.org/rfc/rfc3280.txt\">RFC 3280，第 *******0 节</a>。", "The cache has been cleared.": "缓存已清除。", "The cache is being rebuilt.": "正在刷新缓存。", "The certificate's public key is used to encipher user data only during key agreement operations. Requires that <b>Key Agreement</b> is also set.": "证书的公用密钥仅在密钥协商操作期间才用于加密用户数据。要求还设置<b>密钥协议</ b>。", "The chosen preset ACL will <strong>REPLACE</strong> the ACL currently displayed in the form  and delete any unsaved changes.": "所选的预设ACL将<strong>替换</strong>表单中当前显示的ACL，并删除所有未保存的更改。", "The contents of all added disks will be erased.": "所有已添加磁盘的内容将被删除。", "The credentials are valid.": "凭据有效。", "The cryptographic algorithm to use. The default <i>SHA256</i> only needs to be changed if the organization requires a different algorithm.": "要使用的加密算法。仅当组织需要其他算法时才需要更改默认的<i> SHA256 </ i>。", "The current pool layout is not recommended. Override the following errors?": "不建议使用当前的池布局。覆盖以下错误？", "The default \"Checksum\" value for datasets with deduplication used to be SHA256.\n       Our testing has shown that SHA512 performs better for such datasets.\n       We've changed the checksum value from SHA256 to SHA512. You can change it back in \"Advanced Options\".": "具有重复数据删除功能的数据集的默认“校验和”值曾经是 SHA256。\n 我们的测试表明 SHA512 对此类数据集的性能更好。\n 我们已将校验和值从 SHA256 更改为 SHA512。 您可以在“高级选项”中将其更改回来。", "The default host to use for TFTP transfers. Enter an IP address. Example: *********": "TFTP传输使用的默认主机。输入一个IP地址。例：*********", "The default of <i>davtest</i> is recommended to change. <i>davtest</i> is a known value.": "建议更改默认值<i> davtest </i>。<i> davtest </i>是一个已知值。", "The directory base suffix to use for SID/uid/gid mapping entries. Example: dc=test,dc=org. When undefined, idmap_ldap defaults to using the ldap idmap suffix option from <a href=\"https://www.samba.org/samba/docs/current/man-html/smb.conf.5.html\" target=\"_blank\">smb.conf</a>.": "用于 SID/uid/gid 映射条目的目录基本后缀。 示例：dc=test,dc=org。 未定义时，idmap_ldap 默认使用 <a href=\"https://www.samba.org/samba/docs/current/man-html/smb.conf.5.html\" target= 中的 ldap idmap 后缀选项 \"_blank\">smb.conf</a>。", "The domain to access the Active Directory server when using the LDAP server inside the Active Directory server.": "使用活动目录服务器内部的LDAP服务器时要访问活动目录服务器的域。", "The file used to manually update the system. Browse to the update file stored on the system logged into the web interface to upload and apply. Update file names end with <i>-manual-update-unsigned.tar</i>": "用于手动更新系统的文件。浏览到登录到Web界面的系统上存储的更新文件以进行上载和应用。更新文件名以<i> -manual-update-unsigned.tar </i>结尾", "The filesystem {filesystemName} is {filesystemDescription}, but datastore {datastoreName} is {datastoreDescription}. Is this correct?": "文件系统 {filesystemName} 是 {filesystemDescription}，但数据存储区 {datastoreName} 是 {datastoreDescription}。 这个对吗？", "The following changes to this SMB Share require the SMB Service to be restarted before they can take effect.": "此 SMB 共享的以下更改需要重新启动 SMB 服务才能生效。", "The following datasets cannot be unlocked.": "以下数据集无法解锁。", "The following disk(s) is/are part of the currently exported pools (specified next to disk names). Reusing these disk will make the attached exported pools unable to import. You will lose any and all data in those pools. Please make sure that any sensitive data in said pools is backed up before reusing/repurposing these disks.": "以下磁盘是当前导出的池的一部分（在磁盘名称旁边指定）。 重复使用这些磁盘将使附加的导出池无法导入。 您将丢失这些池中的所有数据。 请确保在重新使用/重新利用这些磁盘之前备份所述池中的任何敏感数据。", "The following { n, plural, one {application} other {# applications} } will be upgraded. Are you sure you want to proceed?": "这{ n, plural, 1 {# 个应用} other {# 个应用} } 将被升级，您确定吗？", "The following { n, plural, one {boot environment} other {# boot environments} } will be deleted. Are you sure you want to proceed?": "后面的 { n, plural, 1 {boot environment} other {# boot environments} } 将被删除。 您确定要继续吗？", "The following { n, plural, one {docker image} other {# docker images} } will be deleted. Are you sure you want to proceed?": "以下 { n, plural, 1 {docker image} other {# docker images} } 将被删除。 您确定要继续吗？", "The following { n, plural, one {docker image} other {# docker images} } will be updated. Are you sure you want to proceed?": "以下 { n, plural, 1 {docker image} other {# docker images} } 将被更新。 您确定要继续吗？", "The following { n, plural, one {snapshot} other {# snapshots} } will be deleted. Are you sure you want to proceed?": "以下{ n, plural, 1 {snapshot} other {# snapshots} }将被删除。 您确定要继续吗？", "The friendly name to show in front of the sending email  address. Example: <i>Storage System 01</i>&<EMAIL>&gt": "显示在发送电子邮件地址前面的友好名称。示例：<i>存储系统01</i> &<EMAIL>&gt", "The group which controls the dataset. This group has the same permissions as granted to the <i>group@</i> <i>Who</i>. Groups created manually or imported from a directory service appear in the drop-down menu.": "控制数据集的组。该组具有与授予<i> group @ </i> <i> Who </i>相同的权限。手动创建或从目录服务导入的组显示在下拉菜单中。", "The hostname or IP address of the LDAP server.  Separate entries by pressing <code>Enter</code>.": "LDAP服务器的主机名或IP地址。通过按<code>Enter</code>分隔条目。", "The imported pool contains encrypted datasets, unlock them now?": "导入的池包含加密的数据集，现在将其解锁吗？", "The key usage extension defines the purpose  (e.g., encipherment, signature, certificate signing) of the key contained in  the certificate. The usage restriction might be employed when a key that  could be used for more than one operation is to be restricted. For  example, when an RSA key should be used only to verify signatures on  objects other than public key certificates and CRLs, the <i>Digital Signature</i>  bits would be asserted. Likewise, when an RSA key should be used only for key  management, the <i>Key Encipherment</i> bit would be asserted. <br>  See <a href=\"https://www.ietf.org/rfc/rfc3280.txt\">RFC 3280, section *******</a>  for more information.": "密钥使用扩展定义了证书中包含的密钥的用途（例如，加密、签名、证书签名）。 当要限制可用于多个操作的密钥时，可以采用使用限制。例如，当RSA密钥仅用于验证公钥证书和CRLs以外的对象上的签名时，<i>数字签名</i>位将被断言。同样，当RSA密钥仅用于密钥管理时，<i>密钥加密</i>位将被声明。<br>有关详细信息，请参阅 <a href=\"https://www.ietf.org/rfc/rfc3280.txt\">RFC 3280，第 ******* 节</a>。", "The length of {field} should be at least {minLength}": "{field} 的长度应至少为 {minLength}", "The length of {field} should be at least {minLength}.": "{field} 的长度应至少为 {minLength}。", "The length of {field} should be no more than {maxLength}": "{field} 的长度不应超过 {maxLength}", "The length of {field} should be no more than {maxLength}.": "{field} 的长度不应超过 {maxLength}。", "The lifespan (in seconds) of each One-Time Password. Default is 30 seconds.  The minimum lifetime is 5 seconds.": "每个一次性密码的寿命（以秒为单位）。默认值为30秒。最小寿命为5秒。", "The lifetime of the CA specified in days.": "CA的寿命，以天为单位。", "The list is empty.": "该列表为空。", "The maximum number of simultaneous clients.": "并发客户端的最大数量。", "The maximum polling interval, in seconds, as a power of 2. For example, <i>10</i> means 2^10, or 1,024 seconds. The default is 10, maximum value is 17.": "最大轮询间隔（以秒为单位）为2的幂。例如，<i> 10 </i>表示2^10或1,024秒。默认值为10，最大值为17。", "The message to show when a user logs in with SSH.": "用户使用SSH登录时显示的消息。", "The minimum polling interval, in seconds, as a power of 2. For example, <i>6</i> means 2^6, or 64 seconds. The default is 6, minimum value is 4.": "最小轮询间隔（以秒为单位）为2的幂。例如，<i> 6 </i>表示2^6或64秒。默认值为6，最小值为4。", "The name \"{value}\" is already in use.": "名称 \"{value}\" 已被使用。", "The number of bits in the key used by the cryptographic algorithm. For security reasons, a minimum key length of <i>2048</i> is recommended.": "密码算法使用的密钥中的位数。出于安全原因，建议最小密钥长度为<i> 2048 </i>。", "The number of digits in the One-Time Password. The default value is 6,  which is the length of the standard OTP from Google. Check the settings of your app or device  before selecting this.": "一次性密码中的位数。默认值为6，这是Google的标准OTP的长度。选择此项之前，请检查您的应用或设备的设置。", "The operation timed out. The requested resource might be offline. Check the network connection.": "操作超时。请求的资源可能处于脱机状态。检查网络连接。", "The parent of this dataset has an Access Control List (ACL). Do you want to set an ACL for this dataset using the ACL Manager? ": "此数据集的父级具有访问控制列表 (ACL)。 是否要使用ACL管理器为此数据集设置ACL？", "The passwords do not match.": "密码不匹配。", "The pasted string ends with a space": "粘贴的字符串以空格结尾", "The pasted string starts with a space": "粘贴的字符串以空格开头", "The path <i>{path}</i> is in a locked dataset.": "路径 <i>{path}</i> 位于锁定的数据集中。", "The physical location of the host.": "主机的物理位置。", "The pool <i>{pool}</i>is in the database but not connected to the machine. If it was exported by     mistake, reconnect the hardware and use <b>Import Pool</b>.<br /><br />": "池 <i>{pool}</i> 位于数据库中，但未连接到计算机。 如果错误导出，重新连接硬件并使用<b>Import Pool</b>。<br /><br />", "The preconfigured system <i>Certificate</i> to use for authenticating the TLS protocol connection to the remote system log server.": "预配置的系统<i>证书</i>，用于验证与远程系统日志服务器的TLS协议连接。", "The product of vCPUs, cores and threads must not exceed {maxVCPUs} on this system.": "产品的vCPU、内核和线程不得超过 {maxVCPUs} 在这个系统上。", "The reporting database is broken": "报告数据库已损坏", "The rollback will destroy any  related intermediate, child dataset, and cloned snapshots that are  newer than the rollback snapshot.": "回滚将销毁比回滚快照新的intermediate，子数据集和克隆快照。", "The search base where group objects can be found in the LDAP server.": "可在LDAP服务器中找到组对象的搜索库。", "The search base where user objects can be found in the LDAP server.": "可在LDAP服务器中找到用户对象的搜索库。", "The secret used to generate OTPs. The secret is produced by the system when Two-Factor  Authentication is first activated.": "用于生成OTP的密码。首次激活“双重身份验证”时，系统会产生此秘密。", "The sharing configuration will be removed.    Data in the share dataset will not be affected.": "共享配置将被删除。共享数据集中的数据不受影响。", "The specified permissions of that group are used by all clients.": "所有客户端都使用该组的指定权限。", "The specified permissions of that user are used by all clients.": "所有客户端都使用该用户的指定权限。", "The standby controller has finished upgrading. To complete the update process,  failover to the standby controller.": "备用控制器已完成升级。要完成更新过程，请故障转移到备用控制器。", "The standby controller will be automatically restarted to finalize the update. Apply updates and restart the standby controller?": "备用控制器将自动重启以完成更新。应用更新并重新启动备用控制器？", "The system could not retrieve any NFS Shares from the database. Please click the button below to add an NFS Share.": "系统无法从数据库中检索任何 NFS 共享。 请单击下面的按钮添加 NFS 共享。", "The system could not retrieve any SMB Shares from the database. Please click the button below to add an SMB Share.": "系统无法从数据库中检索任何SMB共享。请单击下面的按钮添加SMB共享。", "The system could not retrieve any WebDAV Shares from the database. Please click the button below to add an WebDAV Share.": "系统无法从数据库中检索任何 WebDAV 共享。 请单击下面的按钮添加 WebDAV 共享。", "The system could not retrieve any {item} from the database.": "系统无法从数据库中检索任何 {item}。", "The system dataset will be updated and the SMB service restarted. This will cause a temporary disruption of any active SMB connections.": "系统数据集将被更新，SMB服务将重新启动。这将导致任何活动的SMB连接暂时中断。", "The system restricts creating a zvol that brings the pool to over 80% capacity. Set to force creation of the zvol (<b>NOT Recommended</b>).": "系统限制创建zvol以使池达到80％以上的容量。设置为强制创建zvol（<b>不推荐</b>）。", "The system returned the following error - ": "系统返回以下错误 -", "The system uses a self-signed certificate  to enable encrypted web interface connections. To change  the default certificate, select a different certificate  that was created or imported in the <b>Certificates</b> menu.": "系统使用自签名证书来启用加密的Web界面连接。要更改默认证书，请在<b>证书</b>菜单中选择一个已创建或导入的其他证书。", "The system will reboot and be briefly unavailable while applying updates. Apply updates and reboot?": "系统将重新启动，并且在应用更新时短暂不可用。应用更新并重新启动？", "The ticket was created successfully (see link below), but the debug file   failed to attach. Please download the debug manually    (System → Advanced → Save Debug), upload it to a third-party storage service and provide    a link in the JIRA issue comment.": "工单已成功创建（请参见下面的链接），但调试文件未能附加。 请手动下载调试（系统→高级→保存调试），上传到第三方存储服务并在JIRA问题评论中提供链接。", "The time in seconds the system waits for the VM to cleanly shut down.  During system shutdown, the system initiates poweroff for the VM after the shutdown  timeout has expired.": "系统等待虚拟机完全关闭的时间（以秒为单位）。在系统关闭期间，关闭超时到期后，系统将启动虚拟机的电源关闭。", "The time values when the task will run. Accepts standard <a href=\"https://man7.org/linux/man-pages/man5/crontab.5.html\" target=\"_blank\">crontab(5)</a> values. </br></br>Symbols:</br> A comma (,) separates individual values.</br> An asterisk (*) means  \"match all values\".</br> Hyphenated numbers (1-5) sets a range of time.</br> A slash (/) designates a step in the value: */2 means every other minute.</br></br> Example: 30-35 in Minutes, 1,14 in Hours, and */2 in Days means the task will run on 1:30 - 1:35 AM and 2:30 - 2:35 PM every other day.": "任务运行的时间值。 接受标准 <a href=\"https://man7.org/linux/man-pages/man5/crontab.5.html\" target=\"_blank\">crontab(5)</a> 值。 </br></br>符号：</br> 逗号 (,) 分隔各个值。</br> 星号 (*) 表示“匹配所有值”。</br> 连字符数字（1- 5) 设置时间范围。</br> 斜线 (/) 表示值中的一个步长：*/2 表示每隔一分钟。</br></br> 示例：30-35 in Minutes, 1, 14 小时和 */2 天表示任务将在 1:30 - 1:35 AM 和 2:30 - 2:35 PM 每隔一天运行一次。", "The update file is temporarily stored here before being applied.": "更新文件在应用之前会暂时存储在此处。", "The user account <i>Email</i> address to use for the  envelope <i>From</i> email address. The user account <i>Email</i> in  <b>Accounts > Users > Edit</b> must be configured first.": "将所使用的用户的<i>电子邮件</i>地址填入<i>发信电子邮件地址</i>发信。必须首先配置<b>帐户>用户>编辑</b>中用户账户的<i>电子邮件</i>地址。", "The user-defined string that can unlock this dataset.": "可以解锁此数据集的用户定义的字符串。", "The value is out of range. Enter a value between {min} and {max}.": "该值超出范围。输入一个介于 {min} 和 {max} 之间的值。", "The web service must restart for the protocol changes to take effect. The UI will be temporarily unavailable. Restart the service?": "Web服务必须重新启动才能使协议更改生效。用户界面暂时不可用。重新启动服务？", "The zvol default block size is automatically chosen based on the number of the disks in the pool for a general use case.": "在一般情况下，将基于池中的磁盘数量自动选择zvol默认块大小。", "The {service} service failed to start.": "{service} 服务未能启动。", "The {service} service failed to stop.": "{service} 服务未能停止。", "The {service} service has been enabled.": "{service} 服务已启用。", "The {service} service has been started.": "{service} 服务已启动。", "Theme": "主题", "There are no alerts.": "没有警报。", "There are no images requiring upgrade": "没有需要升级的镜像", "There are no tasks.": "没有任何任务。", "There are pending network interface changes. Review them now?": "有待处理的网络接口更改。现在审查它们？", "There are unapplied network interface changes that must be tested before being permanently saved. Test changes now?": "在永久保存之前必须测试未应用的网络接口更改。现在测试更改？", "There are {sessions} active iSCSI connections.": "有 {sessions} 个活动的 iSCSI 连接。", "There is an upgrade waiting to finish.": "有一个升级等待完成。", "These  <a href=\"https://support.apple.com/en-us/HT210803\" target=\"_blank\">protocol extensions</a>  can be used by macOS to improve the performance and behavioral characteristics of SMB shares.  This is required for Time Machine support.": "macOS可以使用这些<a href=\"https://support.apple.com/en-us/HT210803\" target=\"_blank\">协议扩展</a>来改善SMB共享的性能和行为特征。这是时光机支持所必需的。", "These IP Addresses were removed: {uniqueIPs}. The listed services will be changed to listen on 0.0.0.0: {affectedServices}": "这些 IP 地址已被删除：{uniqueIPs}。列出的服务将更改为在 0.0.0.0 上侦听：{affectedServices}", "These arguments are passed to <a href=\"https://rclone.org/docs/\" target=\"_blank\">rclone</a>.": "这些参数传递给<a href=\"https://rclone.org/docs/\" target=\"_blank\">rclone</a> 。", "These datasets could not be unlocked.": "这些数据集无法解锁。", "These datasets were not unlocked because the parent datasets could not be unlocked.": "这些数据集未解锁，因为父数据集无法解锁。", "These datasets were successfully unlocked.": "这些数据集已成功解锁。", "These datasets will be unlocked with the provided credentials.": "这些数据集将使用提供的凭据解锁。", "These images will not be removed as there are other apps which are consuming them": "这些镜像不会被删除，因为有其他应用程序正在使用它们", "These running processes are using {datasetName}:": "这些正在运行的进程正在使用 {datasetName}：", "These running processes are using {name}:": "这些正在运行的进程正在使用 {name}：", "These services depend on dataset {name} and will be destroyed if the dataset is deleted:": "这些服务依赖于数据集 {name}，如果数据集被删除，这些服务将被销毁：", "These services depend on pool {name} and will be disrupted if the pool is detached:": "这些服务依赖于池 {name}，如果池被分离，这些服务将被中断：", "These services must be restarted to export the pool:": "必须重新启动这些服务才能导出池：", "These services must be stopped to export the pool:": "必须停止这些服务才能导出池：", "These settings require the reconfiguration of any existing client.  After saving these changes, use your device to scan the QR Code.": "这些设置需要重新配置任何现有客户端。保存这些更改后，使用您的设备扫描二维码。", "Thick": "丰厚", "Third DNS server.": "第三个DNS服务器。", "Third-party Cloud service providers. Choose a provider  to configure connection credentials.": "第三方云服务提供商。选择一个提供商来配置连接凭据。", "This Certificate Authority is being used to sign one or more certificates. It can be deleted  only after deleting these certificates.": "此证书颁发机构用于签署一个或多个证书。只有在删除这些证书后才能删除。", "This Dataset": "此数据集", "This Dataset and Child Datasets": "此数据集和子数据集", "This action removes all ACLs from the current  dataset and any directories or files contained within this  dataset. Stripping the ACL resets dataset permissions. This  can make data inaccessible until new permissions are created.": "此操作将从当前数据集以及此数据集中包含的任何目录或文件中删除所有 ACL。剥离ACL会重置数据集权限。这可能会使数据无法访问，直到创建新权限。", "This action will set all dataset quotas for the removed or invalid groups to 0,       virutally removing any dataset quota entires for such groups.       Are you sure you want to proceed?": "此操作会将已删除或无效组的所有数据集配额设置为 0，虚拟删除此类组的所有数据集配额。 您确定要继续吗？", "This action will set all dataset quotas for the removed or invalid users to 0, virutally removing any dataset quota entires for such users. Are you sure you want to proceed?": "此操作会将已删除或无效用户的所有数据集配额设置为 0，虚拟删除此类用户的所有数据集配额。 您确定要继续吗？", "This allows your TrueNAS system to be monitored and administrated by TrueCommand. Click <b>SIGNUP</b> to create a new TrueCommand Cloud instance or <b>CONNECT</b> to join an existing instance.": "这使您的TrueNAS系统可以由TrueCommand进行监视和管理。单击<b>注册</b>创建新的TrueCommand Cloud实例，或单击<b>连接</b>加入现有实例。", "This certificate's public key is used for key management.": "该证书的公钥用于密钥管理。", "This certificate's public key is used to decipher user data only during key agreement operations. Requires that <b>Key Agreement</b> is also set.": "该证书的公共密钥仅在密钥协商操作期间才用于解密用户数据。要求还设置<b>密钥协议</b>。", "This certificate's public key is used to encipher user data.": "该证书的公钥用于加密用户数据。", "This certificate's public key is used to manage key agreement.": "该证书的公钥用于管理密钥协议。", "This certificate's public key is used to verify signatures on a certificate revocation list (CRL).": "该证书的公共密钥用于验证证书吊销列表（CRL）上的签名。", "This certificate's public key is used to verify signatures on other public key certificates. Activating this also requires enabling the <b>CA</b> basic constraint.": "该证书的公共密钥用于验证其他公共密钥证书上的签名。激活它还需要启用<b> CA </b>基本约束。", "This certificate's public key is used with digital signature methods that are separate from certificate or CRL signing.": "该证书的公钥与数字签名方法一起使用，这些方法与证书或CRL签名是分开的。", "This certificate's public key verifies digital signatures used for a non-repudiation service.": "该证书的公钥验证用于不可抵赖服务的数字签名。", "This change can interrupt connectivity and must be tested before making permanent. ": "此更改可能会中断连接，并且必须在永久更改之前进行测试。", "This controls how the SMB share reads and writes data. Leave unset for the share to behave like a normal SMB share and set for the share to behave like the deprecated Apple Filing Protocol (AFP). This should only be set when this share originated as an AFP sharing configuration. This is not required for pure SMB shares or MacOS SMB clients.": "这控制了SMB共享读取和写入数据的方式。保留未设置以使共享的行为类似于普通SMB共享，并将共享设置为类似于已弃用的Apple归档协议(AFP)。仅当此共享作为AFP共享配置发起时才应设置。对于纯SMB共享或MacOS SMB客户端，这不是必需的。", "This dataset has children with shares": "这个数据集有共享的子数据集", "This dataset is used by the system": "该数据集由系统使用", "This dataset is used by: {apps}": "此数据集由：{apps}", "This dataset is used by: {vms}": "此数据集由：{vms} 使用", "This dataset is used to store Kubernetes config and other container related data": "该数据集用于存储 Kubernetes 配置和其他容器相关数据", "This disk is part of the exported pool {pool}. Adding this disk to a new or other existing pools will make {pool} unable to import. You will lose any and all data in {pool}. Please make sure you have backed up any sensitive data in {pool} before reusing/repurposing this disk.": "此磁盘是导出池 {pool} 的一部分。 将此磁盘添加到新池或其他现有池将使 {pool} 无法导入。 您将丢失 {pool} 中的所有数据。 在重新使用/重新利用此磁盘之前，请确保您已备份 {pool} 中的所有敏感数据。", "This disk is part of the exported pool {pool}. Reusing this disk will make {pool} unable to import. You will lose any and all data in {pool}. Please make sure any sensitive data in {pool} is backed up before reusing/repurposing this disk.": "此磁盘是导出池 {pool} 的一部分。 重复使用此磁盘将使 {pool} 无法导入。 您将丢失 {pool} 中的所有数据。 请确保在重用/重新利用此磁盘之前备份 {pool} 中的所有敏感数据。", "This disk is part of the exported pool {pool}. Reusing this disk will make {pool} unable to import. You will lose any and all data in {pool}. Please make sure any sensitive data in {pool} is backed up before reusing/repusposing this disk.": "此磁盘是导出池 {pool} 的一部分。 重复使用此磁盘将使 {pool} 无法导入。 您将丢失 {pool} 中的所有数据。 请确保在重新使用/重新使用此磁盘之前备份 {pool} 中的所有敏感数据。", "This disk is part of the exported pool {pool}. Wiping this disk will make {pool} unable\n  to import. You will lose any and all data in {pool}. Please make sure that any sensitive data in {pool} is backed up before wiping this disk.": "此磁盘是导出池 {pool} 的一部分。 擦除此磁盘将使 {pool} 无法\n 导入。 您将丢失 {pool} 中的所有数据。 请确保在擦除此磁盘之前备份 {pool} 中的所有敏感数据。", "This disk is part of the exported pool {pool}. Wiping this disk will make {pool} unable        to import. You will lose any and all data in {pool}. Please make sure that any sensitive data in {pool} is backed up before reusing/repurposing this disk.": "此磁盘是导出池 {pool} 的一部分。 擦除此磁盘将使 {pool} 无法导入。 您将丢失 {pool} 中的所有数据。 请确保在重用/重新利用此磁盘之前备份 {pool} 中的所有敏感数据。", "This feature is memory-intensive and <b>permanently affects how the data is stored</b>. It is recommended to be very familiar with the benefits and drawbacks of deduplication before activating this feature.": "此功能会占用大量内存，并且<b>永久影响数据的存储方式</b>。建议在激活此功能之前非常熟悉重复数据删除的优缺点。", "This field accepts human-readable input (Ex. 50 GiB, 500M, 2 TB).": "此字段接受用户可读的空间（例如 50 GiB、500M、2 TB）。", "This field accepts human-readable input (Ex. 50 GiB, 500M, 2 TB).   If units are not specified, the value defaults to": "接受用户可使用容量数值（例如50 GiB，500M，2 TB）。如果未指定单位，则默认值为所有容量", "This field remains empty until a valid <b>Username</b> and <b>Password</b> is entered. Choose the category that best describes the bug or feature being reported.": "在输入有效的<b>用户名</b>和<b>密码</b>之前，此字段保持为空。选择最能描述所报告错误或功能的类别。", "This is a ONE-SHOT {alertLevel} alert, it won't be dismissed automatically": "这是一次性 {alertLevel} 警报，不会被自动关闭", "This is a one way action and cannot be reversed. Are you sure you want to revoke this Certificate?": "这是一种单向操作，无法逆转。您确定要撤销此证书吗？", "This is a production system": "这是一个生产系统", "This is not a production release, and should only be used for testing.": "这不是生产版本，应仅用于测试。", "This is the OS_TENANT_NAME from an <a href=\"https://rclone.org/swift/#configuration-from-an-openstack-credentials-file\" target=\"_blank\">OpenStack credentials file</a>.": "这是来自OS_TENANT_NAME的<a href=\"https://rclone.org/swift/\\#configuration-from-an-openstack-credentials-file\" target=\"_blank\">OpenStack凭据文件</a>。", "This is the only time the key is shown.": "这是唯一一次显示密钥。", "This job is scheduled to run again {nextRun}.": "此工作计划将再次运行 {nextRun}。", "This job will not run again until it is enabled.": "启用该任务后，它将无法再次运行。", "This operation might take a long time. It cannot be aborted once started. Proceed?": "此操作可能需要很长时间。 一旦开始就不能中止。 继续？", "This option controls how metadata and alternate data streams read write to disks. Only enable this when the share configuration was migrated from the deprecated Apple Filing Protocol (AFP). Do not attempt to force a previous AFP share to behave like a pure SMB share or file corruption can occur.": "此选项控制元数据和备用数据流如何读写磁盘。仅当共享配置从已弃用的Apple归档协议(AFP)迁移时才启用此功能。不要试图强制以前的AFP共享表现得像纯SMB共享，否则可能会发生文件损坏。", "This pool contains the system dataset that stores critical data like debugging core files, encryption keys for pools, and Samba 4 metadata such as the user/group cache and share level permissions. Exporting this pool will transfer the system dataset to another available pool. If the only available pool is encrypted, that pool will no longer be able to be locked. When no other pools exist, the system dataset transfers back to the TrueNAS operating system device.": "该池包含存储关键数据的系统数据集，例如调试核心文件、池的加密密钥以及Samba 4元数据（例如用户/组缓存和共享级别权限）。导出此池会将系统数据集传输到另一个可用池。如果唯一可用的池被加密，则该池将不再能够被锁定。当不存在其他池时，系统数据集传输回TrueNAS操作系统设备。", "This process continues in the background after closing this dialog.": "关闭此对话框后，此过程将在后台继续。", "This system cannot communicate externally.": "该系统无法与外部通信。", "This system will restart when the update completes.": "更新完成后，该系统将重新启动。", "This type of VDEV requires at least {n, plural, one {# disk} other {# disks}}.": "这种类型的 VDEV 至少需要 {n, plural, other {# 个磁盘}}。", "This value represents the threshold block size for including small file blocks into the special allocation class. Blocks smaller than or equal to this value will be assigned to the special allocation class while greater blocks will be assigned to the regular class. Valid values are zero or a power of two from 512B  up to 1M. The default size is 0 which means no small file blocks will be allocated in the special class. Before setting this property, a special class vdev must be added to the pool. See <a href=\"https://zfsonlinux.org/manpages/0.7.13/man8/zpool.8.html\" target=\"_blank\">zpool(8)</a> for more details on the special allocation": "此值表示将小文件块包含到特殊分配类中的阈值块大小。小于或等于此值的块将分配给特殊分配类，而较大的块将分配给常规类。有效值为0或2的幂，从512B到1M。默认大小为0，这意味着不会在特殊类中分配小文件块。在设置此属性之前，必须将一个特殊的类vdev添加到池中。有关更多详细信息，请参阅 <a href=\"https://zfsonlinux.org/manpages/0.7.13/man8/zpool.8.html\" target=\"_blank\">zpool(8)</a>特别分配", "Thread responsible for syncing db transactions not running on other node.": "负责同步未在其他节点上运行的数据库事务的线程。", "Thread responsible for syncing db transactions not running on this node.": "负责同步未在此节点上运行的数据库事务的线程。", "Thread:": "线程：", "Threads": "线程", "Threads:": "线程：", "Threshold Days": "天数阈值", "Threshold days": "阈值天数", "Threshold temperature in Celsius. If the drive temperature is higher than this value, a LOG_CRIT level log entry is created and an email is sent. <i>0</i> disables this check.": "阈值温度（摄氏度）。如果驱动器温度高于此温度，则会创建LOG_CRIT级别的日志条目并发送电子邮件。<i> 0 </i>禁用此检查。", "Thu": "周四", "Thursday": "周四", "TiB": "TiB", "Ticket": "票", "Time": "时间", "Time (in seconds) before the system  stops attempting to establish a connection with the remote system.": "系统停止尝试与远程系统建立连接之前的时间（以秒为单位）。", "Time Format": "时间格式", "Time Machine": "时间机器", "Time Machine Quota": "时间机器配额", "Timeout": "超时", "Times": "时间", "Timezone": "时区", "Title": "标题", "To activate this periodic snapshot schedule, set  this option. To disable this task without deleting it, unset this  option.": "要激活此定期快照计划，请设置此选项。要禁用此任务而不删除它，请取消设置此选项。", "To configure Isolated GPU Device(s), click the \"Configure\" button.": "要配置独立 GPU 设备，请单击“配置”按钮。", "To configure {title} click the \"Add\" button.": "要配置 {title}，请单击“添加”按钮。", "To enable disable Active Directory first.": "首先启用禁用活动目录。", "To enable disable LDAP first.": "首先启用禁用LDAP。", "Toggle Collapse": "切换折叠", "Toggle {row}": "切换 {row}", "Token": "令牌（Token）", "Token created with  <a href=\"https://developers.google.com/drive/api/v3/about-auth\" target=\"_blank\">Google Drive</a>.": "使用<a href=\"https://developers.google.com/drive/api/v3/about-auth\" target=\"_blank\">谷歌云端硬盘</a>创建的令牌。", "Token created with  <a href=\"https://developers.google.com/drive/api/v3/about-auth\" target=\"_blank\">Google Drive</a>. Access Tokens expire periodically and  must be refreshed.": "使用<a href=\"https://developers.google.com/drive/api/v3/about-auth\" target=\"_blank\">Google云端硬盘</a>创建的令牌。访问令牌会定期过期，必须刷新。", "Token expired, please log back in.": "令牌已过期，请重新登录。", "Token is required. Please login to get one!": "需要令牌（Token），请登录并获取一个！", "Top level of the LDAP directory tree to be used when searching for resources. Example: <i>dc=test,dc=org</i>.": "搜索资源时要使用的LDAP目录树的顶层。示例：<i>dc=test,dc=org</i>。", "Topic <a href=\"https://docs.aws.amazon.com/sns/latest/dg/CreateTopic.html\" target=\"_blank\">Amazon Resource Name (ARN)</a> for publishing. Example: <b>arn:aws:sns:us-west-2:111122223333:MyTopic</b>.": "主题 <a href=\"https://docs.aws.amazon.com/sns/latest/dg/CreateTopic.html\" target=\"_blank\">Amazon资源名称（ARN）</a>用于发布。例如：<b>arn:aws:sns:us-west-2:111122223333:MyTopic</b>。", "Topology": "拓扑", "Total": "总计", "Total Allocation": "总分配", "Total Disks": "磁盘总数", "Total Down": "总下降", "Total Snapshots": "总快照", "Total ZFS Errors": "ZFS 错误总数", "Train": "分支", "Transfer": "传输", "Transfer Mode": "传输模式", "Transfers": "传输", "Transmit Hash Policy": "传输哈希策略", "Transparently reuse a single copy of duplicated  data to save space. Deduplication can improve storage capacity, but is RAM intensive.  Compressing data is generally recommended before using deduplication. Deduplicating data is  a one-way process. <b>Deduplicated data cannot be undeduplicated!</b>.": "使用重复数据的单个副本以节省空间。重复数据删除功能可以提高存储容量，但需要占用大量RAM。通常建议在使用重复数据删除功能之前压缩数据。重复数据删除是一个单向过程。 <b>已去重的数据不能再重复删除！</b>。", "Transport": "传输", "Transport Options": "传输选项", "Traverse": "阻止", "TrueCommand": "TrueCommand", "TrueCommand Cloud Service": "TrueCommand云服务", "TrueCommand Cloud Service deregistered": "TrueCommand云服务已注销", "TrueCommand Cloud Service has been deregistered.": "TrueCommand云服务已注销", "TrueCommand IP": "TrueCommand IP", "TrueNAS Controller": "TrueNAS控制器", "TrueNAS Help": "TrueNAS帮助", "TrueNAS URL": "TrueNAS URL", "TrueNAS software versions do not match between storage controllers.": "TrueNAS 软件版本在存储控制器之间不匹配。", "TrueNAS was unable to reach update servers.": "TrueNAS 无法访问更新服务器。", "TrueNAS {product} is Free and <a href=\"https://github.com/truenas/\" target=\"_blank\"> Open Source</a> software, which is provided as-is with no warranty.": "TrueNAS {product} 是免费且<a href=\"https://github.com/truenas/\" target=\"_blank\">开源</a>软件，按原样提供，不提供任何担保 .", "Trust Guest Filters": "信任访客过滤器", "Tue": "周二", "Tuesday": "周二", "Tunables": "微调", "Turn OFF": "关闭", "Turn Off Service": "关闭服务", "Turn On Service": "开启服务", "Two-Factor Auth": "双重验证", "Two-Factor Authentication": "两因素身份验证", "Two-Factor Authentication Code": "双重身份验证码", "Two-factor authentication IS currently enabled.": "当前启用了双重身份验证。", "Two-factor authentication is NOT enabled.": "未启用双重身份验证。", "Two-letter <a  href=\"https://www.iso.org/obp/ui/#search/code/\" target=\"_blank\">ISO 3166-1 alpha-2 code</a>  used to enable LLDP location support.": "两个字母的<a  href=\"https://www.iso.org/obp/ui/\\#search/code/\" target=\"_blank\"> ISO 3166-1 alpha-2代码</a> 用于启用LLDP位置支持。", "Type": "类型", "Type of Microsoft acount. Logging in to a Microsoft account  automatically chooses the correct account type.": "Microsoft帐户的类型。登录到Microsoft帐户会自动选择正确的帐户类型。", "UDP port number on the system receiving  SNMP trap notifications. The default is <i>162</i>.": "接收SNMP陷阱通知的系统上的UDP端口号。 默认值为<i> 162 </i>。", "UID": "UID", "UNIX (NFS) Shares": "UNIX (NFS)共享", "UNIX Charset": "UNIX 字符集", "UPDATE IN PROGRESS": "正在更新", "UPS": "UPS", "UPS Mode": "UPS 模式", "URI of the ACME Server Directory. Choose a preconfigured URI or enter a custom URI.": "ACME服务器目录的URI。选择一个预配置的URI或输入一个自定义URI。", "URL": "网络地址", "URL of the HTTP host to connect to.": "要连接的HTTP主机的URL。", "USB Passthrough Device": "USB 直通设备", "UTC": "世界标准时间", "Unable to retrieve Available Applications": "无法检索可用的应用", "Unable to terminate processes which are using this pool: ": "无法终止正在使用此池的进程：", "Unassigned": "未分配", "Unassigned Disks": "未分配的磁盘", "Unencrypted": "未加密", "Unhealthy": "不健康", "Unique LUN ID. The default is generated from the MAC address of the system.": "唯一的逻辑单元 ID。默认值是从系统的MAC地址生成的。", "Unique Virtual Host ID on the broadcast  segment of the network. Configuring multiple Virtual IP addresses  requires a separate VHID for each address.": "网络广播段上的唯一虚拟主机ID。配置多个虚拟IP地址需要为每个地址使用单独的VHID。", "Unique drive identifier. Log in to a Microsoft account and choose  a drive from the <i>Drives List</i> drop-down to add a valid ID.": "唯一的驱动器标识符。登录到Microsoft帐户，然后从<i>驱动器列表</i>下拉列表中选择一个驱动器以添加有效的ID。", "Unique snapshot name. Cannot be used with  a <i>Naming Schema</i>.": "唯一的快照名称。不能与<i>命名惯例</i>一起使用。", "Unit": "单位", "Unix (NFS) Share": "Unix (NFS) 共享", "Unix NSS Info": "Unix NSS信息", "Unix Permissions": "Unix权限", "Unix Permissions Editor": "Unix权限编辑器", "Unix Primary Group": "Unix主要组", "Unkeep": "取消保留", "Unknown": "未知", "Unknown CPU": "未知CPU", "Unknown Disk": "未知磁盘", "Unknown PID": "未知的进程识别号", "Unlock": "解锁", "Unlock Child Encrypted Roots": "解锁子加密根", "Unlock Datasets": "解锁数据集", "Unlock with Key file": "用密钥文件解锁", "Unlocked": "解锁", "Unlocking Datasets": "正在解锁数据集", "Unselect All": "取消全选", "Unset": "取消设置", "Unset <i>Generate Encryption Key</i> to instead import a custom Hex key.": "取消设置<i>生成加密密钥</i>以导入自定义十六进制密钥。", "Unset Pool": "取消设置池", "Unset to add a login prompt to the system before the console menu is shown.": "在显示控制台菜单之前，取消设置为系统添加登录提示。", "Unset to disable the scheduled scrub  without deleting it.": "取消设置以禁用预定的校验而不删除它。", "Unset to disable this service without deleting it.": "取消设置以禁用此服务而不删除它。", "Unset to prepend the domain name to the username. Unset to prevent name collisions when Allow Trusted Domains is set and multiple domains use the same username.": "取消设置以将域名预置到用户名。取消设置防止名称冲突时，允许受信任的站点设置和多域使用相同的用户名。", "Unshare": "取消共享", "Unsupported Hardware": "不支持的硬件", "Until": "直到", "Up to date": "最新", "Upcoming tasks": "即将到来的任务", "Update": "更新", "Update 'Time Machine'": "更新“时间机器”", "Update Available": "更新可用", "Update File": "更新文件", "Update File Temporary Storage Location": "更新文件临时存储位置", "Update Image": "更新镜像", "Update Interval": "更新间隔", "Update License": "更新许可证", "Update Period": "更新周期", "Update Production Status": "更新生产状态", "Update TrueCommand Settings": "更新TrueCommand设置", "Update available": "更新可用", "Update in Progress": "正在进行更新", "Update is complete, please reboot the system.": "更新完成，请重新启动系统。", "Update successful. Please reboot for the update to take effect. Reboot now?": "更新成功。请重启以便更新生效。现在重启？", "Updated 'Use as Home Share'": "更新了“用作家庭共享”", "Updates Available": "有可用更新", "Updates successfully downloaded": "更新下载成功", "Updating": "更新中", "Updating key type": "更新密钥类型", "Upgrade": "升级", "Upgrade Pool": "升级池", "Upgrade Waiting to Finish": "等待升级完成", "Upgrades both controllers. Files are downloaded to the Active Controller and then transferred to the Standby Controller. The upgrade process starts concurrently on both TrueNAS Controllers. Continue with download?": "升级两个控制器。文件被下载到活动控制器，然后传输到备用控制器。升级过程同时在两个TrueNAS控制器上开始。继续下载吗？", "Upgrading Apps. Please check on the progress in Task Manager.": "升级应用程序。请在任务管理器中检查进度。", "Upgrading...": "正在升级...", "Upload": "上传", "Upload Chunk Size (MiB)": "上传块大小（MiB）", "Upload Config": "上传配置", "Upload File": "上传文件", "Upload Key file": "上传密钥文件", "Upload a Google  <a href=\"https://rclone.org/googlecloudstorage/#service-account-support\"  target=\"_blank\">Service Account credential file</a>. The file is  created with the  <a href=\"https://console.cloud.google.com/apis/credentials\"  target=\"_blank\">Google Cloud Platform Console</a>.": "上传Google <a href=\"https://rclone.org/googlecloudstorage/\\#service-account-support\"  target=\"_blank\">服务帐户凭据文件</a>", "Upload an installer image file": "上传安装镜像文件", "Uploading file...": "正在上传文件...", "Upsmon will wait up to this many seconds in master mode for the slaves to disconnect during a shutdown situation.": "在主模式下，Upsmon将等待这么多秒，以便在关机情况下从站断开连接。", "Uptime": "运行时间", "Usable Capacity": "可用容量", "Usage": "使用率", "Usage collection": "使用情况收集", "Usages": "用法", "Use  <a href=\"https://rclone.org/crypt/\" target=\"_blank\">rclone crypt</a>  to manage data encryption during <i>PUSH</i> or <i>PULL</i> transfers:<br><br>  <i>PUSH:</i> Encrypt files before transfer and store the encrypted  files on the remote system. Files are encrypted using the  <b>Encryption Password</b> and <b>Encryption Salt</b> values.<br><br>  <i>PULL:</i> Decrypt files that are being stored on the remote system  before the transfer. Transferring the encrypted files requires entering  the same <b>Encryption Password</b> and <b>Encryption Salt</b> that was  used to encrypt the files.<br><br>  Additional details about the encryption algorithm and key derivation  are available in the  <a href=\"https://rclone.org/crypt/#file-formats\" target=\"_blank\">rclone crypt File formats documentation</a>.": "在<i>推送</i>或<i>拉取</i>期间使用<a href=\"https://rclone.org/crypt/\" target=\"_blank\">rclone crypt</a>管理数据加密传输：<br><br><i>推送：</i>在传输前先加密文件，然后将加密的文件存储在远程系统上。使用<b>加密密码</b>和<b>加密盐值</b>对文件进行加密。<br> <br><i>拉取：</i>在传输之前，解密存储在远程系统上的文件。传输加密文件需要输入用于加密文件的相同<b>加密密码</b>和<b>加密盐值</b>。<br> <br>有关加密算法和密钥的其他详细信息，请参见<a href=\"https://rclone.org/crypt/\\#file-formats\" target=\"_blank\">rclone crypt文件格式文档</a>。", "Use --fast-list": "使用 --fast-list", "Use <i>Window</i> to extend the validity of passwords beyond the <i>Interval</i> setting. For example, a window setting of 1 means that one password before and after the current one is valid. leaving three valid passwords. Extending the window can be useful in high-latency situations. IMPORTANT: Two-factor authentication is time-based and requires that the system time is set correctly.": "使用<i> Window </i>将密码的有效性扩展到<i> Interval </i>设置之外。例如，Window设置为1表示当前密码前后的一个密码有效。留下三个有效的密码。扩展Window在高延迟情况下很有用。重要说明：双重身份验证是基于时间的，要求正确设置系统时间。", "Use Apple-style Character Encoding": "使用Apple-style的字符编码", "Use DHCP. Unset to manually configure a static IPv4 connection.": "使用DHCP。未设置为手动配置静态IPv4连接。", "Use Default Domain": "使用默认域", "Use FQDN for Logging": "使用全称域名进行日志记录", "Use HTTPS for the connection to the  <b>CheckIP Server</b>.": "使用HTTPS连接到<b> CheckIP服务器</b>。", "Use HTTPS for the connection to the server that updates  the DNS record.": "使用HTTPS连接到更新DNS记录的服务器。", "Use Signature Version 2": "使用签名版本2", "Use Syslog Only": "仅使用系统日志", "Use System Dataset": "使用系统数据集", "Use all disk space": "使用所有的磁盘空间", "Use an SSL certificate that was created or imported in  <b>System > Certificates</b> for secure S3 connections.": "使用在<b>系统>证书</b>中创建或导入的SSL证书进行安全的S3连接。", "Use an exported encryption key file to unlock datasets.": "使用导出的加密密钥文件来解锁数据集。", "Use as Home Share": "用作家庭共享", "Use compressed WRITE records to make the  stream more efficient. The destination system must also support  compressed WRITE records. See  <a href=\"https://linux.die.net/man/8/zfs\"  target=\"_blank\">zfs(8)</a>.": "使用压缩的 WRITE 记录使流更有效率。 目标系统还必须支持压缩的 WRITE 记录。 请参阅 <a href=\"https://linux.die.net/man/8/zfs\" target=\"_blank\">zfs(8)</a>。", "Use existing disk image": "使用已有磁盘镜像", "Use settings from a saved replication.": "使用已保存复制中的设置。", "Use snapshot <i>{snapshot}</i> to roll <b>{dataset}</b> back to {datetime}?": "使用快照 <i>{snapshot}</i> 将 <b>{dataset}</b> 回滚到 {datetime}？", "Use the <i>Log In to GMail</i> button to obtain the credentials for this form.": "使用<i>登录GMail</i>按钮获取此表单的凭据。", "Use the KMIP server to manage ZFS encrypted dataset keys. The key server stores, applies, and destroys encryption keys whenever an encrypted dataset is created, when an existing key is modified, an encrypted dataset is unlocked, or an encrypted dataset is removed. Unsetting this option leaves all encryption key management with the local system.": "使用KMIP服务器来管理ZFS加密的数据集密钥。每当创建加密数据集，修改现有密钥，解锁加密数据集或删除加密数据集时，密钥服务器都会存储，应用和销毁加密密钥。取消设置此选项会将所有加密密钥管理留在本地系统上。", "Use the encryption properties of the root dataset.": "使用根数据集的加密属性。", "Use the format <i>A.B.C.D/E</i> where <i>E</i> is the CIDR mask.": "使用格式<i>ABCD/E</i> ，其中<i>E</i>是CIDR掩码。", "Use this form to set up Two-Factor Authentication for this system.  Then link the system to an authenticator app (such as Google Authenticator, LastPass Authenticator, etc.)  on a mobile device.": "使用此表单为该系统设置“双重身份验证”。然后，将系统链接到移动设备上的身份验证器应用程序（例如Google Authenticator，LastPass Authenticator等）。", "Use this option to allow legacy SMB clients to connect to the  server. Note that SMB1 is being deprecated and it is advised  to upgrade clients to operating system versions that support  modern versions of the SMB protocol.": "此选项允许旧版本的 SMB 客户端连接到服务器。请注意，SMB1 即将被弃用，建议升级客户端到支持当前版本的操作系统。", "Used": "已使用", "Used Ports": "使用的端口", "Used Space": "已用空间", "Used by clients in PASV mode. A default of <i>0</i> means any port above 1023.": "由客户端在PASV模式下使用。默认值<i> 0 </i>表示1023以上的任何端口。", "Used to add additional <a href=\"https://linux.die.net/man/8/proftpd\" target=\"_blank\">proftpd(8)</a> parameters.": "用于添加其他<a href=\"https://linux.die.net/man/8/proftpd\" target=\"_blank\">proftpd（8）</a>参数。", "User": "用户", "User Bind Path": "用户绑定路径", "User CN": "用户CN", "User Data Quota ": "用户数据配额", "User Distinguished Name (DN) to use for authentication.": "用于身份验证的用户可分辨名称（DN）。", "User Domain": "用户域", "User Guide": "用户指南", "User ID": "用户ID", "User ID and Groups": "用户ID和组", "User ID to log in - optional - most swift systems use user and leave this blank <a href=\"https://rclone.org/swift/#standard-options\" target=\"_blank\">(rclone documentation)</a>.": "登录的用户ID-可选-大多数Swift System使用用户并将此空白保留为<a href=\"https://rclone.org/swift/\\#standard-options\" target=\"_blank\">（rclone文档 ）</a>。", "User Name": "用户名", "User Obj": "用户对象", "User Object Quota": "用户对象配额", "User Quotas": "用户配额", "User Settings": "用户设置", "User account to create for CHAP authentication with the user on the remote system. Many initiators use the initiator name as the user name.": "与远程系统上的用户一起创建用于CHAP身份验证的用户帐户。许多启动器使用启动器名称作为用户名。", "User account to which this ACL entry applies.": "此ACL条目适用的用户帐户。", "User accounts have an ID greater than 1000 and system accounts have an ID equal to the default port number used by the service.": "用户帐户的ID大于1000，系统帐户的ID等于服务使用的默认端口号。", "User and Group Ownership": "用户和组所有权", "User deleted": "用户已删除", "User domain - optional <a href=\"https://rclone.org/swift/#standard-options\" target=\"_blank\">(rclone documentation)</a>.": "用户域-可选的<a href=\"https://rclone.org/swift/\\#standard-options\" target=\"_blank\">（rclone文档）</a>。", "User passed to <i>camcontrol security -u</i> to unlock SEDs": "用户传递给<i>camcontrol security -u</i>以解锁SED", "User password. Must be at least 12 and no more than 16 characters long.": "用户密码。必须至少有12个字符，最长不超过16个字符。", "User to run as during file transfers to and from  this module.": "用户在与该模块之间进行文件传输期间的运行方式。", "User who controls the dataset. This user always has permissions to read or write the ACL and read or write attributes. Users created manually or imported from a directory service appear in the drop-down menu.": "控制数据集的用户。该用户始终有权读取或写入ACL以及读取或写入属性。手动创建或从目录服务导入的用户将显示在下拉菜单中。", "User-defined string used to decrypt the dataset. Can be used instead of an encryption key.<br>      WARNING: the passphrase is the only means to decrypt the information stored in this dataset. Be sure to create a      memorable passphrase or physically secure the passphrase.": "用户定义的字符串，用于解密数据集。可以代替加密密钥使用。<br>警告：密码短语是解密存储在此数据集中的信息的唯一方法。确保创建一个难忘的密码短语或在物理上安全的密码短语。", "Username": "用户名", "Username for logging in to the provider and  updating the record.": "登录提供者并更新记录的用户名。", "Username for this service.": "此服务的用户名。", "Username of the SNMP  <a href=\"http://snmplabs.com/pysnmp/docs/api-reference.html#pysnmp.hlapi.UsmUserData\"  target=\"_blank\">User-based Security Model (USM)</a> user.": "SNMP的用户名<a href=\"http://snmplabs.com/pysnmp/docs/api-reference.html\\#pysnmp.hlapi.UsmUserData\"  target=\"_blank\">基于用户的安全模型(USM)</a>用户。", "Username or Password is incorrect.": "用户名或密码不正确。", "Username, Password, or 2FA Code is incorrect.": "用户名，密码或2FA码不正确。", "Usernames can be up to 16 characters long. When using NIS or other legacy software with limited username lengths, keep usernames to eight characters or less for compatibility.": "用户名最长为16个字符。当使用用户名长度受限的NIS或其他传统软件时，为了保持兼容性，用户名应保持在8个字符以内。", "Usernames can be up to 16 characters long. When using NIS or other legacy software with limited username lengths, keep usernames to eight characters or less for compatibility. Usernames cannot begin with a hyphen (<i>-</i>) or contain a space, tab, or these characters: <i>, : + & # % ^ ( ) ! @ ~ * ? < > =</i>. <i>$</i> can only be used as the last character of the username.": "用户名最长为16个字符。当使用用户名长度受限的NIS或其他传统软件时，为了保持兼容性，用户名应保持在8个字符以内。 用户名不能以连字符（<i>-</i>）开头或包含空格，制表符或以下字符：<i>, : + & \\# % ^ ( ) ! @ ~ *? < > =</i>。<i> $ </i>只能用作用户名的最后一个字符。", "Users": "用户", "Users could not be loaded": "无法加载用户", "Uses the SMB Service <i>NetBIOS Name</i> to advertise  the server to WS-Discovery clients. This causes the computer appear in  the <i>Network Neighborhood</i> of modern Windows OSes.": "使用SMB服务<i> NetBIOS名称</i>将服务器通告给WS-Discovery客户端。这会导致计算机出现在Windows操作系统的<i>网上邻居</i>中。", "Using pool {name}": "使用池 {name}", "Using this option will replicate all snapshots  which names match specified regular expression. The  performance on the systems with large number of snapshots  will be lower, as snapshots metadata needs to be read in order  to determine snapshots creation order.": "使用此选项将复制名称与指定正则表达式匹配的所有快照。具有大量快照的系统的性能会降低，因为需要读取快照元数据以确定快照创建顺序。", "Using this option will replicate all snapshots which names match specified regular expression. The performance on the systems with large number of snapshots will be lower, as snapshots metadata needs to be read in order to determine snapshots creation order.": "使用此选项将复制名称与指定正则表达式匹配的所有快照。具有大量快照的系统的性能会降低，因为需要读取快照元数据以确定快照创建顺序。", "VLAN Addresses": "VLAN地址", "VLAN ID": "虚拟局域网ID", "VLAN Settings": "虚拟局域网设置", "VLAN Tag": "VLAN 标签", "VLAN interface": "VLAN接口", "VLANs": "虚拟局域网", "VM": "VM", "VM Summary": "虚拟机概览", "VM system time. Default is <i>Local</i>.": "虚拟机系统时间。默认为<i>本地</i>。", "VM updated successfully.": "VM虚拟机更新成功", "VM {vm} devices": "虚拟机 {vm} 设备", "VMWare Sync": "虚拟机同步", "VMware Snapshot": "VMware 快照", "VMware Snapshot Integration": "VMware 快照集成", "VMware Sync": "虚拟机同步", "Validate Certificates": "验证证书", "Validate Connection": "验证连接", "Validate Remote Path": "验证远程路径", "Value": "值", "Value must be a number": "填写的值必须是一个数字", "Value must be a valid email address": "值必须是有效的电子邮件地址", "Value must be a {type}": "填写的值必须是一个 {type}", "Value must be greater than {value}.": "值必须大于 {value}", "Var": "Var", "Variable": "变量", "Vdev": "Vdev", "Vdev successfully extended.": "Vdev 成功扩展。", "Vendor ID": "Vendor ID", "Verbose Logging": "完整日志输出", "Verify": "核实", "Verify Credential": "验证凭据", "Verify Email Address": "确认电子邮件地址", "Verify certificate authenticity.": "验证证书的真实性。", "Version": "版本", "Version Info": "版本信息", "Version to be upgraded to": "要升级到的版本", "Versions": "版本", "Video, < 100ms latency": "视频，延迟小于100毫秒", "Video, < 10ms latency": "视频，延迟小于10毫秒", "View All": "查看全部", "View All S.M.A.R.T. Tests": "查看所有 S.M.A.R.T.测试", "View All Scrub Tasks": "查看所有擦洗任务", "View All Test Results": "查看所有测试结果", "View Catalog": "查看目录", "View Disk Space Reports": "查看磁盘空间报告", "View Enclosure": "查看机柜", "View Import Log": "查看导入日志", "View More": "查看更多", "View Reports": "查看报告", "View/Download CSR": "查看/下载CSR", "View/Download Certificate": "查看/下载证书", "View/Download Key": "查看/下载密钥", "Virtual CPUs": "虚拟处理器", "Virtual IP Address (Failover Address)": "虚拟IP地址（故障转移地址）", "Virtual Machines": "虚拟机", "Virtual machines cannot be stored in an unmounted mountpoint: {datastore}": "虚拟机不能存储在已卸载的挂载点：{datastore}", "Virtualization": "虚拟化", "Virtualization is not supported": "不支持虚拟化", "Visible": "可见的", "Volume Size": "卷大小", "Volume size cannot be zero.": "卷大小不能为零。", "WARNING": "警告", "WARNING: A failover will  temporarily interrupt system services.": "警告：故障转移将暂时中断系统服务。", "WARNING: Adding data vdevs with different numbers of disks is not recommended.": "警告：不建议添加具有不同磁盘数量的数据 vdev。", "WARNING: Based on the pool topology, {size} is the minimum recommended record size. Choosing a smaller size can reduce system performance.": "警告：根据池拓扑，{size} 是建议的最小记录大小。选择较小的尺寸会降低系统性能。", "WARNING: Exporting/disconnecting pool <i>{pool}</i>. Data on the pool will not be available after export. Data on the pool disks can be destroyed by setting the <b>Destroy data</b> option. Back up critical data <b>before</b> exporting/disconnecting the pool.": "警告：正在导出/断开池 <i>{pool}</i>。 导出后将无法使用池中的数据。 可以通过设置 <b>Destroy data</b> 选项来销毁池磁盘上的数据。 <b>在</b>导出/断开连接池之前备份关键数据。", "WARNING: Keys for all nested datasets with encryption applied will be downloaded.": "警告：将下载应用加密的所有嵌套数据集的密钥。", "WARNING: Only the key for the dataset in question will be downloaded.": "警告：只会下载相关数据集的密钥。", "WARNING: These unknown processes will be terminated while exporting the pool.": "警告：这些未知进程将在导出池时终止。", "Wait to start VM until VNC client connects.": "等待启动虚拟机，直到VNC客户端连接。", "Waiting": "等待中", "Waiting for Active TrueNAS controller to come up...": "在等待TrueNAS系统启动...", "Warning": "警告", "Warning: There are {n} USB disks available that have non-unique serial numbers. USB controllers may report disk serial incorrectly, making such disks indistinguishable from each other. Adding such disks to a pool can result in lost data.": "警告：有 {n} 个 USB 磁盘具有非唯一序列号。 USB 控制器可能会错误地报告磁盘序列，从而使这些磁盘彼此无法区分。将此类磁盘添加到池中可能会导致数据丢失。", "Warning: There are {n} disks available that have non-unique serial numbers. Non-unique serial numbers can be caused by a cabling issue and adding such disks to a pool can result in lost data.": "警告：有 {n} 个可用的磁盘具有非唯一的序列号。非唯一序列号可能是由布线问题引起的，将此类磁盘添加到池中可能会导致数据丢失。", "Warning: iSCSI Target is already in use.</font><br>": "警告：iSCSI目标已在使用中。</font><br>", "Warning: {n} of {total} boot environments could not be deleted.": "警告：无法删除 {n} 个，共 {total} 个引导环境。", "Warning: {n} of {total} docker images could not be deleted.": "警告：无法删除 {n} 个，共 {total} 个 docker 图像。", "Warning: {n} of {total} docker images could not be updated.": "警告：无法更新 {n} 个 docker 镜像，共 {total} 个。", "Warning: {n} of {total} snapshots could not be deleted.": "警告 {n} 的 {total} 无法删除快照。", "Weak Ciphers": "弱密码", "Web Interface": "Web界面", "Web Interface HTTP -> HTTPS Redirect": "Web界面 HTTP -> HTTPS 重定向", "Web Interface HTTP Port": "Web界面HTTP端口", "Web Interface HTTPS Port": "Web界面HTTPS端口", "Web Interface IPv4 Address": "Web界面IPv4地址", "Web Interface IPv6 Address": "Web界面IPv6地址", "WebDAV": "WebDAV", "WebDAV Configuration": "WebDAV配置", "WebDAV Service": "WebDAV 服务", "WebDAV Share": "WebDAV 共享", "WebDAV account password.": "WebDAV账户密码。", "WebDAV account username.": "WebDAV账户用户名。", "Webdav": "Webdav", "Webdav Password": "WebDav密码", "Webdav SSL Certificate": "WebDav SSL证书", "Webhook URL": "Webhook网址", "Wed": "周三", "Wednesday": "周三", "Week(s)": "周", "Weeks": "周数", "What and Where": "做什么和在哪里", "When": "什么时候", "When <code>tls_crypt_auth_enabled</code> is enabled and <code>tls_crypt_auth</code> is not provided,  a static key is automatically generated to be used with OpenVPN client.": "启用<code>tls_crypt_auth_enabled</code>且未提供<code>tls_crypt_auth</code>时，将自动生成一个静态密钥以用于OpenVPN客户端。", "When <i>Syslog Server</i> is defined, only logs matching this level are sent.": "当<i>Syslog Server</i>被定义时，只发送匹配这个级别的日志。", "When a group is selected, the <i>root</i> user is also limited to the permissions of that group.": "当选择一个组时，<i> root </i>用户也被限制为该组的权限。", "When a user is selected, the <i>root</i> user is limited to the permissions of that user.": "选择用户后，<i>root</i>用户将受限于该用户的权限。", "When battery power is low or the shutdown timer ends, enter the custom command to overrule the default shutdown command.": "当电池电量不足或关机定时器结束时，输入自定义命令以否决默认关机命令。", "When both days of month and days of week have restrictions, these restrictions work as an OR condition.": "当月份和星期几都有限制时，这些限制作为 OR 条件起作用。", "When checked it will ensure that the guest always has access to a video device. For headless installations like ubuntu server this is required for the guest to operate properly. However for cases where consumer would like to use GPU passthrough and does not want a display device added should uncheck this.": "选中后，它将确保客机始终可以访问视频设备。对于像 Ubuntu 服务器这样的无头安装，这是客机正常运行所必需的。但是，对于消费者希望使用 GPU 直通且不希望添加显示设备的情况，应取消选中此项。", "When checked, the primary group membership is fetched from the LDAP  attributes (gidNumber). When not checked, the primary group membership is calculated via  the \"primaryGroupID\" LDAP attribute.": "选中后，将从LDAP属性（gidNumber）获取主要组成员身份。如果未选中，则主要组成员通过\"主要组ID\"LDAP属性进行计算。", "When checked, winbind will retrieve the login shell and home directory  from the LDAP attributes. When not checked or when the AD LDAP entry lacks the SFU attributes  the smb4.conf parameters <code>template shell</code> and <code>template homedir</code> are used.": "选中后，winbind将从LDAP属性中检索登录shell和主目录。如果未选中或AD LDAP条目缺少SFU属性，则使用smb4.conf参数<code> template shell </ code>和<code> template homedir </code>。", "When number of vcpus is equal to number of cpus in CPU set vcpus can be automatically pinned into CPU set. Pinning is done by mapping each vcpu into single cpu number in following the order in CPU set. This will improve CPU cache locality and can reduce possible stutter in GPU passthrough VMs.": "当 vcpus 的数量等于 CPU 集中的 cpu 数量时，vcpus 可以自动固定到 CPU 集中。 固定是通过按照 CPU 集中的顺序将每个 vcpu 映射到单个 cpu 编号来完成的。 这将改善 CPU 缓存局部性，并可以减少 GPU 直通 VM 中可能出现的卡顿。", "When replicated snapshots are deleted  from the destination system: <br>  <i>Same as Source</i>: use the configured <i>Snapshot Lifetime</i>  value from the source dataset periodic snapshot task.<br>  <i>Never Delete</i>: never delete snapshots from the destination system.<br>  <i>Custom</i>: set a how long a snapshot remains on the destination  system. Enter a number and choose a measure of time from the drop-down.": "从目标系统删除复制的快照时：<br> <i>与源相同</i>：使用源数据集定期快照任务中配置的<i>快照寿命</i>值。<br> <i>从不删除</i>：永不从目标系统中删除快照。<br> <i>自定义</i>：设置快照在目标系统上的保留时间。 输入一个数字，然后从下拉菜单中选择一个时间度量。", "When replicated snapshots are deleted  from the destination system:<ul>  <li><i>Same as Source</i>: use the <b>Snapshot Lifetime</b>  from the source periodic snapshot task.</li>  <li><i>Custom</i>: define a <b>Snapshot Lifetime</b> for the  destination system.</li>  <li><i>None</i>: never delete snapshots from the destination  system.</li>": "从目标系统删除复制的快照时：<ul> <li><i>与源相同</i>：使用源定期快照任务中的 <b>快照生命周期</b>。</li> <li><i>定制</i>：为目标系统自定义一个 <b>快照生命周期</b>。</li> <li><i>无</i>：从不从目标系统中删除快照。</li>", "When set, a local user is only allowed access to their home directory if they are a member of the <i>wheel</i> group.": "设置后，如果本地用户是<i>wheel</i>组的成员，则只允许访问其主目录。", "When set, report CPU usage in percent  instead of units of kernel time.": "设置后，以百分比而不是内核时间为单位报告CPU使用率。", "When set, rsync is run recursively, preserving                symlinks, permissions, modification times, group,                and special files. When run as root, owner, device                files, and special files are also preserved.                Equivalent to passing the flags <i>-rlptgoD</i> to                rsync.": "设置后，rsync递归运行，保留符号链接，权限，修改时间，组和特殊文件。 当以root身份运行时，所有者，设备文件和特殊文件也将保留。等效于将标志<i>-rlptgoD</i>传递给rsync。", "When set, the common name in the certificate must match the FQDN of the host.": "设置后，证书中的公用名必须与主机的全称域名匹配。", "When set, usernames do not include a domain name. Unset to force domain names to be prepended to user names. One possible reason for unsetting this value is to prevent username collisions when Allow Trusted Domains is set and there are identical usernames in more than one domain.": "设置后，用户名不包含域名。未设置为强制将域名放在用户名之前。取消设置此值的一个可能原因是，当设置了“允许信任的域”并且一个以上的域中有相同的用户名时，防止用户名冲突。", "When this checkbox is checked, domain is inherited from DHCP.": "选中此复选框时，域从 DHCP 继承。", "When using a proxy, enter the proxy information for  the network in the format <i>http://my.proxy.server:3128</i> or  <i>http://user:<EMAIL>:3128</i>": "使用代理时，请以格式<i>http://my.proxy.server:3128</i> 或  <i>http://user:<EMAIL>:3128</i>输入网络的代理信息。", "When using a virtual host, this is also  used as the Kerberos principal name.": "使用虚拟主机时，它也用作Kerberos主体名称。", "Who": "谁", "Who this ACL entry applies to, shown as a <a href=\"https://docs.microsoft.com/en-us/windows/win32/secauthz/security-identifiers\" target=\"_blank\">Windows Security Identifier</a>. Either a <i>SID</i> or a <i>Domain</i> and <i>Name</i> is required for this ACL.": "此ACL条目适用于谁，显示为<a href=\"https://docs.microsoft.com/en-us/windows/win32/secauthz/security-identifiers\" target=\"_blank\"> Windows 安全标识符</a>。此ACL需要<i> SID </ i>或<i> 域 </ i>和<i> 名称 </ i>。", "Who this ACL entry applies to, shown as a user name. Requires adding the user <i>Domain</i>.": "此ACL条目适用于谁，显示为用户名。需要添加用户<i>域</i>。", "Widget Base Class": "窗口小部件基类", "Widget data is not available": "小部分数据不可用", "Will be automatically destroyed at {datetime} by periodic snapshot task": "将在 {datetime} 被周期性快照任务自动销毁", "Will not be destroyed automatically": "不会自动销毁", "Winbind NSS Info": "Winbind NSS 信息", "Window": "Window", "Windows (SMB) Shares": "Windows (SMB)共享", "Wipe": "擦除", "Wipe Disk {name}": "擦除磁盘 {name}", "Wipe this disk?": "擦除这个磁盘？", "Wiping disk...": "正在擦除磁盘...", "With your selection, no GPU is available for the host to consume.": "根据您的选择，没有 GPU 可供主机使用。", "Wizard": "向导", "Workgroup": "工作组", "Would you like to restart the SMB Service?": "是否要重启 SMB 服务？", "Write": "写入", "Write ACL": "写ACL", "Write Attributes": "写属性", "Write Data": "写数据", "Write Errors": "写入错误", "Write Named Attributes": "写入命名属性", "Write Only": "只写", "Write Owner": "写所有者", "Xen initiator compat mode": "Xen Initiator 兼容模式", "Yandex  <a href=\"https://tech.yandex.com/direct/doc/dg-v4/concepts/auth-token-docpage/\"  target=\"_blank\">Access Token</a>.": "Yandex <a href=\"https://tech.yandex.com/direct/doc/dg-v4/concepts/auth-token-docpage/\"  target=\"_blank\">访问令牌</a> 。", "Year(s)": "年", "Years": "年份", "Yes": "是", "Yes I understand the risks": "是的，我了解风险", "You are trying to open:<br>\n{url}<br><br>\nBecause HTTP to HTTPS redirect is enabled in settings your browser will force HTTPS connection for this URL.<br>\nThis may create issues if app does not support secure connections.<br>\n<br>\nYou can try opening app url in an incognito mode.<br>\nAlternatively you can disable redirect in Settings, clear browser cache and try again.": "您正在尝试打开：<br>\n{url}<br><br>\n因为在设置中启用了 HTTP 到 HTTPS 重定向，您的浏览器将强制为此 URL 进行 HTTPS 连接。<br>\n如果应用程序可能会出现问题 不支持安全连接。<br>\n<br>\n您可以尝试以隐身模式打开应用网址。<br>\n您也可以在“设置”中禁用重定向，清除浏览器缓存并重试。", "You can clear reporting database and start data collection immediately.": "您可以立即清除报告数据库并开始数据收集。", "You can join the <a href=\"https://www.truenas.com/newsletter/\" target=\"_blank\" class=\"external-link\">TrueNAS Newsletter</a> for monthly updates and latest developments.": "您可以加入 <a href=\"https://www.truenas.com/newsletter/\" target=\"_blank\" class=\"external-link\">TrueNAS Newsletter</a> 以获取每月更新 和最新进展。", "You can search both for local groups as well as groups from Active Directory.Press ENTER to separate entries.": "您可以搜索本地组以及 Active Directory 中的组。按 ENTER 键分隔条目。", "You can search both for local users as well as users from Active Directory.Press ENTER to separate entries.": "您既可以搜索本地用户，也可以搜索 Active Directory 中的用户。按 ENTER 键分隔条目。", "You have changed one of the settings that require Kubernetes cluster re-initialization. Please be aware that this will delete installed apps and their data.": "您已更改需要Kubernetes集群重新初始化的设置之一。请注意，这将删除已安装的应用程序及其数据。", "You have hidden all of your available widgets. Use the dashboard configuration form to add widgets.": "您已经隐藏了所有可用的小部件。使用仪表板配置表单添加小部件。", "You have left the domain.": "您已离开域。", "Your NAS time {datetime} does not match your computer time.": "您的 NAS 时间 {datetime} 与您的计算机时间不匹配。", "Your query didn't return any results. Please try again.": "您的查询未返回任何结果。请再试一次。", "ZFS": "ZFS", "ZFS Cache": "ZFS缓存", "ZFS Deduplication": "ZFS重复数据删除", "ZFS Encryption": "ZFS 加密", "ZFS Filesystem": "ZFS文件系统", "ZFS Health": "ZFS 健康", "ZFS Info": "ZFS 信息", "ZFS L2ARC read-cache that can be used with fast devices to accelerate read operations. Optional vdev that can be removed.": "ZFS L2ARC读取缓存可与快速设备一起使用以加快读取操作。可以删除的可选vdev。", "ZFS LOG device that can improve speeds of synchronous writes. Optional write-cache that can be removed.": "可以提高同步写入速度的ZFS LOG设备。可以删除的可选写缓存。", "ZFS pools must conform to strict naming  <a href=\"https://docs.oracle.com/cd/E23824_01/html/821-1448/gbcpt.html\" target=\"_blank\">conventions</a>.  Choose a memorable name.": "ZFS池必须遵循严格的命名<a href=\"https://docs.oracle.com/cd/E23824_01/html/821-1448/gbcpt.html\" target=\"_blank\">约定</a>。选择一个能记住的名字。", "ZFS/SED keys synced between KMIP Server and TN database.": "ZFS/SED 密钥在 KMIP 服务器和 TN 数据库之间同步。", "Zoom In": "放大", "Zoom Out": "缩小", "Zvol": "Zvol", "Zvol Details": "Zvol 详细信息", "Zvol Location": "Zvol位置", "Zvol Space Management": "Zvol 空间管理", "Zvol name": "Zvol名称", "[Use fewer transactions in exchange for more RAM.](https://rclone.org/docs/#fast-list) This can also speed up or slow down the transfer.": "[使用更少的交易来换取更多的RAM。]（https://rclone.org/docs/\\#fast-list）这也可以加快或减慢传输速度。", "below to confirm.": "下方确认。", "by ancestor": "由上层", "bytes.": "字节。", "cores": "核心", "details": "详细信息", "everyone@": "每个人@", "expires in {n, plural, one {# day} other {# days} }": "过期日期在 {n, plural, 1 {# day} other {# days} }", "group@": "团队@", "gzip (default level, 6)": "gzip（默认级别，6）", "gzip-1 (fastest)": "gzip-1（最快）", "gzip-9 (maximum, slow)": "gzip-9（最大，慢）", "iSCSI": "iSCSI", "iSCSI Group": "iSCSI组", "iSCSI listen port": "iSCSI 监听端口", "iSCSI supports multiple  authentication methods that are used by the target to discover valid  devices. <i>None</i> allows anonymous discovery while <i>CHAP</i> and  <i>Mutual CHAP</i> require authentication.": "iSCSI支持目标使用的多种身份验证方法来发现有效的设备。<i>无</i>允许匿名发现，而<i> CHAP </i>和<i>相互CHAP </i>需要身份验证。", "iXsystems does not audit or otherwise validate the contents of third-party applications catalogs.     It is incumbent on the user to verify that the new catalog is from a trusted source and that the third-party     properly audits its chart contents. Failure to exercise due diligence may expose the user and their data to     some or all of the following:<br/>    <ul>\n      <li>Malicious software</li>\n      <li>Broken services on TrueNAS host</li>\n      <li>Service disruption on TrueNAS host</li>\n      <li>Broken filesystem permissions on Host or within application</li>\n      <li>Unexpected deletion of user data</li>\n      <li>Unsafe service configuration in application</li>\n      <li>Degradation of TrueNAS host performance and stability</li>\n    </ul>": "iXsystems 不会审核或以其他方式验证第三方应用程序目录的内容。 用户有责任验证新目录是否来自受信任的来源，并且第三方正确审核其图表内容。 不进行尽职调查可能会使用户及其数据暴露于以下部分或全部：<br/> <ul>\n <li>恶意软件</li>\n <li>TrueNAS 主机上的服务中断</ li>\n <li>TrueNAS 主机上的服务中断</li>\n <li>主机或应用程序内的文件系统权限损坏</li>\n <li>意外删除用户数据</li>\n < li>应用程序中不安全的服务配置</li>\n <li>TrueNAS主机性能和稳定性下降</li>\n </ul>", "lz4 (fastest)": "lz4（最快）", "lz4 (recommended)": "lz4（推荐）", "lzjb (legacy, not recommended)": "lzjb（遗留，不推荐）", "mountd(8) bind port": "mountd(8) 绑定端口", "on this enclosure": "在这个机柜上", "or": "或", "overview": "概述", "owner@": "所有者@", "pbkdf2iters": "pbkdf2iters", "pigz (all rounder)": "pigz（全面）", "plzip (best compression)": "plzip（最佳压缩）", "rpc.lockd(8) bind port": "rpc.lockd(8) 绑定端口", "rpc.statd(8) bind port": "rpc.statd(8) 绑定端口", "seconds unless SAVE CHANGES is chosen to make them permanent.": "秒，除非选择 保存更改 使它们永久化。", "selected": "选择的", "threads": "线程", "total": "总", "total available": "总可用", "vdev": "vdev", "vdev is highly discouraged and will result in data loss if it fails": "非常不鼓励使用这种 vdev，如果它发生故障将导致数据丢失。", "was successfully attached.": "已成功附加。", "zle (runs of zeros)": "<PERSON><PERSON>（一连串零）", "zstd (default level, 3)": "zstd（默认级别，3）", "zstd-5 (slow)": "zstd-5（慢）", "zstd-7 (very slow)": "zstd-7（很慢）", "zstd-fast (default level, 1)": "zstd-fast（默认级别，1）", "{ n, plural, one {# snapshot} other {# snapshots} }": "{n, plural, other{#个快照}}", "{duration} remaining": "{duration} 其它的", "{eligible} of {total} existing snapshots of dataset {targetDataset} would be replicated with this task.": "此任务将复制数据集 {targetDataset} 的 {total} 个现有快照中的 {eligible}。", "{field} is required": "{field} 是必需的", "{field} is required.": "{field} 是必需的。", "{interfaceName} must start with \"{prefix}\" followed by an unique number": "{interfaceName} 必须从 \"{prefix}\" 开头，后跟一个唯一的编号", "{key} Key": "{key} 键", "{minute, plural, one {# minute} other {# minutes}}": "{minute, plural, other {# 分钟}}", "{n, plural, =0 {No Errors} one {# Error} other {# Errors}}": "{n, plural, =0 {No Errors} 1 {# Error} other {# Errors}}", "{n, plural, =0 {No Tasks} one {# Task} other {# Tasks}} Configured": "{n, plural, =0 {No Tasks} 1 {# Task} other {# Tasks}} 配置", "{n, plural, one {# boot environment} other {# boot environments}} has been deleted.": "{n, plural, 1 {# boot environment} other {# boot environments}} 已被删除", "{n, plural, one {# docker image} other {# docker images}} has been deleted.": "{n, plural, 1 {# docker image} other {# docker images}} 已补删除。", "{n, plural, one {# docker image} other {# docker images}} has been updated.": "{n, plural, 1 {# docker image} other {# docker images}} 已更新。", "{n}": "{n}", "{n} (applies to descendants)": "{n} (适用于子集)", "{n} RPM": "{n} 转速", "{n} from {dataset}": "{n} 来自 {dataset}", "{n} more": "{n} 个以上", "{serviceName} service failed to start.": "{serviceName} 服务未能启动。", "{serviceName} service failed to stop.": "{serviceName} 服务未能停止。", "{this} and {that}. At least one is required": "{this} 和 {that}。至少需要一个", "{uptimeString} as of {dateTime}": "{uptimeString} 截至 {dateTime}", "{used} of {total} ({used_pct})": "{used} 的 {total} ({used_pct})"}