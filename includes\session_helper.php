<?php
/**
 * 会话辅助函数
 * 在所有需要使用会话的文件中引入此文件
 */
function start_secure_session() {
    // 只有在session未启动时才配置
    if (session_status() == PHP_SESSION_NONE) {
        // 使用系统临时目录
        session_save_path(sys_get_temp_dir());

        // 设置安全的会话配置
        ini_set('session.use_only_cookies', 1);
        ini_set('session.use_strict_mode', 1);
        ini_set('session.cookie_httponly', 1);
        ini_set('session.cookie_lifetime', 3600); // 1小时

        // 启动会话
        session_start();

        // 重新生成session ID（安全措施）
        if (!isset($_SESSION['initiated'])) {
            session_regenerate_id(true);
            $_SESSION['initiated'] = true;
        }
    }
}

// 自动启动会话
start_secure_session();