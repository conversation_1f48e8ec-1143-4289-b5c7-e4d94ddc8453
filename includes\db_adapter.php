<?php
/**
 * 数据库适配器
 * 自动检测数据库结构并提供兼容的查询方法
 */

class DatabaseAdapter {
    private $pdo;
    private $has_license_devices = null;
    private $has_skeys_records = null;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->detectTables();
    }
    
    /**
     * 检测数据库中存在的表
     */
    private function detectTables() {
        try {
            // 检查license_devices表是否存在
            $stmt = $this->pdo->query("SHOW TABLES LIKE 'license_devices'");
            $this->has_license_devices = $stmt->rowCount() > 0;
            
            // 检查skeys_records表是否存在
            $stmt = $this->pdo->query("SHOW TABLES LIKE 'skeys_records'");
            $this->has_skeys_records = $stmt->rowCount() > 0;
        } catch (Exception $e) {
            $this->has_license_devices = false;
            $this->has_skeys_records = false;
        }
    }
    
    /**
     * 获取激活码列表（兼容新旧结构）
     */
    public function getLicenses($where_clause = '', $params = []) {
        $sql = "SELECT l.*, p.plan_name FROM licenses l JOIN plans p ON l.plan_id = p.id";
        
        if ($where_clause) {
            $sql .= " WHERE " . $where_clause;
        }
        
        $sql .= " ORDER BY l.id DESC";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        $licenses = $stmt->fetchAll();
        
        // 如果有license_devices表，获取设备信息
        if ($this->has_license_devices) {
            foreach ($licenses as &$license) {
                $device_stmt = $this->pdo->prepare("SELECT * FROM license_devices WHERE license_id = ?");
                $device_stmt->execute([$license['id']]);
                $license['devices'] = $device_stmt->fetchAll();
            }
        } else {
            // 旧结构：模拟devices数组
            foreach ($licenses as &$license) {
                $license['devices'] = [];
                if (!empty($license['device_uid'])) {
                    $license['devices'][] = [
                        'id' => $license['id'], // 使用license_id作为device_id
                        'device_uid' => $license['device_uid'],
                        'status' => $license['status'] === 'active' ? 'active' : 'disabled',
                        'activated_at' => $license['activated_at'],
                        'last_verify_at' => null,
                        'verify_count' => 0
                    ];
                }
            }
        }
        
        return $licenses;
    }
    
    /**
     * 解绑设备（兼容新旧结构）
     */
    public function unbindDevice($device_id) {
        if ($this->has_license_devices) {
            // 新结构：删除license_devices记录
            $stmt = $this->pdo->prepare("DELETE FROM license_devices WHERE id = ?");
            return $stmt->execute([$device_id]);
        } else {
            // 旧结构：清空licenses表的device_uid
            $stmt = $this->pdo->prepare("UPDATE licenses SET device_uid = NULL, status = 'inactive', unbind_count = unbind_count + 1 WHERE id = ?");
            return $stmt->execute([$device_id]);
        }
    }
    
    /**
     * 获取设备统计信息（兼容新旧结构）
     */
    public function getDeviceStats() {
        if ($this->has_license_devices) {
            // 新结构统计
            return $this->pdo->query("
                SELECT
                    (SELECT COUNT(*) FROM license_devices WHERE status = 'active') as total_devices,
                    (SELECT COUNT(DISTINCT license_id) FROM license_devices WHERE status = 'active') as licenses_with_devices,
                    (SELECT COALESCE(AVG(verify_count), 0) FROM license_devices WHERE status = 'active') as avg_verifications
            ")->fetch();
        } else {
            // 旧结构统计
            return $this->pdo->query("
                SELECT
                    (SELECT COUNT(*) FROM licenses WHERE device_uid IS NOT NULL AND status = 'active') as total_devices,
                    (SELECT COUNT(*) FROM licenses WHERE device_uid IS NOT NULL) as licenses_with_devices,
                    (SELECT COALESCE(AVG(verify_count), 0) FROM (
                        SELECT COUNT(*) as verify_count 
                        FROM logs 
                        WHERE log_type = 'verify' AND status = 'SUCCESS' 
                        GROUP BY device_uid
                    ) as avg_calc) as avg_verifications
            ")->fetch();
        }
    }
    
    /**
     * 获取Skeys（兼容新旧结构）
     */
    public function getSkeys($license_key, $device_uid) {
        // 先查找激活码和设备绑定信息
        if ($this->has_license_devices) {
            $stmt = $this->pdo->prepare("
                SELECT l.*, ld.id as device_id, ld.status as device_status
                FROM licenses l 
                JOIN license_devices ld ON l.id = ld.license_id 
                WHERE l.license_key = ? AND ld.device_uid = ? AND ld.status = 'active'
            ");
        } else {
            $stmt = $this->pdo->prepare("
                SELECT l.*, l.status as device_status
                FROM licenses l 
                WHERE l.license_key = ? AND l.device_uid = ? AND l.status = 'active'
            ");
        }
        
        $stmt->execute([$license_key, $device_uid]);
        $result = $stmt->fetch();
        
        if (!$result) {
            throw new Exception('未找到有效的设备绑定记录');
        }
        
        // 检查激活码状态
        if ($result['status'] === 'disabled') {
            throw new Exception('激活码已被禁用');
        }
        
        // 检查是否过期
        $expires_at = new DateTime($result['expires_at']);
        if (new DateTime() > $expires_at) {
            throw new Exception('激活码已过期');
        }
        
        // 生成Skeys
        $d = (int)($expires_at->getTimestamp() * 1000);
        $part2 = ($d + 10000) * 903 / 100000;
        $part1 = md5($device_uid . $d);
        $prstr = "ppmt"; 
        $prnum = "1200";
        $hash1 = md5($prstr . "100000000:00" . $part1 . $prstr . "100000000:00");
        $part3 = md5($hash1 . $prstr . "100000000:00" . $part2 . md5($device_uid) . $prnum);
        $skeys = $part1 . $part2 . $part3;
        
        // 如果有skeys_records表，存储记录
        if ($this->has_skeys_records) {
            try {
                $skeys_stmt = $this->pdo->prepare("INSERT INTO skeys_records (license_key, device_uid, skeys, generated_at, expires_at) VALUES (?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE skeys = VALUES(skeys), generated_at = VALUES(generated_at)");
                $skeys_stmt->execute([$license_key, $device_uid, $skeys, date('Y-m-d H:i:s'), $result['expires_at']]);
            } catch (Exception $e) {
                // 忽略存储错误
            }
        }
        
        return [
            'skeys' => $skeys,
            'expires_at' => $result['expires_at']
        ];
    }
    
    /**
     * 获取系统监控的表列表
     */
    public function getMonitorTables() {
        $tables = ['licenses', 'logs', 'plans'];
        
        if ($this->has_license_devices) {
            $tables[] = 'license_devices';
        }
        
        if ($this->has_skeys_records) {
            $tables[] = 'skeys_records';
        }
        
        return $tables;
    }
    
    /**
     * 检查是否为新数据库结构
     */
    public function isNewStructure() {
        return $this->has_license_devices;
    }
    
    /**
     * 获取数据库结构信息
     */
    public function getStructureInfo() {
        return [
            'has_license_devices' => $this->has_license_devices,
            'has_skeys_records' => $this->has_skeys_records,
            'structure_type' => $this->has_license_devices ? 'new' : 'legacy'
        ];
    }
}
?>
