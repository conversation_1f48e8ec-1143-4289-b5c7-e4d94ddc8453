<?php
/**
 * 修复脚本：为license_devices表添加缺失的device_name字段
 */

require_once 'includes/db.php';

echo "<h2>修复license_devices表结构</h2>";

try {
    // 检查device_name字段是否存在
    echo "<p>检查device_name字段...</p>";
    
    try {
        $pdo->query("SELECT device_name FROM license_devices LIMIT 1");
        echo "<p style='color: green;'>✓ device_name字段已存在，无需修复</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>device_name字段不存在，开始添加...</p>";
        
        // 添加device_name字段
        $alter_sql = "ALTER TABLE `license_devices` ADD COLUMN `device_name` varchar(100) DEFAULT NULL COMMENT '设备名称（可选）' AFTER `device_uid`";
        $pdo->exec($alter_sql);
        
        echo "<p style='color: green;'>✓ 成功添加device_name字段</p>";
    }
    
    // 显示修复后的表结构
    echo "<h3>修复后的license_devices表结构</h3>";
    $result = $pdo->query("DESCRIBE license_devices");
    $fields = $result->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th></tr>";
    foreach ($fields as $field) {
        $highlight = ($field['Field'] === 'device_name') ? 'style="background-color: #ffffcc;"' : '';
        echo "<tr $highlight>";
        echo "<td>" . $field['Field'] . "</td>";
        echo "<td>" . $field['Type'] . "</td>";
        echo "<td>" . $field['Null'] . "</td>";
        echo "<td>" . $field['Key'] . "</td>";
        echo "<td>" . $field['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3 style='color: green;'>修复完成！</h3>";
    echo "<p>现在可以正常使用系统了。</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>修复失败：" . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
th { background-color: #f2f2f2; }
</style>
