<?php
// 检查服务器数据库结构
require_once 'includes/db.php';

echo "<h2>服务器数据库结构检查</h2>";

try {
    // 检查licenses表结构
    echo "<h3>licenses表结构</h3>";
    $result = $pdo->query("DESCRIBE licenses");
    $fields = $result->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th></tr>";
    foreach ($fields as $field) {
        echo "<tr>";
        echo "<td>" . $field['Field'] . "</td>";
        echo "<td>" . $field['Type'] . "</td>";
        echo "<td>" . $field['Null'] . "</td>";
        echo "<td>" . $field['Key'] . "</td>";
        echo "<td>" . $field['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 检查是否存在license_devices表
    echo "<h3>检查license_devices表</h3>";
    try {
        $pdo->query("SELECT 1 FROM license_devices LIMIT 1");
        echo "<p style='color: green;'>✓ license_devices表存在</p>";
        
        $result = $pdo->query("DESCRIBE license_devices");
        $fields = $result->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th></tr>";
        foreach ($fields as $field) {
            echo "<tr>";
            echo "<td>" . $field['Field'] . "</td>";
            echo "<td>" . $field['Type'] . "</td>";
            echo "<td>" . $field['Null'] . "</td>";
            echo "<td>" . $field['Key'] . "</td>";
            echo "<td>" . $field['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ license_devices表不存在</p>";
    }
    
    // 检查数据统计
    echo "<h3>数据统计</h3>";
    $license_count = $pdo->query("SELECT COUNT(*) FROM licenses")->fetchColumn();
    echo "<p>激活码总数：$license_count</p>";
    
    $active_licenses = $pdo->query("SELECT COUNT(*) FROM licenses WHERE device_uid IS NOT NULL AND device_uid != ''")->fetchColumn();
    echo "<p>已绑定设备的激活码：$active_licenses</p>";
    
    // 显示部分数据样例
    echo "<h3>licenses表数据样例（前5条）</h3>";
    $samples = $pdo->query("SELECT id, license_key, device_uid, status, created_at FROM licenses LIMIT 5")->fetchAll();
    
    if (!empty($samples)) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>激活码</th><th>设备UID</th><th>状态</th><th>创建时间</th></tr>";
        foreach ($samples as $sample) {
            echo "<tr>";
            echo "<td>" . $sample['id'] . "</td>";
            echo "<td>" . htmlspecialchars($sample['license_key']) . "</td>";
            echo "<td>" . htmlspecialchars($sample['device_uid'] ?? '未绑定') . "</td>";
            echo "<td>" . $sample['status'] . "</td>";
            echo "<td>" . $sample['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>错误：" . $e->getMessage() . "</p>";
}
?>

<style>
table { border-collapse: collapse; margin: 10px 0; }
th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
th { background-color: #f2f2f2; }
</style>
