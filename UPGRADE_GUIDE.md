# PPMT 激活码管理系统 v2.0 升级指南

## 🚀 升级概述

本次升级包含了大量的功能增强、性能优化和安全改进。请按照以下步骤进行升级。

## ⚠️ 升级前准备

### 1. 备份数据库
```bash
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 2. 备份文件
```bash
cp -r /path/to/ppmt_admin /path/to/ppmt_admin_backup
```

## 📋 升级步骤

### 步骤1: 数据库结构升级

执行以下SQL文件（按顺序）：

1. **多设备支持升级**
```sql
-- 执行 upgrade_license_devices.sql
ALTER TABLE `licenses` ADD COLUMN `max_devices` int(11) NOT NULL DEFAULT 1 COMMENT '该激活码最大可绑定设备数量';

UPDATE `licenses` l 
JOIN `plans` p ON l.plan_id = p.id 
SET l.max_devices = COALESCE(p.max_devices, 1);
```

2. **Skeys存储表**
```sql
-- 执行 upgrade_skeys_storage.sql
CREATE TABLE IF NOT EXISTS `skeys_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `license_key` varchar(50) NOT NULL COMMENT '激活码',
  `device_uid` varchar(64) NOT NULL COMMENT '设备UID',
  `skeys` text NOT NULL COMMENT '生成的Skeys',
  `generated_at` datetime NOT NULL COMMENT '生成时间',
  `expires_at` datetime NOT NULL COMMENT '过期时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `license_device_unique` (`license_key`, `device_uid`),
  KEY `skeys_search` (`skeys`(100))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

3. **性能优化索引**
```sql
-- 执行 upgrade_performance_indexes.sql
CREATE INDEX IF NOT EXISTS idx_licenses_status_plan ON licenses(status, plan_id);
CREATE INDEX IF NOT EXISTS idx_license_devices_license_status ON license_devices(license_id, status);
CREATE INDEX IF NOT EXISTS idx_logs_time_type ON logs(log_time, log_type);
-- ... 更多索引
```

### 步骤2: 文件更新

1. **更新核心文件**
   - 所有PHP文件已更新
   - JavaScript文件已优化
   - CSS样式已改进

2. **新增文件**
   - `config/app.php` - 应用配置
   - `includes/config.php` - 配置管理器
   - `includes/rate_limiter.php` - API频率限制
   - `includes/error_handler.php` - 错误处理器
   - `actions/export_data.php` - 数据导出
   - `scripts/backup.php` - 自动备份
   - `system_monitor.php` - 系统监控

### 步骤3: 配置更新

1. **创建配置目录**
```bash
mkdir -p config
mkdir -p scripts
mkdir -p backups
```

2. **设置权限**
```bash
chmod 755 scripts/backup.php
chmod 777 backups
```

3. **配置定时备份（可选）**
```bash
# 添加到crontab
0 2 * * * /usr/bin/php /path/to/ppmt_admin/scripts/backup.php
```

## 🆕 新功能说明

### 1. 多设备激活码支持
- 每个激活码可以绑定多个设备
- 支持设备级别的解绑操作
- 激活进度显示（如：已激活 3/5）

### 2. Skeys查看功能
- 在操作日志中查看设备当前有效的Skeys
- 支持通过Skeys搜索日志记录
- 一键复制Skeys功能

### 3. 数据导出功能
- 支持CSV和Excel格式导出
- 可导出激活码列表和操作日志
- 支持筛选条件导出

### 4. 系统监控
- 数据库状态监控
- 性能统计分析
- 热门IP地址统计
- 7天趋势分析

### 5. 安全增强
- API频率限制防止滥用
- 统一错误处理和日志记录
- 会话安全改进

### 6. 用户体验优化
- 界面完全中文化
- 表格列宽自定义
- 批量操作改进
- 前缀历史记录选择

## 🔧 配置说明

### 应用配置 (config/app.php)
```php
return [
    'license' => [
        'max_devices_per_license' => 100,  // 单个激活码最大设备数
        'max_licenses_per_batch' => 1000,  // 批量生成最大数量
    ],
    'api' => [
        'rate_limit' => 100,               // API频率限制（每分钟）
    ],
    // ... 更多配置
];
```

### 功能开关
```php
'features' => [
    'enable_skeys_search' => true,        // 启用Skeys搜索
    'enable_api_rate_limit' => true,      // 启用API频率限制
    'enable_batch_operations' => true,    // 启用批量操作
],
```

## 📊 性能优化

### 数据库优化
- 添加了关键索引提升查询性能
- 优化了复杂查询的执行计划
- 支持大数据量的分页查询

### 前端优化
- 优化了DataTables配置
- 改进了AJAX请求处理
- 减少了不必要的DOM操作

## 🛡️ 安全改进

### API安全
- 实施了频率限制防止暴力攻击
- 改进了输入验证和过滤
- 增强了错误信息的安全性

### 会话安全
- 统一了会话管理
- 改进了会话配置
- 增加了会话超时控制

## 🔍 故障排除

### 常见问题

1. **设备解绑不工作**
   - 检查JavaScript控制台是否有错误
   - 确认设备ID正确传递
   - 检查后端update_license.php文件

2. **Skeys查看按钮无响应**
   - 确认skeys_records表已创建
   - 检查JavaScript事件委托
   - 查看浏览器网络请求

3. **导出功能失败**
   - 检查actions/export_data.php文件权限
   - 确认配置文件正确加载
   - 查看PHP错误日志

### 日志查看
```bash
# 查看PHP错误日志
tail -f /var/log/php_errors.log

# 查看Apache/Nginx错误日志
tail -f /var/log/apache2/error.log
```

## 📈 监控和维护

### 系统监控
- 访问 `system_monitor.php` 查看系统状态
- 监控数据库表状态和记录数
- 查看API请求统计和趋势

### 定期维护
- 定期清理过期日志记录
- 监控磁盘空间使用
- 检查备份文件完整性

## 🎯 升级后验证

### 功能测试清单
- [ ] 激活码生成和管理
- [ ] 多设备绑定和解绑
- [ ] 操作日志查看和搜索
- [ ] Skeys查看功能
- [ ] 数据导出功能
- [ ] 系统监控页面
- [ ] API频率限制
- [ ] 批量操作功能

### 性能测试
- [ ] 大量数据下的页面加载速度
- [ ] API响应时间
- [ ] 数据库查询性能

## 📞 技术支持

如果在升级过程中遇到问题，请：

1. 检查错误日志
2. 确认数据库结构正确
3. 验证文件权限设置
4. 测试基本功能是否正常

升级完成后，您的系统将具备企业级激活码管理系统的所有功能！
