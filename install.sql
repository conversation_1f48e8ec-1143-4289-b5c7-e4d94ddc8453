-- --------------------------------------------------------
-- 主机:                         127.0.0.1
-- 服务器版本:                   5.7.33 - MySQL Community Server (GPL)
-- 服务器操作系统:               Win64
-- HeidiSQL 版本:                11.2.0.6213
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;


-- 导出 zhiyudazi 的数据库结构
CREATE DATABASE IF NOT EXISTS `zhiyudazi` /*!40100 DEFAULT CHARACTER SET utf8mb4 */;
USE `zhiyudazi`;

-- 导出  表 zhiyudazi.licenses 结构
CREATE TABLE IF NOT EXISTS `licenses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `license_key` varchar(50) NOT NULL,
  `plan_id` int(11) NOT NULL COMMENT '关联的套餐ID',
  `device_uid` varchar(64) DEFAULT NULL COMMENT '绑定的设备唯一ID',
  `status` enum('inactive','active','expired','disabled') NOT NULL DEFAULT 'inactive',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `activated_at` datetime DEFAULT NULL,
  `expires_at` datetime DEFAULT NULL,
  `last_ip` varchar(45) DEFAULT NULL,
  `unbind_count` int(11) NOT NULL DEFAULT 0 COMMENT '解绑次数',
  `notes` text COMMENT '激活码备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `license_key` (`license_key`),
  KEY `device_uid` (`device_uid`),
  KEY `plan_id` (`plan_id`),
  CONSTRAINT `licenses_ibfk_1` FOREIGN KEY (`plan_id`) REFERENCES `plans` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 数据导出被取消选择。

-- 导出  表 zhiyudazi.logs 结构
CREATE TABLE IF NOT EXISTS `logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `license_key` varchar(50) DEFAULT NULL,
  `device_uid` varchar(64) DEFAULT NULL,
  `log_type` varchar(20) NOT NULL COMMENT '日志类型: activate, verify',
  `ip_address` varchar(45) DEFAULT NULL,
  `status` varchar(50) NOT NULL COMMENT '操作结果: SUCCESS, FAILED_DISABLED, etc.',
  `log_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `license_key` (`license_key`),
  KEY `device_uid` (`device_uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 数据导出被取消选择。

-- 导出  表 zhiyudazi.plans 结构
CREATE TABLE IF NOT EXISTS `plans` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `plan_name` varchar(100) NOT NULL COMMENT '套餐名称，如：1小时卡',
  `validity_value` int(11) NOT NULL COMMENT '有效期数值',
  `validity_unit` enum('hour','day','month','year') NOT NULL COMMENT '有效期单位',
  `notes` varchar(255) DEFAULT NULL COMMENT '套餐备注',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '套餐是否可用',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4;

-- 正在导出表  zhiyudazi.plans 的数据：~9 rows (大约)
/*!40000 ALTER TABLE `plans` DISABLE KEYS */;
INSERT INTO `plans` (`id`, `plan_name`, `validity_value`, `validity_unit`, `notes`, `is_active`) VALUES
	(1, '1小时体验', 1, 'hour', NULL, 1),
	(2, '3小时体验', 3, 'hour', NULL, 1),
	(3, '1天卡', 1, 'day', NULL, 1),
	(4, '7天卡', 7, 'day', NULL, 1),
	(5, '15天卡', 15, 'day', NULL, 1),
	(6, '30天卡 (月卡)', 30, 'day', NULL, 1),
	(7, '90天卡 (季卡)', 90, 'day', NULL, 1),
	(8, '180天卡 (半年卡)', 180, 'day', NULL, 1),
	(9, '365天卡 (年卡)', 365, 'day', NULL, 1);
/*!40000 ALTER TABLE `plans` ENABLE KEYS */;

/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;