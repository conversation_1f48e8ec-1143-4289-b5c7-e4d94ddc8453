<?php
require_once 'includes/session_helper.php';
require_once 'includes/db.php';

// 强制登录
$_SESSION['loggedin'] = true;

echo "<h2>简单解绑测试</h2>";

// 处理解绑请求
if (isset($_POST['unbind_device_id'])) {
    $device_id = (int)$_POST['unbind_device_id'];
    
    echo "<h3>开始解绑设备ID: $device_id</h3>";
    
    try {
        // 开始事务
        $pdo->beginTransaction();
        echo "<p>✓ 事务开始</p>";
        
        // 查询设备信息
        $check_stmt = $pdo->prepare("SELECT license_id, device_uid FROM license_devices WHERE id = ?");
        $check_stmt->execute([$device_id]);
        $device_info = $check_stmt->fetch();
        
        if (!$device_info) {
            echo "<p style='color: red;'>✗ 设备不存在(ID: $device_id)</p>";
            $pdo->rollBack();
        } else {
            echo "<p>✓ 找到设备: " . htmlspecialchars($device_info['device_uid']) . " (激活码ID: " . $device_info['license_id'] . ")</p>";
            
            // 删除设备记录
            $delete_stmt = $pdo->prepare("DELETE FROM license_devices WHERE id = ?");
            $delete_result = $delete_stmt->execute([$device_id]);
            $affected_rows = $delete_stmt->rowCount();
            
            echo "<p>删除结果: " . ($delete_result ? "成功" : "失败") . ", 影响行数: $affected_rows</p>";
            
            if ($delete_result && $affected_rows > 0) {
                // 更新激活码计数
                $update_stmt = $pdo->prepare("UPDATE licenses SET bound_devices = bound_devices - 1, unbind_count = unbind_count + 1 WHERE id = ?");
                $update_result = $update_stmt->execute([$device_info['license_id']]);
                $update_affected = $update_stmt->rowCount();
                
                echo "<p>更新激活码结果: " . ($update_result ? "成功" : "失败") . ", 影响行数: $update_affected</p>";
                
                if ($update_result) {
                    $pdo->commit();
                    echo "<p style='color: green;'><strong>✓ 解绑成功！事务已提交</strong></p>";
                } else {
                    $pdo->rollBack();
                    echo "<p style='color: red;'>✗ 更新激活码失败，事务已回滚</p>";
                }
            } else {
                $pdo->rollBack();
                echo "<p style='color: red;'>✗ 删除设备失败，事务已回滚</p>";
            }
        }
    } catch (Exception $e) {
        $pdo->rollBack();
        echo "<p style='color: red;'>✗ 异常: " . $e->getMessage() . "</p>";
    }
    
    echo "<hr>";
}

// 显示当前设备列表
echo "<h3>当前设备列表</h3>";
try {
    $stmt = $pdo->query("
        SELECT ld.id, ld.device_uid, ld.activated_at, l.id as license_id, l.license_key, l.bound_devices
        FROM license_devices ld 
        JOIN licenses l ON ld.license_id = l.id 
        WHERE l.id = 775
        ORDER BY ld.id
    ");
    $devices = $stmt->fetchAll();
    
    if (empty($devices)) {
        echo "<p>没有找到设备</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>设备记录ID</th><th>设备UID</th><th>激活码ID</th><th>激活码</th><th>当前绑定数</th><th>绑定时间</th><th>操作</th></tr>";
        foreach ($devices as $device) {
            echo "<tr>";
            echo "<td><strong>" . $device['id'] . "</strong></td>";
            echo "<td>" . htmlspecialchars($device['device_uid']) . "</td>";
            echo "<td>" . $device['license_id'] . "</td>";
            echo "<td>" . htmlspecialchars($device['license_key']) . "</td>";
            echo "<td>" . $device['bound_devices'] . "</td>";
            echo "<td>" . $device['activated_at'] . "</td>";
            echo "<td>";
            echo "<form method='post' style='display: inline;'>";
            echo "<input type='hidden' name='unbind_device_id' value='" . $device['id'] . "'>";
            echo "<button type='submit' onclick='return confirm(\"确定解绑设备ID " . $device['id'] . "?\")'>解绑</button>";
            echo "</form>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>查询错误: " . $e->getMessage() . "</p>";
}
?>

<style>
table { border-collapse: collapse; margin: 10px 0; }
th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
th { background-color: #f2f2f2; }
button { 
    padding: 5px 10px; 
    background-color: #dc3545; 
    color: white; 
    border: none; 
    cursor: pointer; 
    border-radius: 3px;
}
button:hover { background-color: #c82333; }
</style>
