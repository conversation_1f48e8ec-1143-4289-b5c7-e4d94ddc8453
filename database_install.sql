-- PPMT 激活码管理系统 v2.0 完整数据库安装脚本
-- 执行前请确保已创建数据库并选择正确的数据库

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ===================================================================
-- 1. 套餐表 (plans)
-- ===================================================================
CREATE TABLE `plans` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `plan_name` varchar(100) NOT NULL COMMENT '套餐名称',
  `duration_days` int(11) NOT NULL COMMENT '有效期天数',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='套餐表';

-- ===================================================================
-- 2. 激活码表 (licenses)
-- ===================================================================
CREATE TABLE `licenses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `license_key` varchar(50) NOT NULL COMMENT '激活码',
  `plan_id` int(11) NOT NULL COMMENT '套餐ID',
  `status` enum('active','inactive','expired','disabled') NOT NULL DEFAULT 'inactive' COMMENT '状态',
  `max_devices` int(11) NOT NULL DEFAULT 1 COMMENT '最大可绑定设备数量',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `activated_at` datetime DEFAULT NULL COMMENT '激活时间',
  `expires_at` datetime NOT NULL COMMENT '到期时间',
  `notes` text DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `license_key` (`license_key`),
  KEY `plan_id` (`plan_id`),
  KEY `idx_status_plan` (`status`, `plan_id`),
  KEY `idx_expires` (`expires_at`),
  KEY `idx_created` (`created_at`),
  KEY `idx_key_status` (`license_key`, `status`),
  CONSTRAINT `licenses_ibfk_1` FOREIGN KEY (`plan_id`) REFERENCES `plans` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='激活码表';

-- ===================================================================
-- 3. 设备绑定表 (license_devices)
-- ===================================================================
CREATE TABLE `license_devices` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `license_id` int(11) NOT NULL COMMENT '激活码ID',
  `device_uid` varchar(64) NOT NULL COMMENT '设备唯一标识',
  `status` enum('active','disabled') NOT NULL DEFAULT 'active' COMMENT '设备状态',
  `activated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
  `last_verify_at` datetime DEFAULT NULL COMMENT '最后验证时间',
  `last_ip` varchar(45) DEFAULT NULL COMMENT '最后验证IP',
  `verify_count` int(11) NOT NULL DEFAULT 0 COMMENT '验证次数',
  PRIMARY KEY (`id`),
  UNIQUE KEY `license_device_unique` (`license_id`, `device_uid`),
  KEY `device_uid` (`device_uid`),
  KEY `idx_license_status` (`license_id`, `status`),
  KEY `idx_uid_status` (`device_uid`, `status`),
  KEY `idx_activated` (`activated_at`),
  KEY `idx_verify` (`last_verify_at`),
  CONSTRAINT `license_devices_ibfk_1` FOREIGN KEY (`license_id`) REFERENCES `licenses` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备绑定表';

-- ===================================================================
-- 4. 操作日志表 (logs)
-- ===================================================================
CREATE TABLE `logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `license_key` varchar(50) DEFAULT NULL COMMENT '激活码',
  `device_uid` varchar(64) DEFAULT NULL COMMENT '设备UID',
  `log_type` enum('activate','verify') NOT NULL COMMENT '操作类型',
  `status` varchar(50) NOT NULL COMMENT '操作结果',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `log_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`id`),
  KEY `license_key` (`license_key`),
  KEY `device_uid` (`device_uid`),
  KEY `idx_time_type` (`log_time`, `log_type`),
  KEY `idx_license_device` (`license_key`, `device_uid`),
  KEY `idx_ip_time` (`ip_address`, `log_time`),
  KEY `idx_status_time` (`status`, `log_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';

-- ===================================================================
-- 5. Skeys记录表 (skeys_records) - 用于搜索功能
-- ===================================================================
CREATE TABLE `skeys_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `license_key` varchar(50) NOT NULL COMMENT '激活码',
  `device_uid` varchar(64) NOT NULL COMMENT '设备UID',
  `skeys` text NOT NULL COMMENT '生成的Skeys',
  `generated_at` datetime NOT NULL COMMENT '生成时间',
  `expires_at` datetime NOT NULL COMMENT '过期时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `license_device_unique` (`license_key`, `device_uid`),
  KEY `skeys_search` (`skeys`(100)),
  KEY `idx_license_device` (`license_key`, `device_uid`),
  KEY `idx_generated` (`generated_at`),
  KEY `idx_expires` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Skeys记录表，用于搜索和历史记录';

-- ===================================================================
-- 6. API频率限制表 (api_rate_limits) - 安全功能
-- ===================================================================
CREATE TABLE `api_rate_limits` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ip_address` varchar(45) NOT NULL COMMENT 'IP地址',
  `endpoint` varchar(100) NOT NULL COMMENT '端点名称',
  `requests` int(11) NOT NULL DEFAULT 1 COMMENT '请求次数',
  `window_start` datetime NOT NULL COMMENT '时间窗口开始',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ip_endpoint_window` (`ip_address`, `endpoint`, `window_start`),
  KEY `idx_window_start` (`window_start`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API频率限制表';

-- ===================================================================
-- 7. 错误日志表 (error_logs) - 系统监控
-- ===================================================================
CREATE TABLE `error_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `level` varchar(20) NOT NULL COMMENT '错误级别',
  `message` text NOT NULL COMMENT '错误消息',
  `file` varchar(255) DEFAULT NULL COMMENT '文件路径',
  `line` int(11) DEFAULT NULL COMMENT '行号',
  `context` text DEFAULT NULL COMMENT '上下文信息',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_level_created` (`level`, `created_at`),
  KEY `idx_created` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统错误日志表';

-- ===================================================================
-- 8. 插入默认数据
-- ===================================================================

-- 插入默认套餐
INSERT INTO `plans` (`plan_name`, `duration_days`, `is_active`) VALUES
('天卡', 1, 1),
('周卡', 7, 1),
('半月卡', 15, 1),
('月卡', 30, 1),
('季卡', 90, 1),
('半年卡', 180, 1),
('年卡', 365, 1);

-- ===================================================================
-- 9. 设置外键约束
-- ===================================================================
SET FOREIGN_KEY_CHECKS = 1;

-- ===================================================================
-- 10. 显示创建结果
-- ===================================================================
SELECT 'Database installation completed successfully!' as Status;

-- 显示表结构信息
SELECT 
    TABLE_NAME as '表名',
    TABLE_COMMENT as '说明',
    TABLE_ROWS as '记录数'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
ORDER BY TABLE_NAME;
