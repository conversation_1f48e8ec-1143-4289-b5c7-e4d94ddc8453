-- 重新设计多设备功能 - 将设备数量从套餐级别移到激活码级别
-- 执行前请备份数据库

-- 1. 在licenses表中添加max_devices字段
ALTER TABLE `licenses` ADD COLUMN `max_devices` int(11) NOT NULL DEFAULT 1 COMMENT '该激活码最大可绑定设备数量';

-- 2. 从plans表的max_devices迁移数据到licenses表
UPDATE `licenses` l 
JOIN `plans` p ON l.plan_id = p.id 
SET l.max_devices = COALESCE(p.max_devices, 1);

-- 3. 可选：从plans表中移除max_devices字段（如果不再需要）
-- ALTER TABLE `plans` DROP COLUMN `max_devices`;

-- 4. 更新现有的bound_devices字段确保数据正确
UPDATE `licenses` l 
SET `bound_devices` = (
    SELECT COUNT(*) 
    FROM `license_devices` ld 
    WHERE ld.`license_id` = l.`id` AND ld.`status` = 'active'
);
