<?php
require_once 'includes/session_helper.php';
require_once 'includes/db.php';

// 强制登录
$_SESSION['loggedin'] = true;
?>

<!DOCTYPE html>
<html>
<head>
    <title>最终解绑测试</title>
    <meta charset="utf-8">
    <script src="assets/plugins/jquery/jquery.min.js"></script>
</head>
<body>
    <h2>最终解绑测试</h2>
    
    <?php
    // 显示当前设备列表
    echo "<h3>激活码775的设备列表</h3>";
    try {
        $stmt = $pdo->query("
            SELECT ld.id, ld.device_uid, ld.activated_at, l.id as license_id, l.license_key, l.bound_devices
            FROM license_devices ld 
            JOIN licenses l ON ld.license_id = l.id 
            WHERE l.id = 775
            ORDER BY ld.id
        ");
        $devices = $stmt->fetchAll();
        
        if (empty($devices)) {
            echo "<p>没有找到设备</p>";
        } else {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>设备记录ID</th><th>设备UID</th><th>激活码</th><th>当前绑定数</th><th>绑定时间</th><th>操作</th></tr>";
            foreach ($devices as $device) {
                echo "<tr>";
                echo "<td><strong>" . $device['id'] . "</strong></td>";
                echo "<td>" . htmlspecialchars($device['device_uid']) . "</td>";
                echo "<td>" . htmlspecialchars($device['license_key']) . "</td>";
                echo "<td>" . $device['bound_devices'] . "</td>";
                echo "<td>" . $device['activated_at'] . "</td>";
                echo "<td>";
                echo "<button onclick='unbindDevice(" . $device['id'] . ", \"" . htmlspecialchars($device['device_uid']) . "\")'>解绑</button>";
                echo "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>查询错误: " . $e->getMessage() . "</p>";
    }
    ?>
    
    <div id="result" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc; background: #f9f9f9;"></div>
    
    <h3>手动测试</h3>
    <p>设备ID 530: <button onclick="unbindDevice(530, 'TEST_3FP0HC764_MD1FX2J1')">解绑530</button></p>
    <p>设备ID 529: <button onclick="unbindDevice(529, 'TEST_QKBX5PRTB_MD1CPUU4')">解绑529</button></p>
    
    <script>
    function unbindDevice(deviceId, deviceUid) {
        if (!confirm('确定要解绑设备 ' + deviceUid + ' (ID:' + deviceId + ') 吗？')) {
            return;
        }
        
        $('#result').html('<p>正在解绑设备ID: ' + deviceId + '...</p>');
        
        $.ajax({
            url: 'actions/update_license.php',
            type: 'POST',
            data: {
                action: 'unbind_device',
                device_id: deviceId
            },
            dataType: 'json',
            success: function(response) {
                console.log('解绑响应:', response);
                
                if (response.success) {
                    $('#result').html('<div style="color: green;"><strong>解绑成功！</strong><br>' + 
                        '消息: ' + response.message + '<br>' +
                        '设备UID: ' + response.device_uid + '</div>');
                    
                    // 2秒后刷新页面
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                } else {
                    $('#result').html('<div style="color: red;"><strong>解绑失败：</strong>' + response.message + '</div>');
                }
            },
            error: function(xhr, status, error) {
                console.error('请求失败:', xhr.responseText);
                $('#result').html('<div style="color: red;"><strong>请求失败：</strong>' + error + '<br><strong>响应内容：</strong><pre>' + xhr.responseText + '</pre></div>');
            }
        });
    }
    </script>
    
    <style>
    table { border-collapse: collapse; margin: 10px 0; }
    th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
    th { background-color: #f2f2f2; }
    button { 
        padding: 8px 12px; 
        margin: 2px;
        background-color: #dc3545; 
        color: white; 
        border: none; 
        cursor: pointer; 
        border-radius: 4px;
    }
    button:hover { background-color: #c82333; }
    </style>
</body>
</html>
