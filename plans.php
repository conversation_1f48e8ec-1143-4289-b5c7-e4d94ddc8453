<?php
require_once 'includes/check_login.php';
require_once 'includes/db.php';
require_once 'includes/header.php';
require_once 'includes/sidebar.php';

// 获取所有套餐用于展示
$plans = $pdo->query("SELECT * FROM plans ORDER BY id ASC")->fetchAll();
?>

<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid"><h1 class="m-0">套餐管理</h1></div>
    </section>
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- 左侧：套餐列表 -->
                <div class="col-md-8">
                    <div class="card card-info card-outline">
                        <div class="card-header"><h3 class="card-title"><i class="fas fa-tags"></i> 套餐列表</h3></div>
                        <div class="card-body">
                            <table class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>套餐名称</th>
                                        <th>有效期</th>
                                        <th>最大设备数</th>
                                        <th>备注</th>
                                        <th>状态</th>
                                        <th style="width: 120px;">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($plans as $plan): 
                                        $validity_text = $plan['validity_value'] . ' ' . [
                                            'hour' => '小时', 'day' => '天', 'month' => '月', 'year' => '年'
                                        ][$plan['validity_unit']];
                                    ?>
                                    <tr>
                                        <td><?= $plan['id'] ?></td>
                                        <td><?= htmlspecialchars($plan['plan_name']) ?></td>
                                        <td><?= $validity_text ?></td>
                                        <td>
                                            <span class="badge bg-info"><?= $plan['max_devices'] ?? 1 ?> 设备</span>
                                        </td>
                                        <td><?= htmlspecialchars($plan['notes']) ?></td>
                                        <td>
                                            <span class="badge bg-<?= $plan['is_active'] ? 'success' : 'secondary' ?>">
                                                <?= $plan['is_active'] ? '可用' : '不可用' ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <button class="btn btn-sm btn-secondary edit-plan-btn"
                                                        data-id="<?= $plan['id'] ?>"
                                                        data-name="<?= htmlspecialchars($plan['plan_name']) ?>"
                                                        data-value="<?= $plan['validity_value'] ?>"
                                                        data-unit="<?= $plan['validity_unit'] ?>"
                                                        data-max-devices="<?= $plan['max_devices'] ?? 1 ?>"
                                                        data-notes="<?= htmlspecialchars($plan['notes']) ?>"
                                                        data-active="<?= $plan['is_active'] ?>"
                                                        title="编辑"><i class="fas fa-pencil-alt"></i></button>
                                                <a href="actions/update_plan.php?id=<?= $plan['id'] ?>&action=delete" 
                                                   class="btn btn-sm btn-danger" 
                                                   onclick="return confirm('确定要删除套餐【<?= htmlspecialchars($plan['plan_name']) ?>】吗？如果该套餐下已有激活码，将无法删除。');"
                                                   title="删除"><i class="fas fa-trash"></i></a>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 右侧：添加/编辑套餐表单 -->
                <div class="col-md-4">
                    <div class="card card-primary card-outline" id="planFormCard">
                        <div class="card-header"><h3 class="card-title" id="planFormTitle"><i class="fas fa-plus"></i> 添加新套餐</h3></div>
                        <form id="planForm" action="actions/update_plan.php" method="post">
                            <div class="card-body">
                                <input type="hidden" name="action" id="planAction" value="add">
                                <input type="hidden" name="id" id="planId">
                                
                                <div class="form-group">
                                    <label for="plan_name">套餐名称</label>
                                    <input type="text" name="plan_name" id="plan_name" class="form-control" placeholder="例如：永久卡" required>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label for="validity_value">有效期数值</label>
                                            <input type="number" name="validity_value" id="validity_value" class="form-control" value="1" required>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group">
                                            <label for="validity_unit">单位</label>
                                            <select name="validity_unit" id="validity_unit" class="form-control">
                                                <option value="hour">小时</option>
                                                <option value="day">天</option>
                                                <option value="month">月</option>
                                                <option value="year">年</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="max_devices">最大设备数</label>
                                    <input type="number" name="max_devices" id="max_devices" class="form-control" value="1" min="1" max="100" required>
                                    <small class="form-text text-muted">一个激活码最多可以绑定多少台设备</small>
                                </div>
                                <div class="form-group">
                                    <label for="notes">备注 (可选)</label>
                                    <input type="text" name="notes" id="notes" class="form-control" placeholder="例如：活动专属">
                                </div>
                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                      <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" value="1" checked>
                                      <label class="custom-control-label" for="is_active">套餐是否可用</label>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <button type="submit" class="btn btn-primary" id="planSubmitBtn">确认添加</button>
                                <button type="button" class="btn btn-secondary" id="cancelEditBtn" style="display: none;">取消编辑</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<?php require_once 'includes/footer.php'; ?>