<?php
require_once 'includes/session_helper.php';
require_once 'includes/db.php';

// 模拟登录状态
$_SESSION['loggedin'] = true;

echo "<h2>设备解绑测试</h2>";

// 查询激活码775的设备信息
$license_id = 775;
$stmt = $pdo->prepare("
    SELECT ld.id, ld.device_uid, ld.activated_at, l.license_key 
    FROM license_devices ld 
    JOIN licenses l ON ld.license_id = l.id 
    WHERE l.id = ?
");
$stmt->execute([$license_id]);
$devices = $stmt->fetchAll();

echo "<h3>激活码ID: $license_id 的绑定设备</h3>";
if (empty($devices)) {
    echo "<p>没有找到绑定的设备</p>";
} else {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>设备记录ID</th><th>设备UID</th><th>激活码</th><th>绑定时间</th><th>操作</th></tr>";
    foreach ($devices as $device) {
        echo "<tr>";
        echo "<td>" . $device['id'] . "</td>";
        echo "<td>" . htmlspecialchars($device['device_uid']) . "</td>";
        echo "<td>" . htmlspecialchars($device['license_key']) . "</td>";
        echo "<td>" . $device['activated_at'] . "</td>";
        echo "<td><button onclick='unbindDevice(" . $device['id'] . ", \"" . htmlspecialchars($device['device_uid']) . "\")'>解绑</button></td>";
        echo "</tr>";
    }
    echo "</table>";
}

// 查询所有包含TEST_3FP0HC764_MD1FX2J1的设备
echo "<h3>查找设备UID: TEST_3FP0HC764_MD1FX2J1</h3>";
$search_uid = 'TEST_3FP0HC764_MD1FX2J1';
$search_stmt = $pdo->prepare("
    SELECT ld.id, ld.device_uid, ld.activated_at, l.id as license_id, l.license_key 
    FROM license_devices ld 
    JOIN licenses l ON ld.license_id = l.id 
    WHERE ld.device_uid = ?
");
$search_stmt->execute([$search_uid]);
$found_devices = $search_stmt->fetchAll();

if (empty($found_devices)) {
    echo "<p>没有找到设备UID为 $search_uid 的记录</p>";
} else {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>记录ID</th><th>激活码ID</th><th>激活码</th><th>设备UID</th><th>绑定时间</th><th>操作</th></tr>";
    foreach ($found_devices as $device) {
        echo "<tr>";
        echo "<td><strong>" . $device['id'] . "</strong></td>";
        echo "<td>" . $device['license_id'] . "</td>";
        echo "<td>" . htmlspecialchars($device['license_key']) . "</td>";
        echo "<td>" . htmlspecialchars($device['device_uid']) . "</td>";
        echo "<td>" . $device['activated_at'] . "</td>";
        echo "<td><button onclick='unbindDevice(" . $device['id'] . ", \"" . htmlspecialchars($device['device_uid']) . "\")'>解绑</button></td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "<p><strong>说明：记录ID " . $found_devices[0]['id'] . " 就是您看到的ID 530</strong></p>";
}
?>

<div id="result" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc;"></div>

<script src="assets/plugins/jquery/jquery.min.js"></script>
<script>
function unbindDevice(deviceId, deviceUid) {
    if (!confirm('确定要解绑设备 ' + deviceUid + ' (ID:' + deviceId + ') 吗？')) {
        return;
    }
    
    $('#result').html('<p>正在解绑设备...</p>');
    
    $.post('actions/update_license.php', {
        action: 'unbind_device',
        device_id: deviceId
    }).done(function(response) {
        console.log('解绑响应:', response);
        
        try {
            var result = typeof response === 'string' ? JSON.parse(response) : response;
            if (result.success) {
                $('#result').html('<div style="color: green;"><strong>解绑成功！</strong><br>' + result.message + '</div>');
                setTimeout(function() {
                    location.reload();
                }, 2000);
            } else {
                $('#result').html('<div style="color: red;"><strong>解绑失败：</strong>' + result.message + '</div>');
            }
        } catch (e) {
            $('#result').html('<div style="color: red;"><strong>响应解析失败：</strong><br><pre>' + response + '</pre></div>');
            console.error('原始响应:', response);
        }
    }).fail(function(xhr, status, error) {
        $('#result').html('<div style="color: red;"><strong>请求失败：</strong>' + error + '<br><pre>' + xhr.responseText + '</pre></div>');
        console.error('失败详情:', xhr.responseText);
    });
}
</script>

<style>
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
button { padding: 5px 10px; background-color: #dc3545; color: white; border: none; cursor: pointer; }
button:hover { background-color: #c82333; }
</style>
