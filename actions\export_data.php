<?php
require_once '../includes/check_login.php';
require_once '../includes/db.php';
require_once '../includes/config.php';

/**
 * 数据导出功能
 * 支持CSV和Excel格式
 */

class DataExporter {
    private $pdo;
    private $config;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->config = Config::load();
    }
    
    /**
     * 导出激活码数据
     */
    public function exportLicenses($format = 'csv', $filters = []) {
        // 构建查询
        $sql = "SELECT 
                    l.id,
                    l.license_key,
                    p.plan_name,
                    l.status,
                    l.max_devices,
                    l.created_at,
                    l.activated_at,
                    l.expires_at,
                    l.notes,
                    (SELECT COUNT(*) FROM license_devices ld WHERE ld.license_id = l.id AND ld.status = 'active') as bound_devices
                FROM licenses l 
                JOIN plans p ON l.plan_id = p.id";
        
        $where_clauses = [];
        $params = [];
        
        // 应用筛选条件
        if (!empty($filters['plan_id'])) {
            $where_clauses[] = "l.plan_id = ?";
            $params[] = $filters['plan_id'];
        }
        
        if (!empty($filters['status'])) {
            $where_clauses[] = "l.status = ?";
            $params[] = $filters['status'];
        }
        
        if (!empty($filters['date_from'])) {
            $where_clauses[] = "DATE(l.created_at) >= ?";
            $params[] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $where_clauses[] = "DATE(l.created_at) <= ?";
            $params[] = $filters['date_to'];
        }
        
        if (!empty($where_clauses)) {
            $sql .= " WHERE " . implode(' AND ', $where_clauses);
        }
        
        $sql .= " ORDER BY l.id DESC LIMIT " . $this->config['export']['max_records'];
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        $data = $stmt->fetchAll();
        
        // 准备表头
        $headers = [
            'ID', '激活码', '套餐', '状态', '最大设备数', 
            '创建时间', '激活时间', '到期时间', '已绑定设备', '备注'
        ];
        
        // 处理数据
        $rows = [];
        foreach ($data as $row) {
            $rows[] = [
                $row['id'],
                $row['license_key'],
                $row['plan_name'],
                $this->getStatusText($row['status']),
                $row['max_devices'],
                $row['created_at'],
                $row['activated_at'] ?: '未激活',
                $row['expires_at'],
                $row['bound_devices'],
                $row['notes']
            ];
        }
        
        return $this->generateFile($headers, $rows, 'licenses', $format);
    }
    
    /**
     * 导出操作日志
     */
    public function exportLogs($format = 'csv', $filters = []) {
        $sql = "SELECT 
                    id, license_key, device_uid, log_type, status, 
                    ip_address, log_time
                FROM logs";
        
        $where_clauses = [];
        $params = [];
        
        if (!empty($filters['date_from'])) {
            $where_clauses[] = "DATE(log_time) >= ?";
            $params[] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $where_clauses[] = "DATE(log_time) <= ?";
            $params[] = $filters['date_to'];
        }
        
        if (!empty($filters['log_type'])) {
            $where_clauses[] = "log_type = ?";
            $params[] = $filters['log_type'];
        }
        
        if (!empty($where_clauses)) {
            $sql .= " WHERE " . implode(' AND ', $where_clauses);
        }
        
        $sql .= " ORDER BY log_time DESC LIMIT " . $this->config['export']['max_records'];
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        $data = $stmt->fetchAll();
        
        $headers = ['ID', '激活码', '设备UID', '操作类型', '结果', 'IP地址', '时间'];
        
        $rows = [];
        foreach ($data as $row) {
            $rows[] = [
                $row['id'],
                $row['license_key'],
                $row['device_uid'],
                $row['log_type'] === 'activate' ? '激活' : '验证',
                $row['status'] === 'SUCCESS' ? '成功' : '失败',
                $row['ip_address'],
                $row['log_time']
            ];
        }
        
        return $this->generateFile($headers, $rows, 'logs', $format);
    }
    
    /**
     * 生成文件
     */
    private function generateFile($headers, $rows, $type, $format) {
        $timestamp = date('Y-m-d_H-i-s');
        $filename = "{$type}_export_{$timestamp}";
        
        if ($format === 'csv') {
            return $this->generateCSV($headers, $rows, $filename);
        } else {
            return $this->generateExcel($headers, $rows, $filename);
        }
    }
    
    /**
     * 生成CSV文件
     */
    private function generateCSV($headers, $rows, $filename) {
        $filename .= '.csv';
        
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        
        $output = fopen('php://output', 'w');
        
        // 添加BOM以支持Excel中文显示
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // 写入表头
        fputcsv($output, $headers);
        
        // 写入数据
        foreach ($rows as $row) {
            fputcsv($output, $row);
        }
        
        fclose($output);
        exit();
    }
    
    /**
     * 生成Excel文件（简化版，实际项目中建议使用PhpSpreadsheet）
     */
    private function generateExcel($headers, $rows, $filename) {
        $filename .= '.xls';
        
        header('Content-Type: application/vnd.ms-excel; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        
        echo '<html><head><meta charset="utf-8"></head><body>';
        echo '<table border="1">';
        
        // 表头
        echo '<tr>';
        foreach ($headers as $header) {
            echo '<th>' . htmlspecialchars($header) . '</th>';
        }
        echo '</tr>';
        
        // 数据
        foreach ($rows as $row) {
            echo '<tr>';
            foreach ($row as $cell) {
                echo '<td>' . htmlspecialchars($cell) . '</td>';
            }
            echo '</tr>';
        }
        
        echo '</table>';
        echo '</body></html>';
        exit();
    }
    
    /**
     * 获取状态文本
     */
    private function getStatusText($status) {
        $map = [
            'active' => '已激活',
            'inactive' => '未使用',
            'expired' => '已过期',
            'disabled' => '已禁用'
        ];
        return $map[$status] ?? $status;
    }
}

// 处理导出请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $type = $_POST['type'] ?? '';
    $format = $_POST['format'] ?? 'csv';
    $filters = $_POST['filters'] ?? [];
    
    if (!in_array($format, config('export.allowed_formats', ['csv']))) {
        die('不支持的导出格式');
    }
    
    $exporter = new DataExporter($pdo);
    
    try {
        if ($type === 'licenses') {
            $exporter->exportLicenses($format, $filters);
        } elseif ($type === 'logs') {
            $exporter->exportLogs($format, $filters);
        } else {
            die('不支持的导出类型');
        }
    } catch (Exception $e) {
        die('导出失败：' . $e->getMessage());
    }
}

// 如果不是POST请求，重定向回主页
header('Location: ../index.php');
exit();
?>
