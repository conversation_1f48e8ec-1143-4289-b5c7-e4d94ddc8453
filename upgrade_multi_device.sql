-- 多设备支持数据库升级脚本
-- 执行前请备份数据库

-- 1. 在plans表中添加最大设备数字段
ALTER TABLE `plans` ADD COLUMN `max_devices` int(11) NOT NULL DEFAULT 1 COMMENT '最大可绑定设备数量';

-- 2. 更新现有套餐的设备数量（默认为1）
UPDATE `plans` SET `max_devices` = 1 WHERE `max_devices` = 0;

-- 3. 创建设备绑定表
CREATE TABLE IF NOT EXISTS `license_devices` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `license_id` int(11) NOT NULL COMMENT '激活码ID',
  `device_uid` varchar(64) NOT NULL COMMENT '设备唯一ID',
  `device_name` varchar(100) DEFAULT NULL COMMENT '设备名称（可选）',
  `activated_at` datetime NOT NULL COMMENT '绑定时间',
  `last_verify_at` datetime DEFAULT NULL COMMENT '最后验证时间',
  `last_ip` varchar(45) DEFAULT NULL COMMENT '最后访问IP',
  `verify_count` int(11) NOT NULL DEFAULT 0 COMMENT '验证次数',
  `status` enum('active','disabled') NOT NULL DEFAULT 'active' COMMENT '设备状态',
  PRIMARY KEY (`id`),
  UNIQUE KEY `license_device_unique` (`license_id`, `device_uid`),
  KEY `device_uid` (`device_uid`),
  KEY `license_id` (`license_id`),
  CONSTRAINT `license_devices_ibfk_1` FOREIGN KEY (`license_id`) REFERENCES `licenses` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 4. 迁移现有的设备绑定数据
INSERT INTO `license_devices` (`license_id`, `device_uid`, `activated_at`, `last_ip`, `status`)
SELECT `id`, `device_uid`, COALESCE(`activated_at`, NOW()), `last_ip`, 'active'
FROM `licenses` 
WHERE `device_uid` IS NOT NULL AND `device_uid` != '';

-- 5. 在licenses表中添加当前绑定设备数字段（用于快速查询）
ALTER TABLE `licenses` ADD COLUMN `bound_devices` int(11) NOT NULL DEFAULT 0 COMMENT '当前绑定设备数量';

-- 6. 更新licenses表的bound_devices字段
UPDATE `licenses` l 
SET `bound_devices` = (
    SELECT COUNT(*) 
    FROM `license_devices` ld 
    WHERE ld.`license_id` = l.`id` AND ld.`status` = 'active'
);

-- 7. 添加一些示例多设备套餐
INSERT INTO `plans` (`plan_name`, `validity_value`, `validity_unit`, `max_devices`, `notes`, `is_active`) VALUES
('3设备月卡', 30, 'day', 3, '可同时绑定3台设备', 1),
('5设备季卡', 90, 'day', 5, '可同时绑定5台设备', 1),
('10设备年卡', 365, 'day', 10, '可同时绑定10台设备', 1);
