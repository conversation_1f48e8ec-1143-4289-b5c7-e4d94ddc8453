<?php
/**
 * 添加skeys_records表的修复脚本
 * 用于已经迁移但缺少skeys_records表的情况
 */

require_once 'includes/db.php';

echo "<h2>添加skeys_records表</h2>";

try {
    // 检查skeys_records表是否存在
    echo "<p>检查skeys_records表...</p>";
    
    try {
        $pdo->query("SELECT 1 FROM skeys_records LIMIT 1");
        echo "<p style='color: green;'>✓ skeys_records表已存在，无需添加</p>";
        
        // 显示表结构
        echo "<h3>当前skeys_records表结构</h3>";
        $result = $pdo->query("DESCRIBE skeys_records");
        $fields = $result->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th></tr>";
        foreach ($fields as $field) {
            echo "<tr>";
            echo "<td>" . $field['Field'] . "</td>";
            echo "<td>" . $field['Type'] . "</td>";
            echo "<td>" . $field['Null'] . "</td>";
            echo "<td>" . $field['Key'] . "</td>";
            echo "<td>" . $field['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } catch (Exception $e) {
        echo "<p style='color: orange;'>skeys_records表不存在，开始创建...</p>";
        
        // 创建skeys_records表
        $create_sql = "
        CREATE TABLE `skeys_records` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `license_key` varchar(50) NOT NULL COMMENT '激活码',
          `device_uid` varchar(64) NOT NULL COMMENT '设备UID',
          `skeys` text NOT NULL COMMENT '生成的Skeys',
          `generated_at` datetime NOT NULL COMMENT '生成时间',
          `expires_at` datetime NOT NULL COMMENT '过期时间',
          PRIMARY KEY (`id`),
          UNIQUE KEY `license_device_unique` (`license_key`, `device_uid`),
          KEY `skeys_search` (`skeys`(100)),
          KEY `idx_license_device` (`license_key`, `device_uid`),
          KEY `idx_generated` (`generated_at`),
          KEY `idx_expires` (`expires_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Skeys记录表，用于搜索和历史记录'";
        
        $pdo->exec($create_sql);
        echo "<p style='color: green;'>✓ 成功创建skeys_records表</p>";
        
        // 显示创建后的表结构
        echo "<h3>创建后的skeys_records表结构</h3>";
        $result = $pdo->query("DESCRIBE skeys_records");
        $fields = $result->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th></tr>";
        foreach ($fields as $field) {
            echo "<tr>";
            echo "<td>" . $field['Field'] . "</td>";
            echo "<td>" . $field['Type'] . "</td>";
            echo "<td>" . $field['Null'] . "</td>";
            echo "<td>" . $field['Key'] . "</td>";
            echo "<td>" . $field['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 检查所有必要的表
    echo "<h3>数据库表检查</h3>";
    $required_tables = ['licenses', 'license_devices', 'skeys_records', 'logs', 'plans'];
    
    foreach ($required_tables as $table) {
        try {
            $count = $pdo->query("SELECT COUNT(*) FROM $table")->fetchColumn();
            echo "<p style='color: green;'>✓ $table 表存在，记录数：$count</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ $table 表不存在</p>";
        }
    }
    
    echo "<h3 style='color: green;'>检查完成！</h3>";
    echo "<p>现在系统应该可以正常使用了。</p>";
    
    // 显示skeys_records表的作用说明
    echo "<h3>skeys_records表的作用</h3>";
    echo "<ul>";
    echo "<li><strong>存储Skeys记录</strong> - 每次生成Skeys时都会保存记录</li>";
    echo "<li><strong>搜索功能</strong> - 在日志管理中可以通过Skeys搜索相关记录</li>";
    echo "<li><strong>历史记录</strong> - 保存设备的Skeys生成历史</li>";
    echo "<li><strong>系统监控</strong> - 监控Skeys记录的数量和状态</li>";
    echo "<li><strong>去重功能</strong> - 同一设备和激活码组合只保存最新的Skeys</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>操作失败：" . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
th { background-color: #f2f2f2; }
ul { margin: 10px 0; }
li { margin: 5px 0; }
</style>
