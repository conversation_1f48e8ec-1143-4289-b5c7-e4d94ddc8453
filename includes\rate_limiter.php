<?php
/**
 * API频率限制器
 * 防止API滥用和暴力攻击
 */

class RateLimiter {
    private $pdo;
    private $table_name = 'api_rate_limits';
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->createTableIfNotExists();
    }
    
    /**
     * 创建频率限制表
     */
    private function createTableIfNotExists() {
        $sql = "CREATE TABLE IF NOT EXISTS `{$this->table_name}` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `ip_address` varchar(45) NOT NULL,
            `endpoint` varchar(100) NOT NULL,
            `requests` int(11) NOT NULL DEFAULT 1,
            `window_start` datetime NOT NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `ip_endpoint_window` (`ip_address`, `endpoint`, `window_start`),
            KEY `idx_window_start` (`window_start`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        
        try {
            $this->pdo->exec($sql);
        } catch (PDOException $e) {
            // 表可能已存在，忽略错误
        }
    }
    
    /**
     * 检查是否超过频率限制
     * @param string $ip_address IP地址
     * @param string $endpoint 端点名称
     * @param int $limit 限制次数
     * @param int $window_seconds 时间窗口（秒）
     * @return bool true表示允许请求，false表示超过限制
     */
    public function isAllowed($ip_address, $endpoint, $limit = 100, $window_seconds = 60) {
        $window_start = date('Y-m-d H:i:00', time() - (time() % $window_seconds));
        
        // 清理过期记录
        $this->cleanupExpiredRecords($window_seconds);
        
        // 检查当前窗口的请求次数
        $stmt = $this->pdo->prepare("
            SELECT requests FROM {$this->table_name} 
            WHERE ip_address = ? AND endpoint = ? AND window_start = ?
        ");
        $stmt->execute([$ip_address, $endpoint, $window_start]);
        $current_requests = $stmt->fetchColumn();
        
        if ($current_requests === false) {
            // 第一次请求，插入记录
            $stmt = $this->pdo->prepare("
                INSERT INTO {$this->table_name} (ip_address, endpoint, requests, window_start) 
                VALUES (?, ?, 1, ?)
            ");
            $stmt->execute([$ip_address, $endpoint, $window_start]);
            return true;
        } else {
            if ($current_requests >= $limit) {
                return false; // 超过限制
            } else {
                // 增加请求计数
                $stmt = $this->pdo->prepare("
                    UPDATE {$this->table_name} 
                    SET requests = requests + 1 
                    WHERE ip_address = ? AND endpoint = ? AND window_start = ?
                ");
                $stmt->execute([$ip_address, $endpoint, $window_start]);
                return true;
            }
        }
    }
    
    /**
     * 清理过期记录
     */
    private function cleanupExpiredRecords($window_seconds) {
        $cutoff_time = date('Y-m-d H:i:s', time() - $window_seconds * 2);
        $stmt = $this->pdo->prepare("DELETE FROM {$this->table_name} WHERE window_start < ?");
        $stmt->execute([$cutoff_time]);
    }
    
    /**
     * 获取剩余请求次数
     */
    public function getRemainingRequests($ip_address, $endpoint, $limit = 100, $window_seconds = 60) {
        $window_start = date('Y-m-d H:i:00', time() - (time() % $window_seconds));
        
        $stmt = $this->pdo->prepare("
            SELECT requests FROM {$this->table_name} 
            WHERE ip_address = ? AND endpoint = ? AND window_start = ?
        ");
        $stmt->execute([$ip_address, $endpoint, $window_start]);
        $current_requests = $stmt->fetchColumn();
        
        return max(0, $limit - ($current_requests ?: 0));
    }
}

/**
 * 便捷函数：检查API频率限制
 */
function check_api_rate_limit($pdo, $endpoint, $limit = 100, $window_seconds = 60) {
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $rate_limiter = new RateLimiter($pdo);
    
    if (!$rate_limiter->isAllowed($ip, $endpoint, $limit, $window_seconds)) {
        http_response_code(429);
        header('Content-Type: application/json');
        echo json_encode([
            'error' => 'Too Many Requests',
            'message' => '请求过于频繁，请稍后再试',
            'retry_after' => $window_seconds
        ]);
        exit();
    }
    
    return $rate_limiter->getRemainingRequests($ip, $endpoint, $limit, $window_seconds);
}
?>
