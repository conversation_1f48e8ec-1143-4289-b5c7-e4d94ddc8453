-- 添加Skeys存储表，用于搜索功能
-- 执行前请备份数据库

-- 创建Skeys记录表
CREATE TABLE IF NOT EXISTS `skeys_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `license_key` varchar(50) NOT NULL COMMENT '激活码',
  `device_uid` varchar(64) NOT NULL COMMENT '设备UID',
  `skeys` text NOT NULL COMMENT '生成的Skeys',
  `generated_at` datetime NOT NULL COMMENT '生成时间',
  `expires_at` datetime NOT NULL COMMENT '过期时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `license_device_unique` (`license_key`, `device_uid`),
  KEY `skeys_search` (`skeys`(100)),
  KEY `license_key` (`license_key`),
  KEY `device_uid` (`device_uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Skeys记录表，用于搜索和历史记录';
