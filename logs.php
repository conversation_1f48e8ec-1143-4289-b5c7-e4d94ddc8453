<?php
// 临时移除登录检查
// require_once 'includes/check_login.php';
require_once 'includes/db.php';
require_once 'includes/header.php';
require_once 'includes/sidebar.php';

// 获取筛选参数
$search_key = $_GET['key'] ?? '';
$search_uid = $_GET['uid'] ?? '';
$search_skeys = $_GET['skeys'] ?? '';
$search_ip = $_GET['ip'] ?? '';
$search_type = $_GET['type'] ?? '';
$search_status = $_GET['status'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';

$where_clauses = [];
$params = [];

if (!empty($search_key)) {
    $where_clauses[] = "license_key LIKE ?";
    $params[] = "%" . $search_key . "%";
}
if (!empty($search_uid)) {
    $where_clauses[] = "device_uid LIKE ?";
    $params[] = "%" . $search_uid . "%";
}
if (!empty($search_skeys)) {
    // 通过Skeys搜索，需要关联skeys_records表
    $where_clauses[] = "EXISTS (SELECT 1 FROM skeys_records sr WHERE sr.license_key = logs.license_key AND sr.device_uid = logs.device_uid AND sr.skeys LIKE ?)";
    $params[] = "%" . $search_skeys . "%";
}
if (!empty($search_ip)) {
    $where_clauses[] = "ip_address LIKE ?";
    $params[] = "%" . $search_ip . "%";
}
if (!empty($search_type)) {
    $where_clauses[] = "log_type = ?";
    $params[] = $search_type;
}
if (!empty($search_status)) {
    if ($search_status === 'SUCCESS') {
        $where_clauses[] = "status = 'SUCCESS'";
    } else {
        $where_clauses[] = "status != 'SUCCESS'";
    }
}
if (!empty($date_from)) {
    $where_clauses[] = "DATE(log_time) >= ?";
    $params[] = $date_from;
}
if (!empty($date_to)) {
    $where_clauses[] = "DATE(log_time) <= ?";
    $params[] = $date_to;
}

// 定义状态和类型的中文映射
$status_map = [
    'SUCCESS' => ['text' => '成功', 'color' => 'success'],
    'FAILED_KEY_NOT_FOUND' => ['text' => '激活码不存在', 'color' => 'danger'],
    'FAILED_KEY_DISABLED' => ['text' => '激活码已禁用', 'color' => 'warning'],
    'FAILED_KEY_EXPIRED' => ['text' => '激活码已过期', 'color' => 'warning'],
    'FAILED_KEY_BOUND_OTHER' => ['text' => '激活码已绑定其他设备', 'color' => 'danger'],
    'FAILED_MAX_DEVICES_REACHED' => ['text' => '设备数量已达上限', 'color' => 'warning'],
    'FAILED_DEVICE_NOT_FOUND' => ['text' => '设备未找到', 'color' => 'danger'],
    'FAILED_INVALID_SKEY' => ['text' => '无效的验证密钥', 'color' => 'danger'],
    'FAILED_INVALID_STATUS' => ['text' => '无效状态', 'color' => 'danger'],
    'FAILED_INVALID_REQUEST' => ['text' => '无效请求', 'color' => 'danger'],
];

$type_map = [
    'activate' => ['text' => '激活', 'color' => 'primary'],
    'verify' => ['text' => '验证', 'color' => 'info'],
];

$sql = "SELECT * FROM logs";
if (!empty($where_clauses)) {
    $sql .= " WHERE " . implode(' AND ', $where_clauses);
}
$sql .= " ORDER BY id DESC LIMIT 200";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$logs = $stmt->fetchAll();
?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid"><h1 class="m-0">操作日志</h1></div>
    </section>
    <section class="content">
        <div class="container-fluid">
            <div class="card card-info card-outline">
                <div class="card-header"><h3 class="card-title"><i class="fas fa-search"></i> 查询日志</h3></div>
                <div class="card-body">
                    <form method="get">
                        <div class="row">
                            <div class="col-md-3">
                                <input type="text" name="key" class="form-control" placeholder="按激活码查询" value="<?= htmlspecialchars($search_key) ?>">
                            </div>
                            <div class="col-md-3">
                                <input type="text" name="uid" class="form-control" placeholder="按设备UID查询" value="<?= htmlspecialchars($search_uid) ?>">
                            </div>
                            <div class="col-md-3">
                                <input type="text" name="skeys" class="form-control" placeholder="按Skeys查询" value="<?= htmlspecialchars($_GET['skeys'] ?? '') ?>">
                            </div>
                            <div class="col-md-3">
                                <button type="submit" class="btn btn-info w-100">查询</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr><th>ID</th><th>类型</th><th>激活码</th><th>设备UID</th><th>IP地址</th><th>结果</th><th>时间</th><th>操作</th></tr>
                        </thead>
                        <tbody>
                            <?php foreach ($logs as $log):
                                $type_info = $type_map[$log['log_type']] ?? ['text' => $log['log_type'], 'color' => 'secondary'];
                                $status_info = $status_map[$log['status']] ?? ['text' => $log['status'], 'color' => 'secondary'];
                            ?>
                            <tr>
                                <td><?= $log['id'] ?></td>
                                <td><span class="badge bg-<?= $type_info['color'] ?>"><?= $type_info['text'] ?></span></td>
                                <td><a href="?key=<?= urlencode($log['license_key']) ?>"><?= htmlspecialchars($log['license_key']) ?></a></td>
                                <td><a href="?uid=<?= urlencode($log['device_uid']) ?>"><?= htmlspecialchars($log['device_uid']) ?></a></td>
                                <td><?= htmlspecialchars($log['ip_address']) ?></td>
                                <td><span class="badge bg-<?= $status_info['color'] ?>"><?= $status_info['text'] ?></span></td>
                                <td><?= $log['log_time'] ?></td>
                                <td>
                                    <?php if ($log['device_uid'] && $log['license_key'] && $status_info['text'] === '成功'): ?>
                                        <button class="btn btn-xs btn-info view-skeys-btn"
                                                data-license-key="<?= htmlspecialchars($log['license_key']) ?>"
                                                data-device-uid="<?= htmlspecialchars($log['device_uid']) ?>"
                                                title="查看Skeys">
                                            <i class="fas fa-key"></i>
                                        </button>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>

                    <?php if (empty($logs)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <p class="text-muted">没有找到符合条件的日志记录</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- 查看Skeys模态框 -->
<div class="modal fade" id="skeysModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-key"></i> 设备Skeys信息</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>激活码：</label>
                    <span id="modalSkeysLicenseKey" class="font-weight-bold text-primary"></span>
                </div>
                <div class="form-group">
                    <label>设备UID：</label>
                    <span id="modalSkeysDeviceUid" class="font-weight-bold text-info"></span>
                </div>
                <div class="form-group">
                    <label>当前Skeys：</label>
                    <textarea id="modalSkeysValue" class="form-control" rows="4" readonly></textarea>
                </div>
                <div class="text-center">
                    <button type="button" class="btn btn-primary" id="copySkeysBtn">
                        <i class="fas fa-copy"></i> 复制Skeys
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>

<script>
$(document).ready(function() {
    // 查看Skeys功能 - 使用事件委托
    $(document).on('click', '.view-skeys-btn', function() {
        var licenseKey = $(this).data('license-key');
        var deviceUid = $(this).data('device-uid');

        console.log('查看Skeys - 激活码:', licenseKey, '设备UID:', deviceUid); // 调试信息

        // 填充模态框基本信息
        $('#modalSkeysLicenseKey').text(licenseKey);
        $('#modalSkeysDeviceUid').text(deviceUid);
        $('#modalSkeysValue').val('正在获取...');

        // 显示模态框
        $('#skeysModal').modal('show');

        // 发送请求获取Skeys
        $.post('actions/get_skeys.php', {
            license_key: licenseKey,
            device_uid: deviceUid
        }).done(function(response) {
            console.log('Skeys响应:', response); // 调试信息

            try {
                var data = typeof response === 'string' ? JSON.parse(response) : response;
                if (data.success) {
                    $('#modalSkeysValue').val(data.skeys);
                } else {
                    $('#modalSkeysValue').val('获取失败：' + (data.error || data.message));
                }
            } catch (e) {
                console.error('Skeys响应解析失败:', response);
                $('#modalSkeysValue').val('获取失败：服务器响应错误');
            }
        }).fail(function(xhr, status, error) {
            console.error('Skeys请求失败:', xhr.responseText);
            $('#modalSkeysValue').val('获取失败：网络错误');
        });
    });

    // 复制Skeys
    $('#copySkeysBtn').on('click', function() {
        var $textarea = $('#modalSkeysValue');
        $textarea.select();
        try {
            document.execCommand('copy');
            $(this).html('<i class="fas fa-check"></i> 已复制').removeClass('btn-primary').addClass('btn-success');
            setTimeout(() => {
                $(this).html('<i class="fas fa-copy"></i> 复制Skeys').removeClass('btn-success').addClass('btn-primary');
            }, 2000);
        } catch (err) {
            alert('复制失败，请手动复制');
        }
    });
});
</script>