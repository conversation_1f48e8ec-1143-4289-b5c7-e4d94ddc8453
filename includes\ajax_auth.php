<?php
/**
 * AJAX请求专用的身份验证检查
 * 用于需要返回JSON响应的AJAX请求
 */

require_once __DIR__ . '/session_helper.php';

/**
 * 检查AJAX请求的登录状态
 * 如果未登录，返回JSON错误并退出
 */
function check_ajax_auth() {
    if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false, 
            'error' => '会话已过期，请重新登录',
            'redirect' => 'login.php'
        ]);
        exit();
    }
}

/**
 * 发送AJAX成功响应
 */
function ajax_success($data = [], $message = '操作成功') {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'message' => $message,
        'data' => $data
    ]);
    exit();
}

/**
 * 发送AJAX错误响应
 */
function ajax_error($message = '操作失败', $code = 400) {
    header('Content-Type: application/json');
    http_response_code($code);
    echo json_encode([
        'success' => false,
        'error' => $message
    ]);
    exit();
}

/**
 * 发送AJAX验证错误响应
 */
function ajax_validation_error($errors) {
    header('Content-Type: application/json');
    http_response_code(422);
    echo json_encode([
        'success' => false,
        'error' => '数据验证失败',
        'validation_errors' => $errors
    ]);
    exit();
}

// 不自动检查，让调用文件手动调用check_ajax_auth()
?>
