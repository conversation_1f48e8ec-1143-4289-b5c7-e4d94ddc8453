<?php
require_once 'includes/session_helper.php';
require_once 'includes/db.php';

// 强制设置登录状态
$_SESSION['loggedin'] = true;

// 如果是POST请求，直接处理解绑
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'test_unbind') {
    $device_id = (int)($_POST['device_id'] ?? 0);
    
    header('Content-Type: application/json');
    
    if ($device_id <= 0) {
        echo json_encode(['success' => false, 'message' => '无效的设备ID']);
        exit();
    }
    
    try {
        $pdo->beginTransaction();
        
        // 获取设备信息
        $check_stmt = $pdo->prepare("SELECT license_id, device_uid FROM license_devices WHERE id = ?");
        $check_stmt->execute([$device_id]);
        $device_info = $check_stmt->fetch();
        
        if (!$device_info) {
            $pdo->rollBack();
            echo json_encode(['success' => false, 'message' => '设备不存在(ID:' . $device_id . ')']);
            exit();
        }
        
        // 删除设备记录
        $delete_stmt = $pdo->prepare("DELETE FROM license_devices WHERE id = ?");
        $delete_result = $delete_stmt->execute([$device_id]);
        
        if ($delete_result && $delete_stmt->rowCount() > 0) {
            // 更新激活码计数
            $update_stmt = $pdo->prepare("UPDATE licenses SET bound_devices = bound_devices - 1, unbind_count = unbind_count + 1 WHERE id = ?");
            $update_stmt->execute([$device_info['license_id']]);
            
            $pdo->commit();
            echo json_encode([
                'success' => true,
                'message' => '设备解绑成功',
                'device_uid' => $device_info['device_uid'],
                'device_id' => $device_id
            ]);
        } else {
            $pdo->rollBack();
            echo json_encode(['success' => false, 'message' => '删除失败，影响行数：' . $delete_stmt->rowCount()]);
        }
    } catch (Exception $e) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => '解绑失败：' . $e->getMessage()]);
    }
    exit();
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>直接解绑测试</title>
    <meta charset="utf-8">
    <script src="assets/plugins/jquery/jquery.min.js"></script>
</head>
<body>
    <h2>直接解绑测试</h2>
    
    <h3>测试解绑设备ID 530</h3>
    <button onclick="testUnbind(530)">解绑设备ID 530</button>
    
    <h3>测试解绑设备ID 529</h3>
    <button onclick="testUnbind(529)">解绑设备ID 529</button>
    
    <div id="result" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc; background: #f9f9f9;"></div>
    
    <h3>调用原始API测试</h3>
    <button onclick="testOriginalAPI(530)">调用原始API解绑530</button>
    
    <script>
    function testUnbind(deviceId) {
        $('#result').html('<p>正在解绑设备ID: ' + deviceId + '...</p>');
        
        $.post('test_direct_unbind.php', {
            action: 'test_unbind',
            device_id: deviceId
        }).done(function(response) {
            console.log('解绑响应:', response);
            
            try {
                var result = typeof response === 'string' ? JSON.parse(response) : response;
                if (result.success) {
                    $('#result').html('<div style="color: green;"><strong>解绑成功！</strong><br>' + 
                        '消息: ' + result.message + '<br>' +
                        '设备UID: ' + result.device_uid + '<br>' +
                        '设备ID: ' + result.device_id + '</div>');
                } else {
                    $('#result').html('<div style="color: red;"><strong>解绑失败：</strong>' + result.message + '</div>');
                }
            } catch (e) {
                $('#result').html('<div style="color: red;"><strong>响应解析失败：</strong><br><pre>' + response + '</pre></div>');
                console.error('原始响应:', response);
            }
        }).fail(function(xhr, status, error) {
            $('#result').html('<div style="color: red;"><strong>请求失败：</strong>' + error + '<br><pre>' + xhr.responseText + '</pre></div>');
            console.error('失败详情:', xhr.responseText);
        });
    }
    
    function testOriginalAPI(deviceId) {
        $('#result').html('<p>正在调用原始API解绑设备ID: ' + deviceId + '...</p>');
        
        $.post('actions/update_license.php', {
            action: 'unbind_device',
            device_id: deviceId
        }).done(function(response) {
            console.log('原始API响应:', response);
            
            try {
                var result = typeof response === 'string' ? JSON.parse(response) : response;
                if (result.success) {
                    $('#result').html('<div style="color: green;"><strong>原始API解绑成功！</strong><br>' + result.message + '</div>');
                } else {
                    $('#result').html('<div style="color: red;"><strong>原始API解绑失败：</strong>' + result.message + '</div>');
                }
            } catch (e) {
                $('#result').html('<div style="color: red;"><strong>原始API响应解析失败：</strong><br><pre>' + response + '</pre></div>');
                console.error('原始API响应:', response);
            }
        }).fail(function(xhr, status, error) {
            $('#result').html('<div style="color: red;"><strong>原始API请求失败：</strong>' + error + '<br><pre>' + xhr.responseText + '</pre></div>');
            console.error('原始API失败详情:', xhr.responseText);
        });
    }
    </script>
    
    <style>
    button { 
        padding: 10px 15px; 
        margin: 5px; 
        background-color: #dc3545; 
        color: white; 
        border: none; 
        cursor: pointer; 
        border-radius: 4px;
    }
    button:hover { background-color: #c82333; }
    </style>
</body>
</html>
