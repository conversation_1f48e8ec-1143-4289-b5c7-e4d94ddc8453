<?php
require_once '../includes/check_login.php';
require_once '../includes/db.php';

// 处理批量操作 (POST请求)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $ids = $_POST['ids'] ?? [];

    if ($action === 'delete' && !empty($ids)) {
        $placeholders = implode(',', array_fill(0, count($ids), '?'));
        $stmt = $pdo->prepare("DELETE FROM licenses WHERE id IN ($placeholders)");
        $stmt->execute($ids);
    }
    // 注意：POST请求处理完后，不应再继续执行下面的GET逻辑，所以这里要 exit()
    header('Location: ../index.php');
    exit();
}

// 处理单项操作 (GET请求)
$id = (int)($_GET['id'] ?? 0);
$action = $_GET['action'] ?? '';

// ★★★ 增强：在GET请求中增加删除逻辑 ★★★
if ($id > 0 && in_array($action, ['enable', 'disable', 'delete'])) {
    if ($action === 'enable') {
        // ... (启用逻辑不变)
        $stmt_check = $pdo->prepare("SELECT device_uid FROM licenses WHERE id = ?");
        $stmt_check->execute([$id]);
        $license = $stmt_check->fetch();
        $new_status = $license && $license['device_uid'] ? 'active' : 'inactive';
        $stmt = $pdo->prepare("UPDATE licenses SET status = ? WHERE id = ?");
        $stmt->execute([$new_status, $id]);
    } elseif ($action === 'disable') { 
        // ... (禁用逻辑不变)
        $stmt = $pdo->prepare("UPDATE licenses SET status = 'disabled' WHERE id = ?");
        $stmt->execute([$id]);
    } elseif ($action === 'delete') {
        // 新增的单个删除逻辑
        $stmt = $pdo->prepare("DELETE FROM licenses WHERE id = ?");
        $stmt->execute([$id]);
    }
}

header('Location: ../index.php');
exit();
?>