下面是完整的、经过优化的方案和代码。

---

### 第 1 步：优化数据库设计

我们需要修改 `licenses` 表，将 `validity_days` 字段替换为一个更灵活的 `plan_id`，并创建一个新的 `plans` 表来管理套餐。

**1. 创建 `plans` 表 (套餐表)**

这个表用来存储不同的套餐，如“1小时体验卡”、“7天卡”、“年卡”等。

```sql
CREATE TABLE `plans` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `plan_name` varchar(100) NOT NULL COMMENT '套餐名称，如：1小时卡',
  `validity_value` int(11) NOT NULL COMMENT '有效期数值',
  `validity_unit` enum('hour','day','month','year') NOT NULL COMMENT '有效期单位',
  `notes` varchar(255) DEFAULT NULL COMMENT '套餐备注',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '套餐是否可用',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 插入一些默认套餐
INSERT INTO `plans` (`plan_name`, `validity_value`, `validity_unit`) VALUES
('1小时体验', 1, 'hour'),
('3小时体验', 3, 'hour'),
('1天卡', 1, 'day'),
('7天卡', 7, 'day'),
('15天卡', 15, 'day'),
('30天卡 (月卡)', 30, 'day'),
('90天卡 (季卡)', 90, 'day'),
('180天卡 (半年卡)', 180, 'day'),
('365天卡 (年卡)', 365, 'day');
```

**2. 修改 `licenses` 表 (激活码表)**

我们将移除 `validity_days`，并添加 `plan_id` 作为外键。

```sql
-- 如果你已经创建了旧的表，先删除它
-- DROP TABLE licenses;

CREATE TABLE `licenses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `license_key` varchar(50) NOT NULL,
  `plan_id` int(11) NOT NULL COMMENT '关联的套餐ID',
  `device_uid` varchar(32) DEFAULT NULL,
  `status` enum('inactive','active','expired','disabled') NOT NULL DEFAULT 'inactive',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `activated_at` datetime DEFAULT NULL,
  `expires_at` datetime DEFAULT NULL,
  `last_ip` varchar(45) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `license_key` (`license_key`),
  KEY `device_uid` (`device_uid`),
  KEY `plan_id` (`plan_id`),
  CONSTRAINT `licenses_ibfk_1` FOREIGN KEY (`plan_id`) REFERENCES `plans` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```
*   `ON DELETE RESTRICT`: 确保不能删除一个已经被激活码使用的套餐，保证数据完整性。

---

### 第 2 步：升级后端管理系统代码 (PHP)

我们将更新所有文件以支持新功能。

#### 文件结构 (保持不变)

```
/ppmt_admin/
├── api/
│   └── validate.php
├── actions/
│   ├── generate_keys.php
│   └── update_status.php
├── includes/
│   ├── db.php
│   └── header.php
│   └── footer.php
└── index.php
```

#### 修改后的代码

**1. `includes/db.php`** (根据您的信息修改)
```php
<?php
// --- 已根据您的信息修改 ---
$db_host = 'localhost'; // 通常是 localhost
$db_name = 'zhiyudazi';
$db_user = 'zhiyudazi';
$db_pass = 'zhiyudazi';
// --------------------

// 时区设置，确保时间计算准确
date_default_timezone_set('Asia/Shanghai');

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    die("数据库连接失败: " . $e->getMessage());
}
?>
```

**2. `api/validate.php`** (核心逻辑升级)
```php
<?php
require_once '../includes/db.php';

$uid = $_GET['uid'] ?? '';
$uks = $_GET['uks'] ?? ''; // User Key String (激活码)

if (empty($uid) || empty($uks)) {
    exit('');
}

// 联合查询激活码和其套餐信息
$stmt = $pdo->prepare("
    SELECT l.*, p.validity_value, p.validity_unit
    FROM licenses l
    JOIN plans p ON l.plan_id = p.id
    WHERE l.license_key = ?
");
$stmt->execute([$uks]);
$license = $stmt->fetch();

if (!$license) {
    exit(''); // 激活码不存在
}

if ($license['status'] === 'disabled') {
    exit(''); // 已被禁用
}

$now = new DateTime();
$expires_at = null;

// 根据有效期单位构建 DateTime 的 modify 字符串
$modify_str = '+' . $license['validity_value'] . ' ' . $license['validity_unit'];

if ($license['status'] === 'inactive') {
    $expires_at = new DateTime();
    $expires_at->modify($modify_str);

    $stmt = $pdo->prepare(
        "UPDATE licenses SET device_uid = ?, status = 'active', activated_at = NOW(), expires_at = ?, last_ip = ? WHERE id = ?"
    );
    $stmt->execute([$uid, $expires_at->format('Y-m-d H:i:s'), $_SERVER['REMOTE_ADDR'], $license['id']]);

} elseif ($license['status'] === 'active' || $license['status'] === 'expired') {
    if ($license['device_uid'] !== $uid) {
        exit(''); // 已绑定其他设备
    }
    $expires_at = new DateTime($license['expires_at']);
    if ($now > $expires_at) {
        if ($license['status'] !== 'expired') {
            $pdo->prepare("UPDATE licenses SET status = 'expired' WHERE id = ?")->execute([$license['id']]);
        }
        exit('');
    }
} else {
    exit('');
}

// --- 所有验证通过，开始生成skeys (这部分逻辑不变) ---

$d = (int)($expires_at->getTimestamp() * 1000);
$part2 = ($d + 10000) * 903 / 100000;
$part1 = md5($uid . $d);

$prstr = "ppmt";
$prnum = "1200";
$hash1 = md5($prstr . "100000000:00" . $part1 . $prstr . "100000000:00");
$part3 = md5($hash1 . $prstr . "100000000:00" . $part2 . $uid . $prnum);

$skeys = $part1 . $part2 . $part3;

echo $skeys;
?>
```

**3. `index.php`** (UI升级，支持按套餐生成)
```php
<?php
require_once 'includes/db.php';
require_once 'includes/header.php';

// 获取统计数据
$stats = $pdo->query("
    SELECT
        (SELECT COUNT(*) FROM licenses) as total,
        (SELECT COUNT(*) FROM licenses WHERE status = 'active') as active,
        (SELECT COUNT(*) FROM licenses WHERE status = 'inactive') as inactive,
        (SELECT COUNT(*) FROM licenses WHERE status = 'expired') as expired,
        (SELECT COUNT(*) FROM licenses WHERE status = 'disabled') as disabled
")->fetch();

// 获取所有激活的套餐
$plans = $pdo->query("SELECT * FROM plans WHERE is_active = 1 ORDER BY id")->fetchAll();

// 联合查询获取列表
$licenses = $pdo->query("
    SELECT l.*, p.plan_name 
    FROM licenses l 
    JOIN plans p ON l.plan_id = p.id 
    ORDER BY l.id DESC
")->fetchAll();
?>

<div class="container mt-4">
    <h2>PPMT助手 - 授权管理后台</h2>
    <hr>

    <h4>数据仪表盘</h4>
    <div class="row text-center">
        <div class="col-md"><div class="card p-3 mb-2 bg-primary text-white"><h5>总数</h5><h3><?= $stats['total'] ?></h3></div></div>
        <div class="col-md"><div class="card p-3 mb-2 bg-success text-white"><h5>激活</h5><h3><?= $stats['active'] ?></h3></div></div>
        <div class="col-md"><div class="card p-3 mb-2 bg-info text-white"><h5>未用</h5><h3><?= $stats['inactive'] ?></h3></div></div>
        <div class="col-md"><div class="card p-3 mb-2 bg-warning text-dark"><h5>过期</h5><h3><?= $stats['expired'] ?></h3></div></div>
        <div class="col-md"><div class="card p-3 mb-2 bg-danger text-white"><h5>禁用</h5><h3><?= $stats['disabled'] ?></h3></div></div>
    </div>
    <hr>

    <h4>生成激活码</h4>
    <form action="actions/generate_keys.php" method="post" class="form-inline">
        <div class="form-group mr-2">
            <label for="plan_id">选择套餐:</label>
            <select name="plan_id" id="plan_id" class="form-control" required>
                <?php foreach ($plans as $plan): ?>
                    <option value="<?= $plan['id'] ?>"><?= htmlspecialchars($plan['plan_name']) ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="form-group mr-2">
            <label for="count">数量:</label>
            <input type="number" name="count" id="count" class="form-control" value="10" required>
        </div>
        <div class="form-group mr-2">
            <label for="notes">备注 (可选):</label>
            <input type="text" name="notes" id="notes" class="form-control">
        </div>
        <button type="submit" class="btn btn-primary align-self-end">生成</button>
    </form>
    <hr>

    <h4>激活码列表</h4>
    <table class="table table-striped table-bordered table-sm">
        <thead class="thead-dark">
            <tr>
                <th>ID</th>
                <th>激活码</th>
                <th>套餐</th>
                <th>状态</th>
                <th>设备UID</th>
                <th>到期时间</th>
                <th>备注</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($licenses as $row): ?>
            <tr>
                <td><?= $row['id'] ?></td>
                <td><code><?= htmlspecialchars($row['license_key']) ?></code></td>
                <td><?= htmlspecialchars($row['plan_name']) ?></td>
                <td>
                    <span class="badge badge-<?php 
                        switch($row['status']) {
                            case 'active': echo 'success'; break;
                            case 'inactive': echo 'info'; break;
                            case 'expired': echo 'warning'; break;
                            case 'disabled': echo 'danger'; break;
                        }
                    ?>"><?= $row['status'] ?></span>
                </td>
                <td><small><?= htmlspecialchars($row['device_uid']) ?></small></td>
                <td><?= $row['expires_at'] ? date('Y-m-d H:i', strtotime($row['expires_at'])) : 'N/A' ?></td>
                <td><?= htmlspecialchars($row['notes']) ?></td>
                <td>
                    <?php if ($row['status'] === 'disabled'): ?>
                        <a href="actions/update_status.php?id=<?= $row['id'] ?>&status=enable" class="btn btn-sm btn-success" title="启用">✔️</a>
                    <?php else: ?>
                        <a href="actions/update_status.php?id=<?= $row['id'] ?>&status=disabled" class="btn btn-sm btn-danger" title="禁用">❌</a>
                    <?php endif; ?>
                </td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>

<?php require_once 'includes/footer.php'; ?>
```

**4. `actions/generate_keys.php`** (升级为按套餐生成)
```php
<?php
require_once '../includes/db.php';

$plan_id = (int)($_POST['plan_id'] ?? 0);
$count = (int)($_POST['count'] ?? 1);
$notes = $_POST['notes'] ?? '';

// 验证套餐ID是否存在
$stmt_check = $pdo->prepare("SELECT id FROM plans WHERE id = ? AND is_active = 1");
$stmt_check->execute([$plan_id]);
if ($stmt_check->fetchColumn() === false) {
    die('无效的套餐ID');
}

if ($count > 0 && $count <= 1000) {
    $stmt = $pdo->prepare("INSERT INTO licenses (license_key, plan_id, notes) VALUES (?, ?, ?)");
    for ($i = 0; $i < $count; $i++) {
        $key = strtoupper(substr(md5(uniqid(rand(), true)), 0, 16));
        $stmt->execute([$key, $plan_id, $notes]);
    }
}

header('Location: ../index.php');
exit();
?>
```

**5. `actions/update_status.php`** (升级，启用逻辑更智能)
```php
<?php
require_once '../includes/db.php';

$id = (int)($_GET['id'] ?? 0);
$status_action = $_GET['status'] ?? '';

if ($id > 0 && in_array($status_action, ['enable', 'disabled'])) {
    
    if ($status_action === 'enable') {
        // 智能判断启用后应该恢复到什么状态
        $stmt_check = $pdo->prepare("SELECT device_uid FROM licenses WHERE id = ?");
        $stmt_check->execute([$id]);
        $license = $stmt_check->fetch();
        
        $new_status = 'inactive'; // 默认恢复到未使用
        if ($license && $license['device_uid']) {
            // 如果已经绑定过设备，则恢复到激活状态
            $new_status = 'active';
        }

        $stmt = $pdo->prepare("UPDATE licenses SET status = ? WHERE id = ?");
        $stmt->execute([$new_status, $id]);

    } else { // 'disabled'
        $stmt = $pdo->prepare("UPDATE licenses SET status = 'disabled' WHERE id = ?");
        $stmt->execute([$id]);
    }
}

header('Location: ../index.php');
exit();
?>
```
*   `header.php` 和 `footer.php` 保持不变。

---

### 第 3 步：部署和客户端修改说明

#### 服务器部署

1.  **数据库**：
    *   登录您的MySQL。
    *   创建一个名为 `zhiyudazi` 的数据库（如果尚未创建）。
    *   执行第 1 步中的两个 `CREATE TABLE` 和一个 `INSERT` SQL语句，创建并初始化 `plans` 和 `licenses` 表。
2.  **文件上传**：
    *   将包含所有新代码的 `ppmt_admin` 文件夹上传到您的服务器网站根目录。
3.  **配置**：
    *   确保 `includes/db.php` 中的数据库连接信息正确无误。

#### 客户端代码修改 (`main.js`)

**这部分完全不需要修改！**

因为我们后端的 `api/validate.php` 接口返回的格式和原始脚本所期望的完全一致，客户端的验证逻辑可以保持原样。

您只需要确保 `main.js` 中 `ifurl` 数组指向了您正确的服务器地址即可。

**确认 `main.js` 中的配置：**

```javascript
// 您的服务器验证API地址是: http://run.zhiyudazi.cn/ppmt_admin/api/
// 将其进行 Base64 编码，得到: aHR0cDovL3J1bi56aGl5dWRhemkuY24vcHBtdF9hZG1pbi9hcGkv

// 在 main.js 中找到这行代码
// ifurl=["aHR0cDovL3R5LmF1dG9qcy5yZW4v","90f832a2104","0","1","3","2"]

// 将其替换为:
ifurl = ["aHR0cDovL3J1bi56aGl5dWRhemkuY24vcHBtdF9hZG1pbi9hcGkv", "validate.php", "0", "1", "3", "2"];
```

*注意：您的域名 `run.zhiyudazi.cn` 可能是 `http` 或 `https`，请根据实际情况进行Base64编码。我这里用 `http` 作为示例。*

---

### 新系统功能总结

1.  **套餐管理**：你可以在 `plans` 表中自由添加、修改各种时长的套餐。
2.  **按套餐生成激活码**：后台管理界面提供下拉菜单，让你可以选择一个套餐来批量生成激活码。
3.  **智能激活**：用户激活时，系统会自动根据激活码所属的套餐计算正确的到期时间。
4.  **智能禁用/启用**：
    *   可以禁用任何状态的激活码。
    *   启用一个被禁用的激活码时，系统会自动判断它之前是否已使用，并恢复到正确的状态（`inactive`或`active`）。
5.  **清晰的列表展示**：后台列表会清晰地显示每个激活码所属的套餐名称。

这套系统现在非常灵活且功能强大，足以支撑您的商业化运营需求。