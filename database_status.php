<?php
require_once 'includes/db.php';
require_once 'includes/db_adapter.php';

$adapter = new DatabaseAdapter($pdo);
$structure_info = $adapter->getStructureInfo();
$monitor_tables = $adapter->getMonitorTables();

?>
<!DOCTYPE html>
<html>
<head>
    <title>数据库状态检查</title>
    <link rel="stylesheet" href="assets/plugins/admin-lte/css/adminlte.min.css">
    <link rel="stylesheet" href="assets/plugins/fontawesome-free/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <h2><i class="fas fa-database"></i> 数据库状态检查</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-info">
                        <h3 class="card-title">数据库结构信息</h3>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>结构类型:</strong></td>
                                <td>
                                    <span class="badge bg-<?= $structure_info['structure_type'] === 'new' ? 'success' : 'warning' ?>">
                                        <?= $structure_info['structure_type'] === 'new' ? '新结构 (多设备支持)' : '旧结构 (单设备)' ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>license_devices表:</strong></td>
                                <td>
                                    <span class="badge bg-<?= $structure_info['has_license_devices'] ? 'success' : 'secondary' ?>">
                                        <?= $structure_info['has_license_devices'] ? '存在' : '不存在' ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>skeys_records表:</strong></td>
                                <td>
                                    <span class="badge bg-<?= $structure_info['has_skeys_records'] ? 'success' : 'secondary' ?>">
                                        <?= $structure_info['has_skeys_records'] ? '存在' : '不存在' ?>
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success">
                        <h3 class="card-title">表状态监控</h3>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <?php foreach ($monitor_tables as $table): ?>
                                <?php
                                try {
                                    $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
                                    $count = $stmt->fetchColumn();
                                    $status = 'success';
                                    $message = $count . ' 条记录';
                                } catch (Exception $e) {
                                    $status = 'danger';
                                    $message = '错误: ' . $e->getMessage();
                                }
                                ?>
                                <tr>
                                    <td><strong><?= $table ?>:</strong></td>
                                    <td>
                                        <span class="badge bg-<?= $status ?>">
                                            <?= $message ?>
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-warning">
                        <h3 class="card-title">迁移建议</h3>
                    </div>
                    <div class="card-body">
                        <?php if ($structure_info['structure_type'] === 'legacy'): ?>
                            <div class="alert alert-warning">
                                <h5><i class="fas fa-exclamation-triangle"></i> 当前使用旧数据库结构</h5>
                                <p><strong>特点:</strong></p>
                                <ul>
                                    <li>每个激活码只能绑定一个设备</li>
                                    <li>设备信息存储在licenses表的device_uid字段</li>
                                    <li>功能完整，适合简单场景</li>
                                </ul>
                                
                                <p><strong>升级到新结构的好处:</strong></p>
                                <ul>
                                    <li>支持一码多设备</li>
                                    <li>更好的设备管理</li>
                                    <li>Skeys搜索功能</li>
                                    <li>更详细的设备统计</li>
                                </ul>
                                
                                <p><strong>迁移方案:</strong></p>
                                <ol>
                                    <li>备份现有数据库</li>
                                    <li>执行 <code>database_migration.sql</code></li>
                                    <li>验证数据完整性</li>
                                    <li>更新代码配置</li>
                                </ol>
                                
                                <a href="database_migration.sql" class="btn btn-warning" download>
                                    <i class="fas fa-download"></i> 下载迁移脚本
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-success">
                                <h5><i class="fas fa-check-circle"></i> 当前使用新数据库结构</h5>
                                <p><strong>功能特点:</strong></p>
                                <ul>
                                    <li>✅ 支持一码多设备</li>
                                    <li>✅ 独立的设备管理表</li>
                                    <li>✅ Skeys搜索功能</li>
                                    <li>✅ 详细的设备统计</li>
                                </ul>
                                <p>您的数据库结构是最新的，无需迁移。</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-primary">
                        <h3 class="card-title">测试功能</h3>
                    </div>
                    <div class="card-body">
                        <div class="btn-group" role="group">
                            <a href="test_simple.php" class="btn btn-primary">
                                <i class="fas fa-flask"></i> 功能测试
                            </a>
                            <a href="index.php" class="btn btn-success">
                                <i class="fas fa-home"></i> 返回首页
                            </a>
                            <a href="system_monitor.php" class="btn btn-info">
                                <i class="fas fa-chart-line"></i> 系统监控
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
