<?php
require_once 'includes/session_helper.php';
require_once 'includes/db.php';

// 测试解绑功能
if ($_POST['action'] ?? '' === 'test_unbind') {
    $device_id = (int)($_POST['device_id'] ?? 0);
    
    echo "<h3>测试解绑功能</h3>";
    echo "<p>设备ID: $device_id</p>";
    echo "<p>会话状态: " . (isset($_SESSION['loggedin']) ? '已登录' : '未登录') . "</p>";
    
    if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
        echo "<p style='color: red;'>错误：会话未登录</p>";
        exit();
    }
    
    if ($device_id > 0) {
        try {
            // 查询设备信息
            $stmt = $pdo->prepare("SELECT * FROM license_devices WHERE id = ?");
            $stmt->execute([$device_id]);
            $device = $stmt->fetch();
            
            if ($device) {
                echo "<p>找到设备: " . htmlspecialchars($device['device_uid']) . "</p>";
                
                // 执行删除
                $delete_stmt = $pdo->prepare("DELETE FROM license_devices WHERE id = ?");
                $result = $delete_stmt->execute([$device_id]);
                
                if ($result && $delete_stmt->rowCount() > 0) {
                    echo "<p style='color: green;'>删除成功，影响行数: " . $delete_stmt->rowCount() . "</p>";
                } else {
                    echo "<p style='color: red;'>删除失败，影响行数: " . $delete_stmt->rowCount() . "</p>";
                }
            } else {
                echo "<p style='color: red;'>设备不存在</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>错误: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: red;'>无效的设备ID</p>";
    }
    exit();
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>测试解绑功能</title>
    <script src="assets/plugins/jquery/jquery.min.js"></script>
</head>
<body>
    <h2>解绑功能测试</h2>
    
    <form method="post">
        <input type="hidden" name="action" value="test_unbind">
        <label>设备ID:</label>
        <input type="number" name="device_id" value="530" required>
        <button type="submit">测试解绑</button>
    </form>
    
    <hr>
    
    <h3>AJAX测试</h3>
    <button onclick="testAjaxUnbind()">测试AJAX解绑</button>
    
    <div id="result"></div>
    
    <script>
    function testAjaxUnbind() {
        $.post('actions/update_license.php', {
            action: 'unbind_device',
            device_id: 530
        }).done(function(response) {
            console.log('响应:', response);
            $('#result').html('<pre>' + JSON.stringify(response, null, 2) + '</pre>');
        }).fail(function(xhr, status, error) {
            console.error('失败:', xhr.responseText);
            $('#result').html('<pre>失败: ' + xhr.responseText + '</pre>');
        });
    }
    </script>
</body>
</html>
