<?php
// 临时移除登录检查
// require_once 'includes/check_login.php';
require_once 'includes/db.php';
require_once 'includes/header.php';
require_once 'includes/sidebar.php';
// 筛选逻辑
$filter_plan = (int)($_GET['plan'] ?? 0);
$filter_status = $_GET['status'] ?? '';
$where_clauses = [];
$params = [];

if ($filter_plan > 0) {
    $where_clauses[] = "l.plan_id = ?";
    $params[] = $filter_plan;
}

if (!empty($filter_status)) {
    $where_clauses[] = "l.status = ?";
    $params[] = $filter_status;
}
// 获取基础统计信息
$stats = $pdo->query("
    SELECT
        (SELECT COUNT(*) FROM licenses) as total,
        (SELECT COUNT(*) FROM licenses WHERE status = 'active') as active,
        (SELECT COUNT(*) FROM licenses WHERE status = 'inactive') as inactive,
        (SELECT COUNT(*) FROM licenses WHERE status = 'expired') as expired,
        (SELECT COUNT(*) FROM licenses WHERE status = 'disabled') as disabled
")->fetch();

// 获取今日统计信息
$today = date('Y-m-d');
$today_stats = $pdo->query("
    SELECT
        (SELECT COUNT(*) FROM logs WHERE DATE(log_time) = '$today' AND log_type = 'activate' AND status = 'SUCCESS') as today_activations,
        (SELECT COUNT(*) FROM logs WHERE DATE(log_time) = '$today' AND log_type = 'verify' AND status = 'SUCCESS') as today_verifications,
        (SELECT COUNT(DISTINCT device_uid) FROM logs WHERE DATE(log_time) = '$today' AND status = 'SUCCESS') as today_unique_devices,
        (SELECT COUNT(*) FROM license_devices WHERE DATE(activated_at) = '$today') as today_new_devices,
        (SELECT COUNT(DISTINCT license_key) FROM logs WHERE DATE(log_time) = '$today' AND log_type = 'activate' AND status = 'SUCCESS') as today_activated_keys
")->fetch();

// 获取设备统计信息 - 新数据库结构
$device_stats = $pdo->query("
    SELECT
        (SELECT COUNT(*) FROM license_devices WHERE status = 'active') as total_devices,
        (SELECT COUNT(DISTINCT license_id) FROM license_devices WHERE status = 'active') as licenses_with_devices,
        (SELECT COUNT(*) FROM logs WHERE log_type = 'verify' AND status = 'SUCCESS') as total_verifications,
        (SELECT COALESCE(AVG(verify_count), 0) FROM license_devices WHERE status = 'active') as avg_verifications
")->fetch();
$sql = "SELECT l.*, p.plan_name FROM licenses l JOIN plans p ON l.plan_id = p.id";
if (!empty($where_clauses)) {
    $sql .= " WHERE " . implode(' AND ', $where_clauses);
}
$sql .= " ORDER BY l.id DESC";
$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$licenses = $stmt->fetchAll();

// 为每个激活码获取绑定的设备信息
foreach ($licenses as &$license) {
    $device_stmt = $pdo->prepare("SELECT id, device_uid, device_name, activated_at, last_verify_at, verify_count, status FROM license_devices WHERE license_id = ? ORDER BY activated_at DESC");
    $device_stmt->execute([$license['id']]);
    $license['devices'] = $device_stmt->fetchAll();
}
$status_map = [
    'active' => ['text' => '已激活', 'color' => 'success'],
    'inactive' => ['text' => '未使用', 'color' => 'info'],
    'expired' => ['text' => '已过期', 'color' => 'warning'],
    'disabled' => ['text' => '已禁用', 'color' => 'danger'],
];
?>

<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
    <!-- (顶部内容不变) -->
    <div class="content-header"><div class="container-fluid"><h1 class="m-0">仪表盘</h1></div></div>
    <section class="content"><div class="container-fluid">
    <!-- 基础统计 -->
    <div class="row">
        <div class="col-12 col-sm-6 col-md-2">
            <div class="info-box">
                <span class="info-box-icon bg-primary elevation-1"><i class="fas fa-hdd"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">总激活码</span>
                    <span class="info-box-number"><?= $stats['total'] ?></span>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-2">
            <div class="info-box mb-3">
                <span class="info-box-icon bg-success elevation-1"><i class="fas fa-check-circle"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">已激活</span>
                    <span class="info-box-number"><?= $stats['active'] ?></span>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-2">
            <div class="info-box mb-3">
                <span class="info-box-icon bg-info elevation-1"><i class="fas fa-pause-circle"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">未使用</span>
                    <span class="info-box-number"><?= $stats['inactive'] ?></span>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-2">
            <div class="info-box mb-3">
                <span class="info-box-icon bg-danger elevation-1"><i class="fas fa-times-circle"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">已禁用</span>
                    <span class="info-box-number"><?= $stats['disabled'] ?></span>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-4">
            <div class="info-box mb-3">
                <span class="info-box-icon bg-warning elevation-1"><i class="fas fa-mobile-alt"></i></span>
                <div class="info-box-content">
                    <span class="info-box-text">总设备数</span>
                    <span class="info-box-number"><?= $device_stats['total_devices'] ?></span>
                </div>
            </div>
        </div>

    </div>

    <!-- 今日统计 -->
    <div class="row">
        <div class="col-12">
            <div class="card card-info">
                <div class="card-header">
                    <h3 class="card-title"><i class="fas fa-calendar-day"></i> 今日统计</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12 col-sm-6 col-md-2">
                            <div class="info-box bg-gradient-success">
                                <span class="info-box-icon"><i class="fas fa-key"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">今日激活</span>
                                    <span class="info-box-number"><?= $today_stats['today_activations'] ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-sm-6 col-md-2">
                            <div class="info-box bg-gradient-info">
                                <span class="info-box-icon"><i class="fas fa-shield-alt"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">今日验证</span>
                                    <span class="info-box-number"><?= $today_stats['today_verifications'] ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-sm-6 col-md-2">
                            <div class="info-box bg-gradient-warning">
                                <span class="info-box-icon"><i class="fas fa-mobile-alt"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">活跃设备</span>
                                    <span class="info-box-number"><?= $today_stats['today_unique_devices'] ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-sm-6 col-md-2">
                            <div class="info-box bg-gradient-primary">
                                <span class="info-box-icon"><i class="fas fa-plus"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">新增设备</span>
                                    <span class="info-box-number"><?= $today_stats['today_new_devices'] ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-sm-6 col-md-2">
                            <div class="info-box bg-gradient-secondary">
                                <span class="info-box-icon"><i class="fas fa-unlock"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">激活卡密</span>
                                    <span class="info-box-number"><?= $today_stats['today_activated_keys'] ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-sm-6 col-md-2">
                            <div class="info-box bg-gradient-dark">
                                <span class="info-box-icon"><i class="fas fa-chart-line"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">平均验证</span>
                                    <span class="info-box-number"><?= number_format($device_stats['avg_verifications'], 1) ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="card card-primary card-outline">
        <!-- (生成激活码表单不变) -->
        <div class="card-header"><h3 class="card-title"><i class="fas fa-key"></i> 生成激活码</h3></div>
        <div class="card-body">
    <form id="generateKeysForm" action="actions/generate_keys.php" method="post" class="row g-3 align-items-end">
        <!-- 第一行 -->
        <div class="col-md-4">
            <label for="plan_id_gen" class="form-label">选择套餐:</label>
            <select name="plan_id" id="plan_id_gen" class="form-control" required>
                <?php 
                // 从数据库获取可用的套餐
                $gen_plans = $pdo->query("SELECT * FROM plans WHERE is_active = 1 ORDER BY id")->fetchAll();
                foreach ($gen_plans as $plan):
                ?>
                <option value="<?= $plan['id'] ?>">
                    <?= htmlspecialchars($plan['plan_name']) ?>
                    (<?= $plan['max_devices'] ?>设备)
                </option>
                <?php endforeach; ?>
            </select>
        </div>
        
        <!-- ★★★ 新增：自定义前缀输入框 ★★★ -->
        <div class="col-md-3">
            <label for="prefix" class="form-label">激活码前缀:</label>
            <div class="input-group">
                <select class="form-control" id="prefixSelect" style="max-width: 120px;">
                    <option value="">历史前缀</option>
                    <?php
                    // 获取历史使用的前缀
                    $prefix_query = "SELECT DISTINCT SUBSTRING_INDEX(license_key, '-', 1) as prefix_part
                                   FROM licenses
                                   WHERE license_key LIKE '%-%'
                                   ORDER BY prefix_part";
                    $prefixes = $pdo->query($prefix_query)->fetchAll();
                    foreach ($prefixes as $prefix_row):
                        $prefix_value = htmlspecialchars($prefix_row['prefix_part']);
                        if (!empty($prefix_value) && strlen($prefix_value) <= 10):
                    ?>
                    <option value="<?= $prefix_value ?>-"><?= $prefix_value ?>-</option>
                    <?php
                        endif;
                    endforeach;
                    ?>
                </select>
                <input type="text" name="prefix" id="prefix" class="form-control" placeholder="或输入新前缀" value="TP-">
            </div>
            <small class="form-text text-muted">可选择历史前缀或输入新前缀</small>
        </div>

        <div class="col-md-2">
            <label for="count" class="form-label">生成数量:</label>
            <input type="number" name="count" id="count" class="form-control" value="1" min="1" max="1000" required>
        </div>

        <div class="col-md-2">
            <label for="max_devices" class="form-label">设备数量:</label>
            <input type="number" name="max_devices" id="max_devices" class="form-control" value="1" min="1" max="100" required title="每个激活码最多可绑定的设备数量">
        </div>

        <div class="col-md-3">
            <label for="notes_gen" class="form-label">备注信息:</label>
            <input type="text" name="notes" id="notes_gen" class="form-control" placeholder="可选">
        </div>

        <div class="col-md-2">
            <button type="submit" class="btn btn-primary w-100">生成激活码</button>
        </div>
    </form>
</div>
    </div>
    <div class="card card-primary card-outline">
        <!-- (卡片头部和筛选器不变) -->
        <div class="card-header">
            <h3 class="card-title"><i class="fas fa-list-alt"></i> 激活码列表</h3>
            <div class="card-tools">
                <form method="get" class="d-inline-block">
                    <div class="input-group input-group-sm" style="width: 350px;">
                        <select name="plan" class="form-control" onchange="this.form.submit()">
                            <option value="">-- 按套餐筛选 --</option>
                            <?php
                            $available_plans_query = "SELECT p.id, p.plan_name FROM plans p WHERE p.is_active = 1 AND p.id IN (SELECT DISTINCT plan_id FROM licenses) ORDER BY p.id ASC";
                            $available_plans = $pdo->query($available_plans_query)->fetchAll();
                            $filter_plan = $_GET['plan'] ?? '';
                            foreach ($available_plans as $plan):
                            ?>
                            <option value="<?= $plan['id'] ?>" <?= $filter_plan == $plan['id'] ? 'selected' : '' ?>><?= htmlspecialchars($plan['plan_name']) ?></option>
                            <?php endforeach; ?>
                        </select>
                        <select name="status" class="form-control" onchange="this.form.submit()">
                            <option value="">-- 按状态筛选 --</option>
                            <?php
                            $filter_status = $_GET['status'] ?? '';
                            ?>
                            <option value="active" <?= $filter_status == 'active' ? 'selected' : '' ?>>已激活</option>
                            <option value="inactive" <?= $filter_status == 'inactive' ? 'selected' : '' ?>>未使用</option>
                            <option value="expired" <?= $filter_status == 'expired' ? 'selected' : '' ?>>已过期</option>
                            <option value="disabled" <?= $filter_status == 'disabled' ? 'selected' : '' ?>>已禁用</option>
                        </select>
                        <div class="input-group-append">
                            <a href="index.php" class="btn btn-outline-secondary" title="清除筛选"><i class="fas fa-times"></i></a>
                        </div>
                    </div>
                </form>
                <div class="btn-group ml-2">
                    <button type="button" class="btn btn-sm btn-info" id="copySelectedBtn"><i class="fas fa-copy"></i> 批量复制</button>
                    <button type="button" class="btn btn-sm btn-danger" id="deleteSelectedBtn"><i class="fas fa-trash"></i> 批量删除</button>
                    <div class="btn-group">
                        <button type="button" class="btn btn-sm btn-success dropdown-toggle" data-toggle="dropdown">
                            <i class="fas fa-download"></i> 导出数据
                        </button>
                        <div class="dropdown-menu">
                            <a class="dropdown-item" href="#" onclick="exportData('licenses', 'csv')">
                                <i class="fas fa-file-csv"></i> 导出CSV
                            </a>
                            <a class="dropdown-item" href="#" onclick="exportData('licenses', 'xls')">
                                <i class="fas fa-file-excel"></i> 导出Excel
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <!-- ★★★ 核心修正：移除了外围的<form>标签 ★★★ -->
            <table id="licensesTable" class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th class="no-sort" style="width: 40px;"><input type="checkbox" id="selectAll"></th>
                        <th style="width: 60px;">ID</th>
                        <th style="width: 200px;">激活码</th>
                        <th style="width: 120px;">套餐</th>
                        <th style="width: 100px;">状态</th>
                        <th style="width: 250px;">绑定设备</th>
                        <th style="width: 120px;">激活时间</th>
                        <th style="width: 120px;">到期时间</th>
                        <th>备注</th>
                        <th class="no-sort" style="width: 120px;">操作</th>
                    </tr>
                </thead>
               <tbody>
    <?php foreach ($licenses as $license_item): // ★ 使用一个全新的循环变量名，避免任何可能的冲突
        // 计算激活进度 - 新数据库结构
        $max_devices = $license_item['max_devices'] ?? 1;
        $bound_devices = count($license_item['devices']);
        $active_devices = count(array_filter($license_item['devices'], function($device) {
            return $device['status'] === 'active';
        }));

        // 重新计算状态
        if ($license_item['status'] === 'inactive') {
            $status_info = $status_map['inactive'];
        } elseif ($license_item['status'] === 'disabled') {
            $status_info = $status_map['disabled'];
        } elseif ($license_item['expires_at'] && new DateTime() > new DateTime($license_item['expires_at'])) {
            $status_info = $status_map['expired'];
        } elseif ($active_devices >= $max_devices) {
            $status_info = ['text' => "已激活 {$active_devices}/{$max_devices}", 'color' => 'success'];
        } elseif ($active_devices > 0) {
            $status_info = ['text' => "部分激活 {$active_devices}/{$max_devices}", 'color' => 'warning'];
        } else {
            $status_info = $status_map[$license_item['status']];
        }

        $activated_display = $license_item['activated_at'] ? date('Y-m-d H:i', strtotime($license_item['activated_at'])) : '<i class="text-muted">--</i>';
        $expires_display = 'N/A';
        if ($license_item['expires_at']) {
            $expires_display = date('Y-m-d H:i', strtotime($license_item['expires_at']));
        }
    ?>
    <tr data-id="<?= $license_item['id'] ?>">
        <!-- ★★★ 确保 value 的值是来自新的循环变量 ★★★ -->
        <td><input type="checkbox" class="license-checkbox" value="<?= $license_item['id'] ?>"></td>
        <td><?= $license_item['id'] ?></td>
        <td class="license-key-cell">
            <span class="license-key-text" data-key="<?= htmlspecialchars($license_item['license_key']) ?>"
                  style="cursor: pointer; color: #007bff;"
                  title="点击复制激活码">
                <?= htmlspecialchars($license_item['license_key']) ?>
            </span>
        </td>
        <td class="plan-name-cell">
            <?= htmlspecialchars($license_item['plan_name']) ?>
            <br><small class="text-muted"><?= $license_item['max_devices'] ?? 1 ?>设备</small>
        </td>
        <td><span class="badge bg-<?= $status_info['color'] ?>"><?= $status_info['text'] ?></span></td>
        <td style="width: 250px; max-width: 250px; overflow: hidden;">
            <?php if (empty($license_item['devices'])): ?>
                <span class="text-muted">未绑定</span>
            <?php else: ?>
                <div class="device-compact-list">
                    <small class="text-info d-block mb-1" style="font-size: 10px; font-weight: bold;">
                        已激活 <?= $active_devices ?>/<?= $max_devices ?>
                    </small>
                    <?php foreach ($license_item['devices'] as $index => $device): ?>
                        <div class="device-compact-item" style="display: flex; align-items: center; margin-bottom: 2px;">
                            <span class="device-info" style="flex: 1; font-size: 10px; word-break: break-all;">
                                <?= htmlspecialchars($device['device_uid']) ?>
                            </span>
                            <button class="btn btn-xs btn-outline-danger unbind-device-btn"
                                    style="padding: 1px 4px; font-size: 10px; margin-left: 4px;"
                                    data-device-id="<?= $device['id'] ?>"
                                    data-device-uid="<?= htmlspecialchars($device['device_uid']) ?>"
                                    title="解绑设备">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </td>
        <td><?= $activated_display ?></td>
        <td><?= $expires_display ?></td>
        <td class="notes-cell" data-original-text="<?= htmlspecialchars($license_item['notes']) ?>">
            <span class="notes-text"><?= htmlspecialchars($license_item['notes']) ?></span>
            <input type="text" class="form-control form-control-sm notes-input" style="display:none;" value="<?= htmlspecialchars($license_item['notes']) ?>">
        </td>
        <td>
            <div class="btn-group">
                <button class="btn btn-sm btn-secondary edit-notes-btn" title="修改备注"><i class="fas fa-pencil-alt"></i></button>
                <button class="btn btn-sm btn-info add-devices-btn" title="增加设备数" data-id="<?= $license_item['id'] ?>" data-current="<?= $license_item['max_devices'] ?? 1 ?>"><i class="fas fa-mobile-alt"></i></button>
                <?php if($license_item['status'] === 'active' || $license_item['status'] === 'expired'): ?>
                <button class="btn btn-sm btn-primary add-time-btn" title="增加时间"><i class="fas fa-plus-circle"></i></button>
                <?php endif; ?>
                <?php if ($license_item['status'] === 'disabled'): ?>
                    <a href="actions/update_status.php?id=<?= $license_item['id'] ?>&action=enable" class="btn btn-sm btn-success" title="启用"><i class="fas fa-check"></i></a>
                <?php else: ?>
                    <a href="actions/update_status.php?id=<?= $license_item['id'] ?>&action=disable" class="btn btn-sm btn-warning" title="禁用"><i class="fas fa-times"></i></a>
                <?php endif; ?>
                <a href="actions/update_status.php?id=<?= $license_item['id'] ?>&action=delete" class="btn btn-sm btn-danger single-delete-btn" title="删除"><i class="fas fa-trash"></i></a>
            </div>
        </td>
    </tr>
    <?php endforeach; ?>
</tbody>
            </table>
        </div>
    </div>
    </div></section>
</div>
<!-- (模态框和footer保持不变) -->
<textarea id="copyBuffer" style="position: absolute; left: -9999px;"></textarea>

<!-- 增加时间模态框 -->
<div class="modal fade" id="addTimeModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-plus-circle"></i> 增加时间</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="actions/update_license.php" method="post">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_time">
                    <input type="hidden" name="id" id="modalLicenseId">

                    <div class="form-group">
                        <label>激活码：</label>
                        <span id="modalLicenseKey" class="font-weight-bold text-primary"></span>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="add_value">增加数量：</label>
                                <input type="number" name="add_value" id="add_value" class="form-control" min="1" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="add_unit">时间单位：</label>
                                <select name="add_unit" id="add_unit" class="form-control" required>
                                    <option value="hour">小时</option>
                                    <option value="day" selected>天</option>
                                    <option value="month">月</option>
                                    <option value="year">年</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">确认增加</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 增加设备数模态框 -->
<div class="modal fade" id="addDevicesModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-mobile-alt"></i> 增加设备数量</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="actions/update_license.php" method="post">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_devices">
                    <input type="hidden" name="id" id="modalDevicesLicenseId">

                    <div class="form-group">
                        <label>激活码：</label>
                        <span id="modalDevicesLicenseKey" class="font-weight-bold text-primary"></span>
                    </div>

                    <div class="form-group">
                        <label>当前设备数量：</label>
                        <span id="modalCurrentDevices" class="font-weight-bold text-info"></span>
                    </div>

                    <div class="form-group">
                        <label for="new_max_devices">新的设备数量：</label>
                        <input type="number" name="new_max_devices" id="new_max_devices" class="form-control" min="1" max="100" required>
                        <small class="form-text text-muted">设置该激活码最多可以绑定的设备数量</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-info">确认修改</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 生成成功弹窗 -->
<div class="modal fade" id="generateSuccessModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title"><i class="fas fa-check-circle"></i> 激活码生成成功</h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    成功生成 <span id="generatedCount">0</span> 个激活码，套餐：<span id="generatedPlan"></span>
                </div>

                <div class="form-group">
                    <label>生成的激活码：</label>
                    <textarea id="generatedKeysText" class="form-control" rows="10" readonly></textarea>
                </div>

                <div class="text-center">
                    <button type="button" class="btn btn-primary" id="copyGeneratedKeys">
                        <i class="fas fa-copy"></i> 复制所有激活码
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-success" data-dismiss="modal" onclick="location.reload()">
                    <i class="fas fa-refresh"></i> 刷新页面
                </button>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>