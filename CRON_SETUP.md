# PPMT 系统定时任务设置指南

## 概述

为了保持系统的最佳性能，建议设置以下定时任务：

## 1. 日志清理任务

### 每日清理（推荐）
```bash
# 每天凌晨2点执行常规清理
0 2 * * * /usr/bin/php /path/to/ppmt_admin/scripts/cleanup_logs.php normal

# 或者使用智能清理（根据数据量自动调整）
0 2 * * * /usr/bin/php /path/to/ppmt_admin/scripts/cleanup_logs.php smart
```

### 每周深度清理
```bash
# 每周日凌晨3点执行智能清理
0 3 * * 0 /usr/bin/php /path/to/ppmt_admin/scripts/cleanup_logs.php smart
```

## 2. 数据库备份任务

### 每日备份
```bash
# 每天凌晨1点执行备份
0 1 * * * /usr/bin/php /path/to/ppmt_admin/scripts/backup.php
```

### 每周完整备份
```bash
# 每周日凌晨1点执行完整备份
0 1 * * 0 mysqldump -u username -p'password' database_name > /path/to/backups/weekly_backup_$(date +\%Y\%m\%d).sql
```

## 3. 系统监控任务

### 磁盘空间检查
```bash
# 每小时检查磁盘空间
0 * * * * df -h | awk '$5 > 80 {print "Disk space warning: " $0}' | mail -s "Disk Space Alert" <EMAIL>
```

### 错误日志监控
```bash
# 每10分钟检查错误日志
*/10 * * * * /usr/bin/php /path/to/ppmt_admin/scripts/check_errors.php
```

## 4. 完整的crontab配置示例

```bash
# 编辑crontab
crontab -e

# 添加以下内容：

# PPMT 系统维护任务
# 每天1点备份数据库
0 1 * * * /usr/bin/php /path/to/ppmt_admin/scripts/backup.php

# 每天2点清理日志
0 2 * * * /usr/bin/php /path/to/ppmt_admin/scripts/cleanup_logs.php smart

# 每周日3点深度清理
0 3 * * 0 /usr/bin/php /path/to/ppmt_admin/scripts/cleanup_logs.php smart

# 每小时检查磁盘空间
0 * * * * df -h /path/to/ppmt_admin | awk 'NR==2 && $5+0 > 80 {print "Disk space warning: " $0}' | mail -s "PPMT Disk Alert" <EMAIL>
```

## 5. 高频请求优化建议

针对您提到的每天新增一万多条日志的情况，建议：

### 5.1 调整客户端验证频率
```php
// 在客户端实现缓存机制，减少验证频率
// 例如：成功验证后缓存5-10分钟
$cache_duration = 300; // 5分钟
$last_verify = get_cache('last_verify_time');
if (time() - $last_verify < $cache_duration) {
    // 使用缓存结果，不发送验证请求
    return $cached_result;
}
```

### 5.2 批量验证
```php
// 客户端可以批量发送多个操作的验证请求
// 减少网络请求次数
$operations = ['op1', 'op2', 'op3'];
$result = batch_verify($license_key, $device_uid, $operations);
```

### 5.3 数据库优化
```sql
-- 创建分区表（按月分区）
ALTER TABLE logs PARTITION BY RANGE (YEAR(log_time) * 100 + MONTH(log_time)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    -- ... 更多分区
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 5.4 日志级别控制
```php
// 在config/app.php中添加日志级别控制
'logging' => [
    'log_successful_verify' => false,  // 不记录成功的验证日志
    'log_failed_only' => true,         // 只记录失败的操作
    'batch_log_interval' => 3600,      // 每小时汇总一次成功日志
],
```

## 6. 监控脚本示例

### 创建错误检查脚本
```php
<?php
// scripts/check_errors.php
require_once __DIR__ . '/../includes/db.php';

$stmt = $pdo->query("
    SELECT COUNT(*) as error_count 
    FROM logs 
    WHERE log_time >= DATE_SUB(NOW(), INTERVAL 10 MINUTE) 
    AND status != 'SUCCESS'
");
$error_count = $stmt->fetchColumn();

if ($error_count > 100) {
    mail('<EMAIL>', 'PPMT Error Alert', 
         "检测到异常错误数量: $error_count 个错误在过去10分钟内");
}
?>
```

## 7. 性能优化建议

### 7.1 数据库连接池
```php
// 使用持久连接减少连接开销
$pdo = new PDO($dsn, $username, $password, [
    PDO::ATTR_PERSISTENT => true,
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
]);
```

### 7.2 缓存机制
```php
// 使用Redis或Memcached缓存频繁查询的数据
$redis = new Redis();
$redis->connect('127.0.0.1', 6379);

// 缓存激活码信息
$cache_key = "license:$license_key";
$license_info = $redis->get($cache_key);
if (!$license_info) {
    $license_info = query_database($license_key);
    $redis->setex($cache_key, 300, serialize($license_info)); // 缓存5分钟
}
```

## 8. 日志轮转配置

### 使用logrotate管理日志文件
```bash
# /etc/logrotate.d/ppmt
/path/to/ppmt_admin/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        /usr/bin/php /path/to/ppmt_admin/scripts/cleanup_logs.php smart
    endscript
}
```

## 9. 执行步骤

1. **修改路径**：将上述脚本中的 `/path/to/ppmt_admin` 替换为实际路径
2. **设置权限**：确保脚本有执行权限
   ```bash
   chmod +x /path/to/ppmt_admin/scripts/*.php
   ```
3. **测试脚本**：手动执行一次确保正常工作
   ```bash
   /usr/bin/php /path/to/ppmt_admin/scripts/cleanup_logs.php stats
   ```
4. **添加crontab**：使用 `crontab -e` 添加定时任务
5. **监控执行**：检查 `/var/log/cron` 确保任务正常执行

## 10. 故障排除

### 检查cron日志
```bash
tail -f /var/log/cron
```

### 检查脚本输出
```bash
# 将输出重定向到日志文件
0 2 * * * /usr/bin/php /path/to/ppmt_admin/scripts/cleanup_logs.php >> /var/log/ppmt_cleanup.log 2>&1
```

### 测试邮件通知
```bash
echo "Test message" | mail -s "Test Subject" <EMAIL>
```

通过合理设置这些定时任务，可以有效控制数据库大小，保持系统性能，并及时发现潜在问题。
