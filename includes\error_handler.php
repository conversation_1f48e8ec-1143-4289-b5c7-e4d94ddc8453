<?php
/**
 * 统一错误处理器
 * 提供统一的错误记录和响应格式
 */

class ErrorHandler {
    private static $pdo = null;
    
    /**
     * 初始化错误处理器
     */
    public static function init($pdo) {
        self::$pdo = $pdo;
        self::createErrorLogTable();
        
        // 设置错误处理函数
        set_error_handler([self::class, 'handleError']);
        set_exception_handler([self::class, 'handleException']);
        register_shutdown_function([self::class, 'handleFatalError']);
    }
    
    /**
     * 创建错误日志表
     */
    private static function createErrorLogTable() {
        if (!self::$pdo) return;
        
        $sql = "CREATE TABLE IF NOT EXISTS `error_logs` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `level` varchar(20) NOT NULL,
            `message` text NOT NULL,
            `file` varchar(255) DEFAULT NULL,
            `line` int(11) DEFAULT NULL,
            `context` text DEFAULT NULL,
            `ip_address` varchar(45) DEFAULT NULL,
            `user_agent` varchar(500) DEFAULT NULL,
            `created_at` datetime NOT NULL,
            PRIMARY KEY (`id`),
            KEY `idx_level_created` (`level`, `created_at`),
            KEY `idx_created` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        
        try {
            self::$pdo->exec($sql);
        } catch (PDOException $e) {
            // 忽略表已存在的错误
        }
    }
    
    /**
     * 处理一般错误
     */
    public static function handleError($severity, $message, $file, $line) {
        if (!(error_reporting() & $severity)) {
            return false;
        }
        
        $level = self::getSeverityLevel($severity);
        self::logError($level, $message, $file, $line);
        
        return true;
    }
    
    /**
     * 处理异常
     */
    public static function handleException($exception) {
        self::logError(
            'EXCEPTION',
            $exception->getMessage(),
            $exception->getFile(),
            $exception->getLine(),
            [
                'trace' => $exception->getTraceAsString(),
                'code' => $exception->getCode()
            ]
        );
        
        // 如果是API请求，返回JSON错误
        if (self::isApiRequest()) {
            self::sendApiError('服务器内部错误', 500);
        } else {
            self::sendWebError('系统错误，请稍后重试');
        }
    }
    
    /**
     * 处理致命错误
     */
    public static function handleFatalError() {
        $error = error_get_last();
        if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
            self::logError(
                'FATAL',
                $error['message'],
                $error['file'],
                $error['line']
            );
        }
    }
    
    /**
     * 记录错误到数据库
     */
    public static function logError($level, $message, $file = null, $line = null, $context = []) {
        if (!self::$pdo) {
            // 如果数据库不可用，记录到文件
            error_log("[$level] $message in $file:$line");
            return;
        }
        
        try {
            $stmt = self::$pdo->prepare("
                INSERT INTO error_logs (level, message, file, line, context, ip_address, user_agent, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $level,
                $message,
                $file,
                $line,
                json_encode($context),
                $_SERVER['REMOTE_ADDR'] ?? null,
                $_SERVER['HTTP_USER_AGENT'] ?? null,
                date('Y-m-d H:i:s')
            ]);
        } catch (PDOException $e) {
            // 如果数据库记录失败，记录到文件
            error_log("[$level] $message in $file:$line");
            error_log("Database logging failed: " . $e->getMessage());
        }
    }
    
    /**
     * 获取错误级别名称
     */
    private static function getSeverityLevel($severity) {
        switch ($severity) {
            case E_ERROR:
            case E_USER_ERROR:
                return 'ERROR';
            case E_WARNING:
            case E_USER_WARNING:
                return 'WARNING';
            case E_NOTICE:
            case E_USER_NOTICE:
                return 'NOTICE';
            default:
                return 'UNKNOWN';
        }
    }
    
    /**
     * 检查是否为API请求
     */
    private static function isApiRequest() {
        return strpos($_SERVER['REQUEST_URI'] ?? '', '/api/') !== false ||
               strpos($_SERVER['HTTP_ACCEPT'] ?? '', 'application/json') !== false;
    }
    
    /**
     * 发送API错误响应
     */
    private static function sendApiError($message, $code = 500) {
        http_response_code($code);
        header('Content-Type: application/json');
        echo json_encode([
            'error' => true,
            'message' => $message,
            'code' => $code
        ]);
        exit();
    }
    
    /**
     * 发送Web错误页面
     */
    private static function sendWebError($message) {
        http_response_code(500);
        echo "<!DOCTYPE html>
        <html>
        <head>
            <title>系统错误</title>
            <meta charset='utf-8'>
            <style>
                body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                .error { color: #d32f2f; }
            </style>
        </head>
        <body>
            <h1 class='error'>系统错误</h1>
            <p>$message</p>
            <p><a href='javascript:history.back()'>返回上一页</a></p>
        </body>
        </html>";
        exit();
    }
}

/**
 * 便捷函数：记录应用错误
 */
function log_app_error($message, $context = []) {
    ErrorHandler::logError('APP_ERROR', $message, null, null, $context);
}

/**
 * 便捷函数：记录应用警告
 */
function log_app_warning($message, $context = []) {
    ErrorHandler::logError('APP_WARNING', $message, null, null, $context);
}
?>
