<?php
require_once 'includes/session_helper.php';
require_once 'includes/db.php';

echo "<h2>数据库调试信息</h2>";

try {
    // 检查数据库连接
    echo "<h3>数据库连接状态</h3>";
    echo "<p>PDO连接：" . ($pdo ? "成功" : "失败") . "</p>";
    
    // 检查license_devices表结构
    echo "<h3>license_devices表结构</h3>";
    $structure = $pdo->query("DESCRIBE license_devices")->fetchAll();
    echo "<table border='1'>";
    echo "<tr><th>字段</th><th>类型</th><th>空值</th><th>键</th><th>默认值</th><th>额外</th></tr>";
    foreach ($structure as $field) {
        echo "<tr>";
        echo "<td>" . $field['Field'] . "</td>";
        echo "<td>" . $field['Type'] . "</td>";
        echo "<td>" . $field['Null'] . "</td>";
        echo "<td>" . $field['Key'] . "</td>";
        echo "<td>" . $field['Default'] . "</td>";
        echo "<td>" . $field['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 查询设备ID 530的详细信息
    echo "<h3>设备ID 530的详细信息</h3>";
    $stmt = $pdo->prepare("SELECT * FROM license_devices WHERE id = ?");
    $stmt->execute([530]);
    $device = $stmt->fetch();
    
    if ($device) {
        echo "<table border='1'>";
        foreach ($device as $key => $value) {
            echo "<tr><td><strong>$key</strong></td><td>$value</td></tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>设备ID 530 不存在！</p>";
    }
    
    // 查询设备ID 529的详细信息
    echo "<h3>设备ID 529的详细信息</h3>";
    $stmt = $pdo->prepare("SELECT * FROM license_devices WHERE id = ?");
    $stmt->execute([529]);
    $device = $stmt->fetch();
    
    if ($device) {
        echo "<table border='1'>";
        foreach ($device as $key => $value) {
            echo "<tr><td><strong>$key</strong></td><td>$value</td></tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>设备ID 529 不存在！</p>";
    }
    
    // 查询激活码775的信息
    echo "<h3>激活码ID 775的信息</h3>";
    $stmt = $pdo->prepare("SELECT * FROM licenses WHERE id = ?");
    $stmt->execute([775]);
    $license = $stmt->fetch();
    
    if ($license) {
        echo "<table border='1'>";
        foreach ($license as $key => $value) {
            echo "<tr><td><strong>$key</strong></td><td>$value</td></tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>激活码ID 775 不存在！</p>";
    }
    
    // 测试删除操作（不实际执行）
    echo "<h3>测试SQL语句</h3>";
    echo "<p><strong>删除语句：</strong>DELETE FROM license_devices WHERE id = 530</p>";
    echo "<p><strong>更新语句：</strong>UPDATE licenses SET bound_devices = bound_devices - 1, unbind_count = unbind_count + 1 WHERE id = 775</p>";
    
    // 检查会话状态
    echo "<h3>会话状态</h3>";
    echo "<p>登录状态：" . (isset($_SESSION['loggedin']) && $_SESSION['loggedin'] === true ? "已登录" : "未登录") . "</p>";
    echo "<p>Session ID：" . session_id() . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>错误：" . $e->getMessage() . "</p>";
}
?>

<style>
table { border-collapse: collapse; margin: 10px 0; }
th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
th { background-color: #f2f2f2; }
</style>
