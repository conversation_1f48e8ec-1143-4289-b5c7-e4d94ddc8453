-- 性能优化索引
-- 执行前请备份数据库

-- 1. 激活码表索引优化
CREATE INDEX IF NOT EXISTS idx_licenses_status_plan ON licenses(status, plan_id);
CREATE INDEX IF NOT EXISTS idx_licenses_expires ON licenses(expires_at);
CREATE INDEX IF NOT EXISTS idx_licenses_created ON licenses(created_at);
CREATE INDEX IF NOT EXISTS idx_licenses_key_status ON licenses(license_key, status);

-- 2. 设备绑定表索引优化  
CREATE INDEX IF NOT EXISTS idx_license_devices_license_status ON license_devices(license_id, status);
CREATE INDEX IF NOT EXISTS idx_license_devices_uid_status ON license_devices(device_uid, status);
CREATE INDEX IF NOT EXISTS idx_license_devices_activated ON license_devices(activated_at);
CREATE INDEX IF NOT EXISTS idx_license_devices_verify ON license_devices(last_verify_at);

-- 3. 日志表索引优化
CREATE INDEX IF NOT EXISTS idx_logs_time_type ON logs(log_time, log_type);
CREATE INDEX IF NOT EXISTS idx_logs_license_device ON logs(license_key, device_uid);
CREATE INDEX IF NOT EXISTS idx_logs_ip_time ON logs(ip_address, log_time);
CREATE INDEX IF NOT EXISTS idx_logs_status_time ON logs(status, log_time);

-- 4. 套餐表索引优化
CREATE INDEX IF NOT EXISTS idx_plans_active ON plans(is_active);

-- 5. Skeys记录表索引优化（如果已创建）
CREATE INDEX IF NOT EXISTS idx_skeys_license_device ON skeys_records(license_key, device_uid);
CREATE INDEX IF NOT EXISTS idx_skeys_generated ON skeys_records(generated_at);
CREATE INDEX IF NOT EXISTS idx_skeys_expires ON skeys_records(expires_at);

-- 查看索引创建结果
SHOW INDEX FROM licenses;
SHOW INDEX FROM license_devices;
SHOW INDEX FROM logs;
SHOW INDEX FROM plans;
