<?php
/**
 * 日志清理脚本
 * 用于清理过期的日志记录，减少数据库大小
 * 建议通过cron定时执行：0 2 * * * /usr/bin/php /path/to/cleanup_logs.php
 */

require_once __DIR__ . '/../includes/db.php';
require_once __DIR__ . '/../includes/config.php';

class LogCleaner {
    private $pdo;
    private $config;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->config = Config::load();
    }
    
    /**
     * 自定义清理
     */
    public function customCleanup($days, $log_status = 'all') {
        echo "开始自定义清理日志...\n";

        $cutoff_date = date('Y-m-d H:i:s', strtotime("-{$days} days"));

        // 构建WHERE条件
        $where_conditions = ["log_time < ?"];
        $params = [$cutoff_date];

        if ($log_status === 'success') {
            $where_conditions[] = "status = 'SUCCESS'";
        } elseif ($log_status === 'failed') {
            $where_conditions[] = "status != 'SUCCESS'";
        }
        // 'all' 不添加额外条件

        $where_clause = implode(' AND ', $where_conditions);

        try {
            $stmt = $this->pdo->prepare("DELETE FROM logs WHERE {$where_clause}");
            $stmt->execute($params);
            $cleaned = $stmt->rowCount();

            echo "自定义清理完成，清理了 {$cleaned} 条记录\n";
            return $cleaned;
        } catch (Exception $e) {
            echo "清理失败: " . $e->getMessage() . "\n";
            return 0;
        }
    }

    /**
     * 执行日志清理
     */
    public function cleanup() {
        echo "开始清理日志...\n";

        $total_cleaned = 0;

        // 1. 清理操作日志
        $logs_cleaned = $this->cleanupOperationLogs();
        $total_cleaned += $logs_cleaned;
        echo "清理操作日志: $logs_cleaned 条\n";
        
        // 2. 清理Skeys记录
        $skeys_cleaned = $this->cleanupSkeysRecords();
        $total_cleaned += $skeys_cleaned;
        echo "清理Skeys记录: $skeys_cleaned 条\n";
        
        // 3. 清理API频率限制记录
        $rate_limits_cleaned = $this->cleanupRateLimits();
        $total_cleaned += $rate_limits_cleaned;
        echo "清理频率限制记录: $rate_limits_cleaned 条\n";
        
        // 4. 清理错误日志
        $error_logs_cleaned = $this->cleanupErrorLogs();
        $total_cleaned += $error_logs_cleaned;
        echo "清理错误日志: $error_logs_cleaned 条\n";
        
        // 5. 优化表
        $this->optimizeTables();
        
        echo "清理完成，总计清理 $total_cleaned 条记录\n";
        
        return $total_cleaned;
    }
    
    /**
     * 清理操作日志
     */
    private function cleanupOperationLogs() {
        $keep_days = config('database.cleanup_logs_days', 90);
        
        // 保留最近的成功记录和所有失败记录（失败记录用于安全分析）
        $sql = "DELETE FROM logs 
                WHERE log_time < DATE_SUB(NOW(), INTERVAL ? DAY) 
                AND status = 'SUCCESS'";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$keep_days]);
        
        return $stmt->rowCount();
    }
    
    /**
     * 清理过期的Skeys记录
     */
    private function cleanupSkeysRecords() {
        // 删除已过期的Skeys记录
        $sql = "DELETE FROM skeys_records 
                WHERE expires_at < NOW()";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute();
        
        return $stmt->rowCount();
    }
    
    /**
     * 清理API频率限制记录
     */
    private function cleanupRateLimits() {
        $keep_hours = config('database.cleanup_rate_limits_hours', 24);
        
        $sql = "DELETE FROM api_rate_limits 
                WHERE window_start < DATE_SUB(NOW(), INTERVAL ? HOUR)";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$keep_hours]);
        
        return $stmt->rowCount();
    }
    
    /**
     * 清理错误日志
     */
    private function cleanupErrorLogs() {
        $keep_days = 30; // 错误日志保留30天
        
        $sql = "DELETE FROM error_logs 
                WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$keep_days]);
        
        return $stmt->rowCount();
    }
    
    /**
     * 优化表结构
     */
    private function optimizeTables() {
        echo "优化表结构...\n";
        
        $tables = ['logs', 'skeys_records', 'api_rate_limits', 'error_logs'];
        
        foreach ($tables as $table) {
            try {
                $this->pdo->exec("OPTIMIZE TABLE `$table`");
                echo "优化表 $table 完成\n";
            } catch (Exception $e) {
                echo "优化表 $table 失败: " . $e->getMessage() . "\n";
            }
        }
    }
    
    /**
     * 获取表统计信息
     */
    public function getTableStats() {
        $stats = [];
        
        $tables = [
            'logs' => '操作日志',
            'skeys_records' => 'Skeys记录',
            'api_rate_limits' => 'API频率限制',
            'error_logs' => '错误日志'
        ];
        
        foreach ($tables as $table => $name) {
            try {
                $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM `$table`");
                $count = $stmt->fetchColumn();
                
                $stmt = $this->pdo->query("
                    SELECT ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
                    FROM information_schema.TABLES 
                    WHERE table_schema = DATABASE() AND table_name = '$table'
                ");
                $size = $stmt->fetchColumn();
                
                $stats[$table] = [
                    'name' => $name,
                    'count' => $count,
                    'size_mb' => $size
                ];
            } catch (Exception $e) {
                $stats[$table] = [
                    'name' => $name,
                    'count' => 0,
                    'size_mb' => 0,
                    'error' => $e->getMessage()
                ];
            }
        }
        
        return $stats;
    }
    
    /**
     * 智能清理 - 根据数据量自动调整清理策略
     */
    public function smartCleanup() {
        echo "执行智能清理...\n";
        
        $stats = $this->getTableStats();
        
        // 如果操作日志超过100万条，加强清理
        if ($stats['logs']['count'] > 1000000) {
            echo "检测到大量日志记录，执行加强清理...\n";
            
            // 只保留最近30天的成功记录
            $sql = "DELETE FROM logs 
                    WHERE log_time < DATE_SUB(NOW(), INTERVAL 30 DAY) 
                    AND status = 'SUCCESS'";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute();
            echo "加强清理操作日志: " . $stmt->rowCount() . " 条\n";
        }
        
        // 如果错误日志超过10万条，清理更多
        if ($stats['error_logs']['count'] > 100000) {
            echo "检测到大量错误日志，执行加强清理...\n";
            
            $sql = "DELETE FROM error_logs 
                    WHERE created_at < DATE_SUB(NOW(), INTERVAL 7 DAY)";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute();
            echo "加强清理错误日志: " . $stmt->rowCount() . " 条\n";
        }
        
        return $this->cleanup();
    }
    
    /**
     * 显示清理建议
     */
    public function showCleanupSuggestions() {
        $stats = $this->getTableStats();
        
        echo "\n=== 数据库清理建议 ===\n";
        
        foreach ($stats as $table => $info) {
            echo sprintf("%-20s: %s条记录, %.2fMB\n", 
                $info['name'], 
                number_format($info['count']), 
                $info['size_mb']
            );
            
            // 给出建议
            if ($table === 'logs' && $info['count'] > 500000) {
                echo "  建议: 操作日志过多，建议执行清理\n";
            }
            if ($table === 'error_logs' && $info['count'] > 50000) {
                echo "  建议: 错误日志过多，建议检查系统问题\n";
            }
        }
        
        $total_size = array_sum(array_column($stats, 'size_mb'));
        echo sprintf("\n总大小: %.2fMB\n", $total_size);
        
        if ($total_size > 100) {
            echo "建议: 数据库较大，建议定期清理\n";
        }
    }
}

// 如果直接运行此脚本
if (php_sapi_name() === 'cli' && basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $cleaner = new LogCleaner($pdo);
    
    // 检查命令行参数
    $action = $argv[1] ?? 'normal';
    
    switch ($action) {
        case 'smart':
            $cleaner->smartCleanup();
            break;
        case 'stats':
            $cleaner->showCleanupSuggestions();
            break;
        case 'normal':
        default:
            $cleaner->cleanup();
            break;
    }
    
    echo "\n清理完成！\n";
}
?>
