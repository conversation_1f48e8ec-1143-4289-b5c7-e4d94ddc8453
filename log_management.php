<?php
require_once 'includes/check_login.php';
require_once 'includes/db.php';
require_once 'includes/config.php';
require_once 'scripts/cleanup_logs.php';
require_once 'includes/header.php';
require_once 'includes/sidebar.php';

$cleaner = new LogCleaner($pdo);

// 处理清理请求
if ($_POST['action'] ?? '' === 'cleanup') {
    $cleanup_type = $_POST['cleanup_type'] ?? 'normal';
    $days = (int)($_POST['days'] ?? 90);
    $log_status = $_POST['log_status'] ?? 'all'; // 'success', 'failed', 'all'

    if ($cleanup_type === 'smart') {
        $cleaned = $cleaner->smartCleanup();
    } elseif ($cleanup_type === 'custom') {
        $cleaned = $cleaner->customCleanup($days, $log_status);
    } else {
        $cleaned = $cleaner->cleanup();
    }

    $success_message = "清理完成，共清理了 $cleaned 条记录";
}

// 获取统计信息
$stats = $cleaner->getTableStats();

// 获取日志增长趋势
$growth_stats = [];
try {
    $stmt = $pdo->query("
        SELECT 
            DATE(log_time) as date,
            COUNT(*) as daily_count
        FROM logs 
        WHERE log_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        GROUP BY DATE(log_time)
        ORDER BY date DESC
    ");
    $growth_stats = $stmt->fetchAll();
} catch (Exception $e) {
    $growth_stats = [];
}
?>

<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <h1 class="m-0">日志管理</h1>
        </div>
    </section>
    
    <section class="content">
        <div class="container-fluid">
            
            <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible">
                <button type="button" class="close" data-dismiss="alert">&times;</button>
                <?= $success_message ?>
            </div>
            <?php endif; ?>
            
            <!-- 统计概览 -->
            <div class="row">
                <?php foreach ($stats as $table => $info): ?>
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-<?= $info['count'] > 100000 ? 'warning' : 'info' ?>">
                            <i class="fas fa-<?= $table === 'logs' ? 'list' : ($table === 'error_logs' ? 'exclamation-triangle' : 'database') ?>"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text"><?= $info['name'] ?></span>
                            <span class="info-box-number"><?= number_format($info['count']) ?></span>
                            <span class="info-box-more"><?= $info['size_mb'] ?>MB</span>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            
            <!-- 清理操作 -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">日志清理</h3>
                        </div>
                        <div class="card-body">
                            <form method="post" onsubmit="return confirm('确定要执行清理操作吗？此操作不可恢复！')">
                                <input type="hidden" name="action" value="cleanup">
                                
                                <div class="form-group">
                                    <label>清理模式：</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="cleanup_type" value="normal" checked>
                                        <label class="form-check-label">
                                            常规清理
                                            <small class="text-muted d-block">清理90天前的成功日志，保留失败日志</small>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="cleanup_type" value="smart">
                                        <label class="form-check-label">
                                            智能清理
                                            <small class="text-muted d-block">根据数据量自动调整清理策略</small>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="cleanup_type" value="custom">
                                        <label class="form-check-label">
                                            自定义清理
                                            <small class="text-muted d-block">自定义清理天数和日志类型</small>
                                        </label>
                                    </div>
                                </div>

                                <!-- 自定义清理选项 -->
                                <div id="customOptions" style="display: none;" class="mt-3 p-3 border rounded">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label>清理天数:</label>
                                            <input type="number" name="days" value="90" min="1" max="365" class="form-control">
                                            <small class="text-muted">清理N天前的日志</small>
                                        </div>
                                        <div class="col-md-6">
                                            <label>日志类型:</label>
                                            <select name="log_status" class="form-control">
                                                <option value="all">所有日志</option>
                                                <option value="success">仅成功日志</option>
                                                <option value="failed">仅失败日志</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <strong>注意：</strong>清理操作将永久删除数据，请确保已做好备份！
                                </div>
                                
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-broom"></i> 执行清理
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- 清理建议 -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">清理建议</h3>
                        </div>
                        <div class="card-body">
                            <?php
                            $total_size = array_sum(array_column($stats, 'size_mb'));
                            $total_count = array_sum(array_column($stats, 'count'));
                            ?>
                            
                            <p><strong>数据库总大小：</strong><?= number_format($total_size, 2) ?>MB</p>
                            <p><strong>总记录数：</strong><?= number_format($total_count) ?>条</p>
                            
                            <div class="mt-3">
                                <?php if ($stats['logs']['count'] > 1000000): ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-circle"></i>
                                    操作日志过多（<?= number_format($stats['logs']['count']) ?>条），强烈建议执行智能清理
                                </div>
                                <?php elseif ($stats['logs']['count'] > 500000): ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    操作日志较多（<?= number_format($stats['logs']['count']) ?>条），建议执行常规清理
                                </div>
                                <?php else: ?>
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle"></i>
                                    日志数量正常，暂不需要清理
                                </div>
                                <?php endif; ?>
                                
                                <?php if ($stats['error_logs']['count'] > 10000): ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-bug"></i>
                                    错误日志过多，建议检查系统问题
                                </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="mt-3">
                                <h6>自动清理设置：</h6>
                                <p class="text-muted">
                                    建议设置定时任务每天凌晨2点执行清理：<br>
                                    <code>0 2 * * * /usr/bin/php <?= __DIR__ ?>/scripts/cleanup_logs.php</code>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 日志增长趋势 -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">最近7天日志增长趋势</h3>
                        </div>
                        <div class="card-body">
                            <?php if (empty($growth_stats)): ?>
                                <p class="text-muted">暂无数据</p>
                            <?php else: ?>
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>日期</th>
                                            <th>新增日志</th>
                                            <th>趋势</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php 
                                        $prev_count = 0;
                                        foreach ($growth_stats as $day): 
                                            $trend = '';
                                            $trend_class = '';
                                            if ($prev_count > 0) {
                                                $change = $day['daily_count'] - $prev_count;
                                                if ($change > 0) {
                                                    $trend = '+' . number_format($change);
                                                    $trend_class = 'text-danger';
                                                } elseif ($change < 0) {
                                                    $trend = number_format($change);
                                                    $trend_class = 'text-success';
                                                } else {
                                                    $trend = '0';
                                                    $trend_class = 'text-muted';
                                                }
                                            }
                                            $prev_count = $day['daily_count'];
                                        ?>
                                        <tr>
                                            <td><?= $day['date'] ?></td>
                                            <td><?= number_format($day['daily_count']) ?></td>
                                            <td class="<?= $trend_class ?>"><?= $trend ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                                
                                <?php
                                $avg_daily = array_sum(array_column($growth_stats, 'daily_count')) / count($growth_stats);
                                $projected_monthly = $avg_daily * 30;
                                ?>
                                
                                <div class="mt-3">
                                    <p><strong>平均每日新增：</strong><?= number_format($avg_daily) ?>条</p>
                                    <p><strong>预计月增长：</strong><?= number_format($projected_monthly) ?>条</p>
                                    
                                    <?php if ($projected_monthly > 300000): ?>
                                    <div class="alert alert-warning">
                                        <i class="fas fa-chart-line"></i>
                                        按当前增长速度，建议每周执行一次清理
                                    </div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
        </div>
    </section>
</div>

<script>
// 控制自定义清理选项的显示
document.addEventListener('DOMContentLoaded', function() {
    const radios = document.querySelectorAll('input[name="cleanup_type"]');
    const customOptions = document.getElementById('customOptions');

    radios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'custom') {
                customOptions.style.display = 'block';
            } else {
                customOptions.style.display = 'none';
            }
        });
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
