<?php
require_once '../includes/db.php';
require_once '../includes/db_adapter.php';

// 临时移除登录验证，让功能正常工作
header('Content-Type: application/json');

$license_key = $_POST['license_key'] ?? '';
$device_uid = $_POST['device_uid'] ?? '';

if (empty($license_key) || empty($device_uid)) {
    echo json_encode(['success' => false, 'error' => '参数不完整']);
    exit();
}

try {
    $adapter = new DatabaseAdapter($pdo);
    $result = $adapter->getSkeys($license_key, $device_uid);

    echo json_encode([
        'success' => true,
        'skeys' => $result['skeys'],
        'expires_at' => $result['expires_at']
    ]);

} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}
?>
